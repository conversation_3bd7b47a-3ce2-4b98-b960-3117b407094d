DROP TABLE IF EXISTS `inc_gara_welfare_2025_static`;

CREATE TABLE `inc_gara_welfare_2025_static` (
  `agenzia_id` char(4) NOT NULL,
  `active` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_gara_welfare_2025_static`
    ADD PRIMARY KEY (`agenzia_id`);

ALTER TABLE `inc_gara_welfare_2025_static`
    ADD CONSTRAINT `inc_gara_welfare_2025_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

INSERT INTO `inc_gara_welfare_2025_static` (`agenzia_id`, `active`) VALUES
    ('G030', 1), ('G031', 1), ('G045', 1), ('G051', 1), ('G059', 1), ('G065', 1), ('G067', 1), ('G075', 1), ('G081', 1), ('G101', 1), ('G114', 1), ('G116', 1), ('G117', 1), ('G119', 1), ('G122', 1), ('G126', 1), ('G143', 1), ('G146', 1), ('G159', 1), ('G162', 1), ('G163', 1), ('G174', 1), ('G187', 1), ('G190', 1), ('G193', 1), ('G203', 1), ('G211', 1), ('G243', 1), ('G244', 1), ('G249', 1), ('G254', 1), ('G256', 1), ('G260', 1), ('G269', 1), ('G277', 1), ('G295', 1), ('G296', 1), ('G306', 1), ('G371', 1), ('G375', 1), ('G411', 1), ('G428', 1), ('G431', 1), ('G435', 1), ('G451', 1), ('G454', 1), ('G471', 1), ('G476', 1), ('G482', 1), ('G484', 1), ('G486', 1), ('G542', 1), ('G766', 1), ('G816', 1), ('G837', 1), ('G848', 1), ('G850', 1), ('G857', 1), ('G864', 1), ('G875', 1), ('G902', 1), ('G943', 1), ('G980', 1), ('G981', 1), ('G983', 1), ('G993', 1), ('GL23', 1), ('GL60', 1), ('GL63', 1), ('GL85', 1), ('GM12', 1), ('GM15', 1), ('GM19', 1), ('GM21', 1), ('GM23', 1), ('GM28', 1), ('GM40', 1), ('GM43', 1), ('GM51', 1), ('GM53', 1), ('GM63', 1), ('GM18', 1), ('GM35', 1), ('N033', 1), ('N036', 1), ('N048', 1), ('N050', 1), ('N079', 1), ('N086', 1), ('N144', 1), ('N173', 1), ('N180', 1), ('N268', 1), ('N312', 1), ('N335', 1), ('N392', 1), ('N465', 1), ('N479', 1), ('N569', 1), ('N852', 1), ('N863', 1), ('N959', 1), ('N984', 1), ('N992', 1), ('NB36', 1), ('NC60', 1), ('NC70', 1), ('ND16', 1), ('ND66', 1), ('ND70', 1), ('NF22', 1), ('NF54', 1);

DROP TABLE IF EXISTS `inc_gara_welfare_2025_data`;
CREATE TABLE `inc_gara_welfare_2025_data` (
  `agenzia_id` char(4) NOT NULL,
  `contraente` varchar(64) NOT NULL,
  `polizza` varchar(20) NOT NULL,
  `polizzaMadre` varchar(20) NOT NULL,
  `codiceProdotto` varchar(6) NOT NULL,
  `premio` DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_gara_welfare_2025_data`
    ADD PRIMARY KEY (`agenzia_id`, `polizza`);

ALTER TABLE `inc_gara_welfare_2025_data`
    ADD CONSTRAINT `inc_gara_welfare_2025_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP VIEW IF EXISTS `vw_inc_gara_welfare_2025_data`;
CREATE VIEW `vw_inc_gara_welfare_2025_data` AS
SELECT
    pc.*,
    CASE
        WHEN d.agenzia_id IS NOT NULL THEN 'SI'
        ELSE 'NO'
        END AS `validaIncent`
FROM `pc_data` pc
         LEFT JOIN `inc_gara_welfare_2025_data` d ON
    d.agenzia_id = pc.agenzia_id AND
    d.polizza = pc.polizza AND
    d.codiceProdotto = pc.codiceProdotto
WHERE pc.codiceProdotto = '000520'
  AND dataContabileEmissione BETWEEN '2025-07-01' AND '2025-12-31';

DROP TABLE IF EXISTS `inc_gara_welfare_2025_status`;
CREATE TABLE `inc_gara_welfare_2025_status` (
  `agenzia_id` char(4) NOT NULL,
  `pezzi` smallint UNSIGNED NOT NULL,
  `premiTot` DECIMAL(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `inc_gara_welfare_2025_status`
    ADD PRIMARY KEY (`agenzia_id`);

ALTER TABLE `inc_gara_welfare_2025_status`
    ADD CONSTRAINT `inc_gara_welfare_2025_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

DROP VIEW IF EXISTS `vw_inc_gara_welfare_2025_status`;
CREATE VIEW `vw_inc_gara_welfare_2025_status` AS
SELECT
    s.agenzia_id, a.localita, a.nome, a.area, ga.nome as areaName, a.district, gd.nome as districtName,
    s.pezzi, s.premiTot
FROM `inc_gara_welfare_2025_status` s
join agenzie a on a.id = s.agenzia_id
join geo_aree ga on a.area = ga.id
join geo_districts gd on a.district = gd.id;
