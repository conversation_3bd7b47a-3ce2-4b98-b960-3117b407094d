/*
    14/6/2023 Si<PERSON>me spesso mi arrivano richieste di estrazioni random e non si riesce mai a capire se è roba una tantum o roba ricorrente ho deciso di segnarmele tutte a prescindere
*/

/* Richiesta di Martella del 14/6/2023 alle 12:47: "mi serve estrazione di tutti agenti attivi che sono a posto con corso Antiriciclaggio" */

/* Antiriciclaggio Groupama */
SELECT u.agenzia_id as 'ID Agenzia', au.login as 'Login', au.login1 as 'Login1', au.login2 as 'Login2', u.nome as 'Nome', u.cognome as 'Cognome',  u.type as 'Tipo', c.title as '<PERSON>lo corso', c.year as 'Anno corso', a.score as 'Punteggio', a.completedAt as 'Timestamp completamento'
FROM tra_attendance a
         JOIN users u ON a.user_id = u.id
         JOIN users_auth au ON au.id = u.id
         JOIN tra_course c ON a.course_id = c.id
WHERE c.antiriciclaggio = 1 and u.active = 1 and a.result = 'ok';

/* Antiriciclaggio Esterno */
SELECT u.agenzia_id as 'ID Agenzia', au.login as 'Login', au.login1 as 'Login1', au.login2 as 'Login2', u.nome as 'Nome', u.cognome as 'Cognome',  u.type as 'Tipo', ex.courseTitle as 'Titolo corso', ex.year as 'Anno corso', ex.date as 'Data corso'
FROM tra_external_credits ex
    JOIN users u ON u.id = ex.user_id
    JOIN users_auth au ON au.id = u.id
WHERE ex.antiriciclaggio = 1 and u.active = 1;



/* ===== ANIA - STATISTICA INDICI AZIENDALI 2022 ===== */

/* in aula direzione */
SELECT COUNT(*) from tra_attendance a
    JOIN tra_course tc on a.course_id = tc.id
        WHERE tc.year = 2022 AND tc.groupamaType = 'direz' AND tc.modalita = 'AULA' AND a.state = 'signedup'; /* 632 */

SELECT COUNT(*), SUM(a.credits) from tra_attendance a
    JOIN tra_course tc on a.course_id = tc.id
        WHERE tc.year = 2022 AND tc.groupamaType = 'direz' AND tc.modalita = 'AULA' AND a.state = 'signedup' AND a.result = 'ok'; /* 624 */

SELECT COUNT(*) from tra_attendance a
    JOIN tra_course tc on a.course_id = tc.id
        WHERE tc.year = 2022 AND tc.groupamaType = 'direz' AND tc.modalita = 'AULA' AND a.state = 'signedup' AND a.result IN('ko', 'none'); /* 5 */

/* area */
SELECT COUNT(*) from tra_attendance a
    INNER JOIN tra_course tc on a.course_id = tc.id
        WHERE tc.year = 2022 AND tc.groupamaType = 'area';

SELECT COUNT(*), SUM(a.credits) from tra_attendance a
    INNER JOIN tra_course tc on a.course_id = tc.id
WHERE tc.year = 2022 AND tc.groupamaType = 'area' AND a.result = 'ok';

SELECT COUNT(*) from tra_attendance a
    INNER JOIN tra_course tc on a.course_id = tc.id
WHERE tc.year = 2022 AND tc.groupamaType = 'area' AND a.result = 'ko';



/* ===== RICHIESTA MADONNA: elenco delle agenzie (in riga) con numero di agenti e numero collaboratori (in colonna). ovviamente solo gli attivi ===== */
SELECT a.id as 'Agenzia',
       SUM(IF( u.type = 'AGENTE' AND u.active = 1, 1, 0)) as 'Agenti',
       SUM(IF( u.type = 'INTERMEDIARIO' AND u.active = 1 AND u.ruolo = 'COLL_AGZ', 1, 0)) as 'Coll. Agenzia',
       SUM(IF( u.type = 'INTERMEDIARIO' AND u.active = 1 AND u.ruolo = 'COLL_SUBAGZ', 1, 0)) as 'Coll. SubAgenzia',
       SUM(IF( u.type = 'INTERMEDIARIO' AND u.active = 1 AND u.ruolo = 'DIP_AGZ', 1, 0)) as 'Dip. Agenzia',
       SUM(IF( u.type = 'INTERMEDIARIO' AND u.active = 1 AND u.ruolo = 'DIP_SUBAGZ', 1, 0)) as 'Dip. SubAgenzia',
       SUM(IF( u.type = 'INTERMEDIARIO' AND u.active = 1 AND u.ruolo = 'NEO', 1, 0)) as 'NEO Intermediario',
       SUM(IF( u.type = 'INTERMEDIARIO' AND u.active = 1 AND u.ruolo = 'SUBAGZ', 1, 0)) as 'Titolare SubAgenzia'
FROM users u
    JOIN agenzie a ON u.agenzia_id = a.id
WHERE a.status IN('ON', 'DIREZ', 'INTER', 'NEW', 'RIORG')
GROUP BY a.id;


/* ===== RICHIESTA CATINELLA (tramite Martella): mi servirebbe elenco utenti che nel 2023 hanno frequentato corso in aula DINMICA PLUS COMMERCIO (direzione e aree), con indicazione esito ===== */
/* ID corsi dinamica plus commercio: 13400, 13415, 13427 */
SELECT u.agenzia_id as 'ID Agenzia', u.nome as 'Nome', u.cognome as 'Cognome',  u.type as 'Tipo Utente',
    CASE
       WHEN tc.groupamaType = 'direz' THEN "DIREZ"
       WHEN tc.groupamaType = 'area' THEN "AREA"
       ELSE "-"
    END as 'Tipo corso',
    CASE
       WHEN tc.modalita = 'AULA' THEN "AULA"
       WHEN tc.modalita = 'DIST' THEN "DISTANZA"
       ELSE "-"
    END as 'Modalità',
    tc.title as 'Titolo corso', tc.year as 'Anno corso',
    CASE
       WHEN a.result = 'ok' THEN "POS"
       WHEN a.result = 'ko' THEN "NEG"
       ELSE "-"
    END as 'Esito',
    a.credits as 'Crediti'
FROM tra_attendance a
    JOIN tra_course tc on a.course_id = tc.id
    JOIN users u ON a.user_id = u.id
WHERE tc.year = 2023 AND tc.id IN(13400, 13415, 13427) and a.state = 'signedup';

/* Richiesta Martella: tutti gli AGENTI e INTERMEDIARI che hanno fatto elearning nel 2023 */
SELECT `u`.`id`           AS `id`,
       `u`.`nome`         AS `nome`,
       `u`.`cognome`      AS `cognome`,
       `u`.`type`         AS `tipo`,
       `u`.`agenzia_id`   AS `agenzia`,
       `u`.`email`        AS `email agente`,
       `u`.`cellulare`    AS `telefono agente`,
       `ag`.`email`       AS `email agenzia`,
       `ag`.`telefono`    AS `telefono agenzia`,
       `ga`.`nome`        AS `area`,
       `gd`.`nome`        AS `district`,
       SUM(`a`.`credits`) AS `crediti`
FROM (
         (
             `portaleagendo`.`tra_attendance` `a`
                 JOIN `portaleagendo`.`users` `u`
                 ON
                     ((`a`.`user_id` = `u`.`id`))
             )
             JOIN `portaleagendo`.`tra_course` `c`
             ON
                 ((`c`.`id` = `a`.`course_id`))
             JOIN `portaleagendo`.`agenzie` `ag`
             ON
                 ((`ag`.`id` = `u`.`agenzia_id`))
             JOIN `portaleagendo`.`geo_aree` `ga`
             ON
                 ((`ga`.`id` = `u`.`area`))
             JOIN `portaleagendo`.`geo_districts` `gd`
         ON
             ((`gd`.`id` = `u`.`district`))
         )
WHERE (
              (`c`.`year` = 2023) AND (`u`.`active` = 1) AND
              (`a`.`result` = 'ok') AND (`c`.`groupamaType` = 'e-learning')
          )
group by u.id;

/* Richiesta Martella: sapere quanti controlli sono stati effettuati nell'anno corrente sui corsi di Area */
SELECT COUNT(*) FROM `tra_attendance` ta JOIN tra_course tc ON ta.course_id = tc.id WHERE tc.year = 2023 AND tc.groupamaType = 'area' AND ta.flagAreaStrumenti = 1;
/* Richiesta Martella: a seguire voleva l'elenco dei corsi corrispondenti alle partecipazioni della query precedente */
SELECT tc.* FROM `tra_attendance` ta JOIN tra_course tc ON ta.course_id = tc.id WHERE tc.year = 2023 AND tc.groupamaType = 'area' AND ta.flagAreaStrumenti = 1 AND ta.flagAreaDidattica = 1 AND ta.role = 'NEO' GROUP BY tc.id;
/* Richiesta Martella: a seguire ancora voleva l'elenco degli utenti in questione */
SELECT u.nome, u.cognome, u.agenzia_id, tc.title FROM `tra_attendance` ta JOIN tra_course tc ON ta.course_id = tc.id JOIN users u ON u.id = ta.user_id WHERE tc.year = 2023 AND tc.groupamaType = 'area' AND ta.flagAreaStrumenti = 1 AND ta.role = 'NEO'

/* Richiesta Martella: "mi servirebbe estrazione degli agenti che hanno partecipato ai corsi di Benessere in salute.
ci sono sia corsi di area che direzione (anche uno solo per la sardegna) che e-learning" */
SELECT u.agenzia_id as 'ID Agenzia', u.nome as 'Nome', u.cognome as 'Cognome',  u.type as 'Tipo Utente',
       CASE
           WHEN tc.groupamaType = 'direz' THEN "DIREZ"
           WHEN tc.groupamaType = 'area' THEN "AREA"
           WHEN tc.groupamaType = 'e-learning' THEN "E-LEARNING"
           ELSE "-"
           END as 'Tipo corso',
       CASE
           WHEN tc.modalita = 'AULA' THEN "AULA"
           WHEN tc.modalita = 'DIST' THEN "DISTANZA"
           ELSE "-"
           END as 'Modalità',
       tc.title as 'Titolo corso', tc.year as 'Anno corso',
       tc.year,
       CASE
           WHEN a.result = 'ok' THEN "POSITIVO"
           WHEN a.result = 'ko' THEN "NEGATIVO"
           ELSE "-"
           END as 'Esito',
       a.credits as 'Crediti'
FROM tra_attendance a
         JOIN tra_course tc on a.course_id = tc.id
         JOIN users u ON a.user_id = u.id
WHERE tc.id IN(13835, 13868, 13886, 14190, 14301) and a.state = 'signedup';

/* Richiesta Botta/Bodi: tutti i cancellati di un corso (casa senza confini) */
SELECT tl.city, tl.name, tc.firstDay, agenzia_id, nome, cognome, email from users u
    JOIN tra_attendance ta ON ta.user_id = u.id
    JOIN tra_class tc ON ta.class_id = tc.id
    JOIN tra_location tl ON tc.location_id = tl.id
        WHERE tc.course_id = 13835 AND ta.state = 'deleted';

/* Richiesta Martella: tutte le partecipazioni a corsi fatti sul territorio nel 2023  */
SELECT COUNT(*) from tra_attendance a JOIN tra_course c ON a.course_id = c.id WHERE c.modalita = 'AULA' and c.year = 2023 and a.state = 'signedup';
/* Sempre stessa richiesta di prima ma con "corsi sul territorio" intendeva "aule di corsi di area", vai a capire.  */
SELECT COUNT(*) FROM `tra_class` cl JOIN tra_course co ON cl.course_id = co.id WHERE co.groupamaType = 'area' AND co.year = 2023;
SELECT COUNT(*) from tra_attendance a JOIN tra_course c ON a.course_id = c.id WHERE c.groupamaType = 'area' and c.year = 2023 and a.state = 'signedup';

/* Richiesta Martella: tutte le Agenzie attive con relativa area e distretto */
SELECT a.id as 'Codice Agenzia', ga.id as 'ID Area', ga.nome as 'Area', CONCAT(u.nome, ' ', u.cognome) as 'Area m.', gd.id as 'ID Distretto', gd.nome as 'District m.'
FROM agenzie a
    JOIN geo_aree ga ON a.area = ga.id
    JOIN geo_districts gd ON a.district = gd.id
    JOIN users u ON a.area = u.area AND u.active = 1 AND u.type = 'AREAMGR'
WHERE a.status !='OFF';

/* Richiesta Catinella: conteggio dei corsi più frequentati dai dipendenti di agenzia */
SELECT c.year, c.title, c.groupamaType, COUNT(a.id) FROM tra_course as c
    JOIN tra_attendance as a ON c.id = a.course_id
WHERE c.year IN(2022,2023,2024) AND a.role IN('DIP_AGZ','DIP_SUBAGZ') AND a.state = 'signedup' AND a.result= 'ok' GROUP BY c.id;

DROP VIEW IF EXISTS tra_ania_counters;
CREATE VIEW tra_ania_counters
as select
       SUM(countDirezOk.counter + countDirezKo.counter) as 'Totale partecipazioni Direzione',
       countDirezOk.counter as 'Partecipazioni Direzione OK',
       countDirezKo.counter as 'Partecipazioni Direzione KO',
       SUM(countAreaOk.counter + countAreaKo.counter) as 'Totale partecipazioni Area',
       countAreaOk.counter as 'Partecipazioni Area OK',
       countAreaKo.counter as 'Partecipazioni Area KO',
       SUM(countDirezOk.totalDirezCredits + countAreaOk.totalAreaCredits ) as 'Totale crediti erogati',
       countDirezOk.totalDirezCredits as 'Crediti erogati Direzione',
       countAreaOk.totalAreaCredits as 'Crediti erogati Area'
   from (
            (
                SELECT COUNT(*) as counter, SUM(tra_attendance.credits) as totalDirezCredits from tra_attendance
                                                                                                      INNER JOIN tra_course ON tra_attendance.course_id = tra_course.id
                WHERE tra_course.groupamaType = 'direz' and tra_attendance.state = 'signedup' AND tra_attendance.result = 'ok' AND tra_course.year = 2024
            ) countDirezOk,
                (
                    SELECT COUNT(*) as counter from tra_attendance
                                                        INNER JOIN tra_course ON tra_attendance.course_id = tra_course.id
                    WHERE tra_course.groupamaType = 'direz' and tra_attendance.state = 'signedup' AND tra_attendance.result = 'ko' AND tra_course.year = 2024
                ) countDirezKo,
                (
                    SELECT COUNT(*) as counter, SUM(tra_attendance.credits) as totalAreaCredits from tra_attendance
                                                                                                          INNER JOIN tra_course ON tra_attendance.course_id = tra_course.id
                    WHERE tra_course.groupamaType = 'area' and tra_attendance.state = 'signedup' AND tra_attendance.result = 'ok' AND tra_course.year = 2024
                ) countAreaOk,
                (
                    SELECT COUNT(*) as counter from tra_attendance
                                                        INNER JOIN tra_course ON tra_attendance.course_id = tra_course.id
                    WHERE tra_course.groupamaType = 'area' and tra_attendance.state = 'signedup' AND tra_attendance.result = 'ko' AND tra_course.year = 2024
                ) countAreaKo
            );

/* Agenti che non hanno risposto alla survey di Formazione */
SELECT u.* FROM `users` u left outer join tra_survey_training_offer t ON t.user_id = u.id WHERE u.active = 1 AND u.type = 'AGENTE' AND t.user_id is null;

/* Madonna vuole le stat degli accessi a Uniqum */
SELECT s.date as 'Data accesso', s.n as 'Num. accessi', u.nome as Nome, u.cognome as Cognome, u.agenzia_id as Agenzia FROM stats_users_actions s LEFT JOIN users u ON s.user_id = u.id WHERE s.l1 = 'uniqum';