DROP TABLE IF EXISTS pc_data;
CREATE TABLE `pc_data` (
    `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `agenzia_id` CHAR(4) NOT NULL,
    `canale` VARCHAR(64) NULL DEFAULT '',
    `contraente` VARCHAR(64) NULL DEFAULT '',
    `polizza` VARCHAR(20) NOT NULL,
    `polizzaMadre` VARCHAR(20) NULL DEFAULT '',
    `tipoEsitoOperazione` VARCHAR(20) NULL DEFAULT '',
    `dataEffettoContratto` DATE NULL,
    `dataScadenzaContratto` DATE NULL,
    `dataEffettoOperazione` DATE NULL,
    `dataContabileEmissione` DATE NOT NULL, /* data da controllare per le incentivazioni */
    `dataContabileIncasso` DATE NULL,
    `dataEffettivoIncasso` DATE NULL,
    `frazionamento` VARCHAR(64) NULL DEFAULT '',
    `numeroCampagna` VARCHAR(10) NULL DEFAULT '',
    `nomeCampagna` VARCHAR(64) NULL DEFAULT '',
    `codiceConvenzione` VARCHAR(10) NULL DEFAULT '',
    `nomeConvenzione` VARCHAR(64) NULL DEFAULT '',
    `tipoProdotto` VARCHAR(64) NULL DEFAULT '',
    `codiceProdotto` VARCHAR(10) NULL DEFAULT '',
    `nomeProdotto` VARCHAR(64) NULL DEFAULT '',
    `flagCollettiva` VARCHAR(10) NOT NULL,
    `tipoCollettiva` VARCHAR(20) NULL DEFAULT '',
    `premioNetto` DECIMAL(10,2) UNSIGNED NOT NULL,
    `premioAccessori` DECIMAL(10,2) UNSIGNED NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_pc_data` (`agenzia_id`, `codiceProdotto`, `polizza`, `dataContabileEmissione`, `premioNetto`),
    KEY `fk_agenzie` (`agenzia_id`),
    KEY `idx_policy` (`agenzia_id`, `codiceProdotto`, `polizza`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `pc_data`
    ADD CONSTRAINT `fk_pc_data_agenzia_id` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);
