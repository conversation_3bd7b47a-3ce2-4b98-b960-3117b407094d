ALTER TABLE `cms_agenzie_status` ADD `shortLink` VARCHAR(150) NULL AFTER `link`;

DROP VIEW IF EXISTS `vw_cms_agenzie_data`;
CREATE VIEW `vw_cms_agenzie_data` as
SELECT ca.agenzia_id, cs.groupama_id, ag.nome, cs.link, cs.shortLink, ca.approved, ca.agencyEntrancePhoto, ca.whatsapp, ca.description, ca.url, ca.pec, ca.text, ca.showWeeklySchedule, gd.nome as distretto, ga.nome as area
FROM cms_agenzie_status cs
         JOIN cms_agenzie_data ca ON cs.agenzia_id = ca.agenzia_id
         JOIN agenzie ag ON ag.id = ca.agenzia_id
         LEFT JOIN geo_aree ga ON ag.area = ga.id
         LEFT JOIN geo_districts gd ON ag.district = gd.id;
