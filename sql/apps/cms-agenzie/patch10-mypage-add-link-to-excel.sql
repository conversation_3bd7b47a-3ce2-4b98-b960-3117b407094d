DROP VIEW IF EXISTS `vw_cms_agenzie_status`;

/* Aggiungo link e shortlink alla vista dello status per poterlo avere nell'excel */
CREATE VIEW `vw_cms_agenzie_status` as
SELECT ain.agenzia_id, REPLACE( REPLACE( GROUP_CONCAT(ain.nominativo SEPARATOR ', '), ', -', '' ), '-, ', '' ) as agents, ain.groupama_id, ain.nome, ain.ragioneSociale, ain.approved, ain.standard, ain.distretto, ain.area, ain.localita, ain.link, ain.shortLink,
       CONVERT(IF(ain.agencyEntrancePhoto IS NOT NULL, 'SI', 'NO') USING utf8mb3) as hasAgencyPhoto, ain.detail, SUM(ain.privacy) as privacyAccepted, COUNT(ain.nominativo) as teamTotal
FROM (
         SELECT cs.agenzia_id, IF( u.type = 'AGENTE', CONCAT(u.nome, ' ', u.cognome), '-') as nominativo, cs.groupama_id, ag.nome, cs.ragioneSociale, cs.approved, cs.standard, cs.link, cs.shortLink,
                gd.nome as distretto, ga.nome as area, ag.localita, cd.agencyEntrancePhoto, IF(ci.privacy = 'YES', 1, 0) as privacy,
                CONVERT((CASE
                             WHEN cd.description IS NOT NULL AND cd.url IS NOT NULL AND cd.whatsapp IS NOT NULL THEN "FULL"
                             WHEN cd.description IS NOT NULL OR cd.url IS NOT NULL OR cd.whatsapp IS NOT NULL THEN "PARTIAL"
                             ELSE "EMPTY"
                    END) USING utf8mb3) as detail
         FROM cms_agenzie_status cs
                  JOIN agenzie ag ON ag.id = cs.agenzia_id AND ag.status IN('ON', 'NEW')
                  JOIN cms_agenzie_data cd ON cd.agenzia_id = cs.agenzia_id AND cd.approved = 0
                  JOIN users u on ag.id = u.agenzia_id
                  JOIN cms_agenzie_impiegati ci ON ci.user_id = u.id AND ci.approved = 1
                  LEFT JOIN geo_aree ga ON ag.area = ga.id
                  LEFT JOIN geo_districts gd ON ag.district = gd.id
         WHERE u.type IN('AGENTE', 'INTERMEDIARIO')  AND u.active = 1
     ) as ain
GROUP BY ain.agenzia_id;