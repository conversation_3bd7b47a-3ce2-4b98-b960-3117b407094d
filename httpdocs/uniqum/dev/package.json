{"name": "uniqum", "version": "1.3.4", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@headlessui/tailwindcss": "^0.2.1", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.1.5", "@phosphor-icons/vue": "^2.2.1", "@vee-validate/i18n": "^4.13.2", "@vee-validate/rules": "^4.13.2", "axios": "^1.7.7", "floating-vue": "^5.2.2", "pinia": "^2.1.7", "vee-validate": "^4.13.2", "vue": "^3.4.29", "vue-router": "^4.3.3", "vue-tippy": "^6.4.4", "vue3-toastify": "^0.2.3", "vuejs-confirm-dialog": "^0.5.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.12", "vite": "^5.3.1", "vite-plugin-vue-devtools": "^7.3.1"}}