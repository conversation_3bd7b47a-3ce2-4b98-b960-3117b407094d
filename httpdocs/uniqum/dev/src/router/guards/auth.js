import {useUserStore} from "@/_common/stores/user.js";
import config from '@/config'
import {auth} from "@/_common/api/auth.js";

const AGENTE_RESTRICTED_ROUTES = ['team', 'active-courses', 'course-detail', 'bookings'];

export function applyAuthGuard(router) {
    const userStore = useUserStore()

    router.beforeEach(async (to, from, next) => {
        // First check authentication
        if (!userStore?.isAuthenticated) {
            homeRedirect();
            return;
        }

        // Check user type
        if (userStore.UTYPE !== 'AGENTE' && userStore.UTYPE !== 'AREAMGR' && userStore.UTYPE !== 'INTERMEDIARIO') {
            homeRedirect();
            return;
        }

        // For INTERMEDIARIO, check if allowed via API
        if (userStore.UTYPE === 'INTERMEDIARIO') {
            try {
                const isAllowed = await auth.checkEnrolled();
                if (isAllowed) {
                    next();
                    return;
                } else {
                    homeRedirect();
                    return;
                }
            } catch (error) {
                console.error('Error checking permissions:', error);
                homeRedirect();
                return;
            }
        }

        // Gli AM hanno acceso solo ai materiali e alle pagine di testo
        if (userStore.UTYPE !== 'AGENTE' && AGENTE_RESTRICTED_ROUTES.includes(to.name)) {
            homeRedirect();
        } else {
            next();
        }
    })
}

function homeRedirect() {
    // Questo if è un workaround per evitare un loop infinito di redirect nella build live
    if ( ! import.meta.env.DEV ) {
        window.location.href = '../'
    }
}