import axios from 'axios';

async function getActiveCourses() {

    const res = (await axios.get('/apps/uniqum/dashboard/active-courses'))?.data;

    if (res?.success) {
        return res;
    }
    return [];
}

async function getCourse(courseId) {

    const res = (await axios.get(`/apps/formazione/course/view/${courseId}`))?.data;

    if (res?.success) {
        return res;
    }
    return [];
}

async function getAvailableClassrooms(courseId) {

    const res = (await axios.get(`/apps/formazione/booking/course/${courseId}/available`))?.data;

    if (res?.success) {
        return res;
    }
    return [];
}

async function getClassroomData(classId) {

    const res = (await axios.get(`/apps/formazione/booking/classroom/${classId}`))?.data;

    if (res?.success) {
        return res;
    }
    return [];
}

async function getClassroomBookings(classId) {

    const res = (await axios.get(`/apps/formazione/booking/course/${classId}/recipients`))?.data;

    if (res?.success) {
        return res;
    }
    return [];
}

export const coursesApi = {
    getActiveCourses,
    getCourse,
    getAvailableClassrooms,
    getClassroomData,
    getClassroomBookings
}
