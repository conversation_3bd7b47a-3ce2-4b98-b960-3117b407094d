<script setup>
import LaunchVideoModal from "@/views/VideoModal.vue";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, PhFilePdf, PhFilePpt, PhPlayCircle} from "@phosphor-icons/vue";
import {ref} from "vue";
import {materialsApi} from "@/_common/api/materials.js";
import {toast} from "vue3-toastify";

const launchVideoModal = ref(null);
const highlightedMaterials = ref([]);

getHighlightedMaterials()
async function getHighlightedMaterials() {
    const res = await materialsApi.getHomeHighlightedMaterials()
    if ( ! res?.success ) {
        return toast.error('Errore imprevisto')
    }
    highlightedMaterials.value = res.data
}

function openLaunchVideoModal() {
    launchVideoModal.value.openModal();
}

function openFile(fileName) {
    window.open('https://' + window.staticHost + '/uniqum/files/' + fileName, '_blank');
}
</script>

<template>
    <LaunchVideoModal ref="launchVideoModal" :cover="'/assets/dashboard/uniqum-cover-video.jpg'" :source="'/assets/dashboard/Groupama_Uniqum_1080.mp4'" />

    <div class="pt-12 pb-12 relative">
        <div class="container-small">
            <div class="text-5xl md:text-7xl font-light text-white text-center mb-8">L'evoluzione continua.</div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <a class="big-pill flex gap-8 items-center justify-center cursor-pointer text-white rounded-full mx-auto" :href="'https://' + window.staticHost + '/assets/dashboard/Uniqum-report.pdf'" target="_blank">
                    <div><PhFilePdf size="50" color="#ffffff" /></div>
                    <div class="text-xl font-light">Scarica il report della prima tornata di aule</div>
                </a>

                <div class="big-pill flex gap-8 items-center justify-center cursor-pointer text-white rounded-full mx-auto" @click="openLaunchVideoModal">
                    <div><PhPlayCircle size="50" color="#ffffff" /></div>
                    <div class="text-xl font-light">Guarda il video con tutte le novità</div>
                </div>
            </div>

        </div>
    </div>

    <div class="bg-white min-h-screen h-full py-16">
        <div class="container-small">

            <div class="grid grid-cols-3 gap-5 mb-12">
                <div class="col-span-3 md:col-span-1">
                    <img class="mx-auto" :src="'https://' + window.staticHost + '/themes/uniqum/tizia-farfalla.png'" alt="">
                </div>
                <div class="col-span-3 md:col-span-2">
                    <div class="text-2xl text-[#00624A] font-bold mb-3 leading-tight">Uniqum si impegna a valorizzare al meglio ogni risorsa a disposizione, con una visione che si fonda su due pilastri essenziali.</div>
                    <div class="text-lg text-[#3E4154] mb-3 leading-tight"><span class="font-bold">Innovazione continua:</span><br>arricchire costantemente il sito con nuovi contenuti e attività.</div>
                    <div class="text-lg text-[#3E4154] mb-3 leading-tight"><span class="font-bold">Collaborazione totale:</span><br>coinvolgere attivamente tutti gli attori, dalla Compagnia agli Agenti, dai Dipendenti ai Collaboratori di Agenzia.</div>
                </div>
            </div>

            <div class="grid grid-cols-3 gap-5 mb-12">
                <div class="col-span-3 md:col-span-2 md:flex md:flex-col md:justify-end">
                    <div class="text-2xl text-[#00624A] font-bold mb-3 leading-tight">Uniqum si evolve con una nuova sezione dedicata agli Approfondimenti.</div>
                    <div class="flex gap-x-3 mb-1">
                        <PhCrosshair class="flex-shrink-0" size="38" color="#7ec5ab" />
                        <div class="text-lg text-[#3E4154] mb-3 leading-tight"><span class="font-bold">Contenuti mirati:</span><br>tematiche commerciali, di prodotto e operative.</div>
                    </div>
                    <div class="flex gap-x-3">
                        <PhPlayCircle class="flex-shrink-0" size="38" color="#7ec5ab" />
                        <div class="text-lg text-[#3E4154] mb-3 leading-tight"><span class="font-bold">Formato innovativo:</span><br>pillole informative in video per catturare l’attenzione e stimolare la curiosità.</div>
                    </div>
                    <div class="text-lg"><span class="text-[#E94E1D] font-bold">Esplora questa sezione e condividi contenuti con i tuoi dipendenti.</span> Ogni video sarà un’opportunità per confrontarsi e crescere insieme.</div>
                </div>
                <div class="col-span-3 md:col-span-1 hidden md:flex md:flex-col md:justify-end">
                    <img class="hidden md:block" :src="'https://' + window.staticHost + '/themes/uniqum/fumetto.png'" alt="">
                    <div class="bg-[#C7E6DA] text-xl text-semibold text-center text-[#3E4154] py-2.5 px-14 rounded-xl" style="border-radius: 50px">Approfondimenti</div>
                </div>
            </div>

            <div class="text-3xl text-[#00624A] font-bold text-center">
                La strada è pronta, sta a noi percorrerla insieme per collaborare, crescere, eccellere!
            </div>

        </div>
    </div>
    <div class="bg-[#e4f3ed] py-16">
        <div class="container-small">

            <div class="text-3xl text-[#00624A] font-bold text-center mb-12">In evidenza</div>

            <div class="flex justify-center">
                <div class="flex items-center gap-3" v-for="material in highlightedMaterials">
                    <div class="flex items-center">
                        <img :src="'https://' + window.staticHost + '/uniqum/covers/' + material.cover" alt="" class="max-w-32 rounded-full">
                        <!--                            <img :src="'https://placehold.co/260x260'" alt="" class="max-w-32 rounded-full">-->
                        <div class="bg-white rounded-full shadow-lg -ml-6 p-4">
                            <PhFilePpt v-if="material.type === 'PPT'" size="34" />
                            <PhFilePdf v-if="material.type === 'PDF'" size="34" />
                            <PhPlayCircle v-if="material.type === 'VID'" size="34" />
                        </div>
                    </div>
                    <div>
                        <div class="text-lg font-bold leading-tight text-[#00624A]">{{ material.title }}</div>
                        <div class="text-[#3E4154] underline cursor-pointer" v-if="material.fileName" @click="openFile(material.fileName)">{{ material.data.linkString }}</div>
                        <RouterLink :to="{name: material.data.internalLink}" class="text-[#3E4154] underline cursor-pointer" v-if="material.data?.internalLink">{{ material.data.linkString }}</RouterLink>
                    </div>
                </div>
            </div>

<!--            <div class="grid grid-cols-2 gap-10">

                <div class="col-span-2 md:col-span-1" v-for="material in highlightedMaterials">

                    <div class="flex items-center gap-3">
                        <div class="flex items-center">
                            <img :src="'https://' + window.staticHost + '/uniqum/covers/' + material.cover" alt="" class="max-w-32 rounded-full">
&lt;!&ndash;                            <img :src="'https://placehold.co/260x260'" alt="" class="max-w-32 rounded-full">&ndash;&gt;
                            <div class="bg-white rounded-full shadow-lg -ml-6 p-4">
                                <PhFilePpt v-if="material.type === 'PPT'" size="34" />
                                <PhFilePdf v-if="material.type === 'PDF'" size="34" />
                                <PhPlayCircle v-if="material.type === 'VID'" size="34" />
                            </div>
                        </div>
                        <div>
                            <div class="text-lg font-bold leading-tight text-[#00624A]">{{ material.title }}</div>
                            <div class="text-[#3E4154] underline cursor-pointer" v-if="material.fileName" @click="openFile(material.fileName)">{{ material.data.linkString }}</div>
                            <RouterLink :to="{name: material.data.internalLink}" class="text-[#3E4154] underline cursor-pointer" v-if="material.data?.internalLink">{{ material.data.linkString }}</RouterLink>
                        </div>
                    </div>

                </div>

            </div>-->

        </div>
    </div>
</template>

<style scoped>
.big-pill {
    background: #063C3E;
    background: linear-gradient(0deg, rgba(6, 60, 62, 1) 0%, rgba(9, 157, 162, 1) 75%);
    @apply py-5 px-14;
}
</style>
