<script setup>
import {defineEmits, ref} from "vue";
import {Bars3Icon} from "@heroicons/vue/24/solid/index.js";
import MenuMobile from "@/views/MenuMobile.vue";
import {Menu, MenuButton, MenuItem, MenuItems} from "@headlessui/vue";
import {ChevronDownIcon} from "@heroicons/vue/16/solid/index.js";
import {PhBookBookmark, PhChartPieSlice, PhHandshake, PhTreeStructure} from "@phosphor-icons/vue";
import {materialsApi} from "@/_common/api/materials.js";
import {toast} from "vue3-toastify";
import {useUserStore} from "@/_common/stores/user.js";

const authData = useUserStore();
const emit = defineEmits(['clickOnFaq']);
const materialsStatus = ref([])
const hasNewMaterials = ref(false)
// Al click su "Cos'è" emetto un evento da Header verso App
function scrollToElement() {
    emit('clickOnFaq');
}
const menuMobileModal = ref(null)

function openMobilemenu() {
    menuMobileModal.value.openModal()
}

getMaterials()
async function getMaterials() {
    const res = await materialsApi.getMaterialsStatus()
    if ( ! res?.success ) {
        toast.error('Errore imprevisto')
        return;
    }
    materialsStatus.value = res.data
    hasNewMaterials.value = materialsStatus.value.some(item => item.hasNewContent === 1);
}

function checkIfCategoryHasNewContent(category) {
    return materialsStatus.value.some(item => item.category === category && item.hasNewContent === 1);
}
</script>

<template>
    <MenuMobile ref="menuMobileModal" @scroll-request="scrollToElement" />
    <header style="padding: 35px 0">
        <div class="container">
            <div class="flex items-center justify-between md:justify-normal" style="gap: 40px">
                <RouterLink to="/">
                    <img class="mb-1" :src="'https://' + window.staticHost + '/themes/uniqum/uniqum-logo.svg'" alt="Uniqum">
                    <div style="color: #ffffff; font-size: 10px">COLLABORARE, CRESCERE, ECCELLERE.</div>
                </RouterLink>
                <div class="flex justify-between grow items-center">
                    <nav class="menu gap-x-5 hidden lg:flex">
                        <RouterLink to="/corsi-attivi" v-if="authData.UTYPE !== 'AREAMGR' && authData.UTYPE !== 'INTERMEDIARIO'">Corsi attivi</RouterLink>
                        <RouterLink to="/prenotazioni" v-if="authData.UTYPE !== 'AREAMGR' && authData.UTYPE !== 'INTERMEDIARIO'">Le tue prenotazioni</RouterLink>
                        <Menu as="div" class="relative">

                            <div class="absolute right-0 top-0" v-if="hasNewMaterials">
                                <div class="h-4 w-4 rounded-full bg-[#E94E1D]"></div>
                            </div>

                            <div>
                                <MenuButton class="flex items-center gap-x-3">
                                    Approfondimenti
                                    <ChevronDownIcon
                                        class="h-5 w-5 text-[#00624A]"
                                        aria-hidden="true"
                                    />
                                </MenuButton>
                            </div>

                            <transition
                                enter-active-class="transition duration-100 ease-out"
                                enter-from-class="transform scale-95 opacity-0"
                                enter-to-class="transform scale-100 opacity-100"
                                leave-active-class="transition duration-75 ease-in"
                                leave-from-class="transform scale-100 opacity-100"
                                leave-to-class="transform scale-95 opacity-0"
                            >
                                <MenuItems
                                    class="absolute left-0 mt-5 p-2 w-56 origin-top-right divide-y-2 divide-gray-500 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none z-10"
                                >
                                    <MenuItem v-slot="{ active }">
                                        <RouterLink :to="{name: 'commercial'}"
                                            class="relative flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"
                                        >
                                            <div class="absolute right-0 top-1/2 -translate-y-1/2" style="top: 50%; transform: translateY(-50%)" v-if="checkIfCategoryHasNewContent('commercial')">
                                                <div class="h-4 w-4 rounded-full bg-[#E94E1D]"></div>
                                            </div>
                                            <PhHandshake size="28" class="mr-5" />
                                            <span class="text-left leading-tight">Ambito<br>commerciale</span>
                                        </RouterLink>
                                    </MenuItem>

                                    <MenuItem v-slot="{ active }">
                                        <RouterLink :to="{name: 'products'}"
                                            class="relative flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"
                                        >
                                            <div class="absolute right-0 top-1/2 -translate-y-1/2" style="top: 50%; transform: translateY(-50%)" v-if="checkIfCategoryHasNewContent('products')">
                                                <div class="h-4 w-4 rounded-full bg-[#E94E1D]"></div>
                                            </div>
                                            <PhBookBookmark size="28" class="mr-5" />
                                            <span class="text-left leading-tight">Conoscere<br>i prodotti</span>
                                        </RouterLink>
                                    </MenuItem>

                                    <MenuItem v-slot="{ active }">
                                        <RouterLink :to="{name: 'operative'}"
                                            class="relative flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"
                                        >
                                            <div class="absolute right-0 top-1/2 -translate-y-1/2" v-if="checkIfCategoryHasNewContent('operative')">
                                                <div class="h-4 w-4 rounded-full bg-[#E94E1D]"></div>
                                            </div>
                                            <PhTreeStructure size="28" class="mr-5" />
                                            <span class="text-left leading-tight">Novità operative<br>e di processo</span>
                                        </RouterLink>
                                    </MenuItem>

                                </MenuItems>
                            </transition>
                        </Menu>
                    </nav>
                    <nav class="menu-2 gap-x-3 hidden lg:flex items-center">
                        <RouterLink active-class="active" :to="{name: 'description'}"  title="Cos'è Uniqum">Cos'è Uniqum</RouterLink>
                        <span style="background-color: #14513A; width: 1px; height: 30px"></span>
                        <RouterLink v-if="authData.UTYPE !== 'AREAMGR' && authData.UTYPE !== 'INTERMEDIARIO'" active-class="active" to="/team" title="Il tuo team" class="relative">
                            Il tuo team
                        </RouterLink>
                    </nav>
                </div>
                <div class="block lg:hidden">
                    <Bars3Icon class="text-white size-10" @click="openMobilemenu" />
                </div>
            </div>
        </div>
        <div class="hidden 2xl:block" style="position: absolute; right: 0; top: 25px">
            <div style="background-color: #ffffff; padding: 18px">
                <img :src="'https://' + window.staticHost + '/themes/uniqum/groupama-logo.svg'" alt="Groupama">
            </div>
        </div>
    </header>
</template>

<style scoped>

</style>