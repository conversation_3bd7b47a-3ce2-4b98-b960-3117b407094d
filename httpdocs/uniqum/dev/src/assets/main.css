@import './base.css';

html { scroll-behavior: smooth; }

body {
    background-size: cover;
    background-image: url("bgr-body.jpg");
    background-repeat: no-repeat;
    background-position: top center;
    background-attachment: fixed;
    min-height: 100vh;
    color: #14513A;
    font-family: 'Nuni<PERSON>', sans-serif;
    font-weight: 500;
}

*:focus {
    outline: none;
}

.container-small {
    @apply mx-auto px-6;
    max-width: 900px;
}

@media (min-width: 1280px) {
    .container {
        max-width: 1200px;
    }
}

h1 {
    color: #00624A;
}

.menu {
    > a {
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 25px;
        padding: 10px 18px;
        font-weight: 500;
        color: #14513A;
        transition: all 0.2s ease-in-out;
        &:hover {
            background-color: rgba(255, 255, 255, 1);
        }
        &.active {
            background-color: rgba(255, 255, 255, 1);
        }
    }
    > div {
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 25px;
        padding: 10px 18px;
        font-weight: 500;
        color: #14513A;
        transition: all 0.2s ease-in-out;
        &:hover {
            background-color: rgba(255, 255, 255, 1);
        }
        &.active {
            background-color: rgba(255, 255, 255, 1);
        }
    }
}

.menu-2 {
    a {
        color: #14513A;
        font-weight: 500;
        &:hover {
            color: #ffffff;
        }
        &.active {
            color: #ffffff;
        }
    }
    a:before {
        display: block;
        content: attr(title);
        font-weight: bold;
        height: 1px;
        color: transparent;
        overflow: hidden;
        visibility: hidden;
    }
    .label-date {
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #FF0000;
        color: #FFFFFF;
        font-size: 12px;
        font-weight: 400;
        white-space: nowrap;
        @apply rounded-full px-3 py-0.5;
    }
}

.card {
    background-color: #ffffff;
    box-shadow: 0 3px 12px #00000029;
    border-radius: 12px;
    @apply py-6 px-8 md:py-8 md:px-8;
    &.card-fit {
        padding: 0;
    }
}

.card-header button {
    @apply text-xl;
    color: #14513A;
}

.course-card {
    @apply flex flex-col items-stretch;
    background-color: #ffffff;
    box-shadow: 0 3px 12px #00000029;
    border-radius: 12px;
    .card-body {
        padding: 12px 20px;
        @apply flex flex-col grow justify-between;
    }
    .btn-calendar {
        background-color: #ffffff;
        box-shadow: 0 3px 6px #00000029;
        @apply rounded-full p-2;
    }
}

.tab {
    font-size: 1.5rem;
    &.active {
        @apply font-black;
        border-bottom: 4px solid #E23614;
    }
}

.btn {
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    &.compact {
        padding: 5px 15px;
    }
}

.btn:disabled {
    opacity: 0.8;
}

.btn-red {
    display: inline-block;
    background-color: #E94E1D;
    color: #ffffff;
    &:hover {
        background-color: #af3914;
    }
}

.btn-outline {
    border: 1px solid #707070;
    color: #3E4154;
    &:hover {
        color: #ffffff;
        background-color: #3E4154;
    }
}

.btn-outline-inv {
    border: 1px solid #3E4154;
    color: #ffffff;
    background-color: #3E4154;
    &:hover {
        color: #3E4154;
        background-color: #ffffff;
    }
}

.btn-secondary {
    border: 1px solid transparent;
    color: #3E4154;
    &:hover {
        border: 1px solid #707070;
    }
}

.btn-outline-red {
    @apply border;
    display: inline-block;
    background-color: transparent;
    color: #E23614;
    &:hover {
        background-color: #E23614;
        color: #FFFFFF;
    }
}

.btn-pill {
    background-color: #4E4E4E;
    color: #FFFFFF;
    padding: 8px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    &.compact {
        padding: 5px 15px;
    }
}

.form-group {
    margin-bottom: 25px;

    > label {
        @apply text-sm;
        color: #7D8299;
    }

    input[type='date'],
    input[type='text'],
    input[type='number'],
    select {
        @apply rounded;
        border: 1px solid #E1E3EA;
        color: #3E4154;
        font-size: 0.875rem;
        line-height: normal;
        padding: 5px 10px;
        width: 100%;
        &:focus-visible {
            outline: 2px solid #898baa;
        }
    }

    .has-error {
        border: 1px solid #D9214E !important;
    }

}

/* Toglie i fottuti numeretti dagli input number */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type=number] {
    -moz-appearance: textfield;
}

.form-error {
    color: #D9214E;
}

.embed-container {
    position: relative;
    padding-bottom: 56.6%;
    height: 0;
    overflow: hidden;
    max-width: 100%;

}

.embed-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.new-sticker {
    @apply bg-[#e23614] text-white font-bold rounded-full uppercase h-8 w-8;
    font-size: 10px;
    transform: rotate(-25deg);
    -webkit-box-shadow: 0px 0px 8px 0px rgba(226,54,20,1);
    -moz-box-shadow: 0px 0px 8px 0px rgba(226,54,20,1);
    box-shadow: 0px 0px 8px 0px rgba(226,54,20,1);
}