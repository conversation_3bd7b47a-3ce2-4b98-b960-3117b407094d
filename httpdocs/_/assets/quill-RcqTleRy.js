var ra=Object.defineProperty;var sa=(n,t,e)=>t in n?ra(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var x=(n,t,e)=>sa(n,typeof t!="symbol"?t+"":t,e);import{c as Xt,g as Fl}from"./app-DvLzsgz7.js";var Hl=typeof global=="object"&&global&&global.Object===Object&&global,ia=typeof self=="object"&&self&&self.Object===Object&&self,kt=Hl||ia||Function("return this")(),te=kt.Symbol,zl=Object.prototype,la=zl.hasOwnProperty,oa=zl.toString,dn=te?te.toStringTag:void 0;function aa(n){var t=la.call(n,dn),e=n[dn];try{n[dn]=void 0;var r=!0}catch{}var s=oa.call(n);return r&&(t?n[dn]=e:delete n[dn]),s}var ca=Object.prototype,ua=ca.toString;function ha(n){return ua.call(n)}var fa="[object Null]",da="[object Undefined]",Ui=te?te.toStringTag:void 0;function Ke(n){return n==null?n===void 0?da:fa:Ui&&Ui in Object(n)?aa(n):ha(n)}function Ut(n){return n!=null&&typeof n=="object"}var fe=Array.isArray;function ee(n){var t=typeof n;return n!=null&&(t=="object"||t=="function")}function Gl(n){return n}var ga="[object AsyncFunction]",pa="[object Function]",ma="[object GeneratorFunction]",ba="[object Proxy]";function gi(n){if(!ee(n))return!1;var t=Ke(n);return t==pa||t==ma||t==ga||t==ba}var Os=kt["__core-js_shared__"],Fi=function(){var n=/[^.]+$/.exec(Os&&Os.keys&&Os.keys.IE_PROTO||"");return n?"Symbol(src)_1."+n:""}();function ya(n){return!!Fi&&Fi in n}var va=Function.prototype,Ea=va.toString;function pe(n){if(n!=null){try{return Ea.call(n)}catch{}try{return n+""}catch{}}return""}var Aa=/[\\^$.*+?()[\]{}|]/g,Na=/^\[object .+?Constructor\]$/,wa=Function.prototype,Ta=Object.prototype,xa=wa.toString,La=Ta.hasOwnProperty,Sa=RegExp("^"+xa.call(La).replace(Aa,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function _a(n){if(!ee(n)||ya(n))return!1;var t=gi(n)?Sa:Na;return t.test(pe(n))}function Oa(n,t){return n==null?void 0:n[t]}function me(n,t){var e=Oa(n,t);return _a(e)?e:void 0}var Fs=me(kt,"WeakMap"),Hi=Object.create,qa=function(){function n(){}return function(t){if(!ee(t))return{};if(Hi)return Hi(t);n.prototype=t;var e=new n;return n.prototype=void 0,e}}();function Ca(n,t,e){switch(e.length){case 0:return n.call(t);case 1:return n.call(t,e[0]);case 2:return n.call(t,e[0],e[1]);case 3:return n.call(t,e[0],e[1],e[2])}return n.apply(t,e)}function Kl(n,t){var e=-1,r=n.length;for(t||(t=Array(r));++e<r;)t[e]=n[e];return t}var Ia=800,Ra=16,ka=Date.now;function Ba(n){var t=0,e=0;return function(){var r=ka(),s=Ra-(r-e);if(e=r,s>0){if(++t>=Ia)return arguments[0]}else t=0;return n.apply(void 0,arguments)}}function Ma(n){return function(){return n}}var yr=function(){try{var n=me(Object,"defineProperty");return n({},"",{}),n}catch{}}(),$a=yr?function(n,t){return yr(n,"toString",{configurable:!0,enumerable:!1,value:Ma(t),writable:!0})}:Gl,Da=Ba($a);function ja(n,t){for(var e=-1,r=n==null?0:n.length;++e<r&&t(n[e],e,n)!==!1;);return n}var Pa=9007199254740991,Ua=/^(?:0|[1-9]\d*)$/;function Vl(n,t){var e=typeof n;return t=t??Pa,!!t&&(e=="number"||e!="symbol"&&Ua.test(n))&&n>-1&&n%1==0&&n<t}function pi(n,t,e){t=="__proto__"&&yr?yr(n,t,{configurable:!0,enumerable:!0,value:e,writable:!0}):n[t]=e}function _n(n,t){return n===t||n!==n&&t!==t}var Fa=Object.prototype,Ha=Fa.hasOwnProperty;function Wl(n,t,e){var r=n[t];(!(Ha.call(n,t)&&_n(r,e))||e===void 0&&!(t in n))&&pi(n,t,e)}function On(n,t,e,r){var s=!e;e||(e={});for(var i=-1,l=t.length;++i<l;){var a=t[i],c=void 0;c===void 0&&(c=n[a]),s?pi(e,a,c):Wl(e,a,c)}return e}var zi=Math.max;function za(n,t,e){return t=zi(t===void 0?n.length-1:t,0),function(){for(var r=arguments,s=-1,i=zi(r.length-t,0),l=Array(i);++s<i;)l[s]=r[t+s];s=-1;for(var a=Array(t+1);++s<t;)a[s]=r[s];return a[t]=e(l),Ca(n,this,a)}}function Ga(n,t){return Da(za(n,t,Gl),n+"")}var Ka=9007199254740991;function Zl(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=Ka}function Tr(n){return n!=null&&Zl(n.length)&&!gi(n)}function Va(n,t,e){if(!ee(e))return!1;var r=typeof t;return(r=="number"?Tr(e)&&Vl(t,e.length):r=="string"&&t in e)?_n(e[t],n):!1}function Wa(n){return Ga(function(t,e){var r=-1,s=e.length,i=s>1?e[s-1]:void 0,l=s>2?e[2]:void 0;for(i=n.length>3&&typeof i=="function"?(s--,i):void 0,l&&Va(e[0],e[1],l)&&(i=s<3?void 0:i,s=1),t=Object(t);++r<s;){var a=e[r];a&&n(t,a,r,i)}return t})}var Za=Object.prototype;function mi(n){var t=n&&n.constructor,e=typeof t=="function"&&t.prototype||Za;return n===e}function Xa(n,t){for(var e=-1,r=Array(n);++e<n;)r[e]=t(e);return r}var Ya="[object Arguments]";function Gi(n){return Ut(n)&&Ke(n)==Ya}var Xl=Object.prototype,Qa=Xl.hasOwnProperty,Ja=Xl.propertyIsEnumerable,Hs=Gi(function(){return arguments}())?Gi:function(n){return Ut(n)&&Qa.call(n,"callee")&&!Ja.call(n,"callee")};function tc(){return!1}var Yl=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Ki=Yl&&typeof module=="object"&&module&&!module.nodeType&&module,ec=Ki&&Ki.exports===Yl,Vi=ec?kt.Buffer:void 0,nc=Vi?Vi.isBuffer:void 0,En=nc||tc,rc="[object Arguments]",sc="[object Array]",ic="[object Boolean]",lc="[object Date]",oc="[object Error]",ac="[object Function]",cc="[object Map]",uc="[object Number]",hc="[object Object]",fc="[object RegExp]",dc="[object Set]",gc="[object String]",pc="[object WeakMap]",mc="[object ArrayBuffer]",bc="[object DataView]",yc="[object Float32Array]",vc="[object Float64Array]",Ec="[object Int8Array]",Ac="[object Int16Array]",Nc="[object Int32Array]",wc="[object Uint8Array]",Tc="[object Uint8ClampedArray]",xc="[object Uint16Array]",Lc="[object Uint32Array]",F={};F[yc]=F[vc]=F[Ec]=F[Ac]=F[Nc]=F[wc]=F[Tc]=F[xc]=F[Lc]=!0;F[rc]=F[sc]=F[mc]=F[ic]=F[bc]=F[lc]=F[oc]=F[ac]=F[cc]=F[uc]=F[hc]=F[fc]=F[dc]=F[gc]=F[pc]=!1;function Sc(n){return Ut(n)&&Zl(n.length)&&!!F[Ke(n)]}function bi(n){return function(t){return n(t)}}var Ql=typeof exports=="object"&&exports&&!exports.nodeType&&exports,mn=Ql&&typeof module=="object"&&module&&!module.nodeType&&module,_c=mn&&mn.exports===Ql,qs=_c&&Hl.process,Ue=function(){try{var n=mn&&mn.require&&mn.require("util").types;return n||qs&&qs.binding&&qs.binding("util")}catch{}}(),Wi=Ue&&Ue.isTypedArray,yi=Wi?bi(Wi):Sc,Oc=Object.prototype,qc=Oc.hasOwnProperty;function Jl(n,t){var e=fe(n),r=!e&&Hs(n),s=!e&&!r&&En(n),i=!e&&!r&&!s&&yi(n),l=e||r||s||i,a=l?Xa(n.length,String):[],c=a.length;for(var h in n)(t||qc.call(n,h))&&!(l&&(h=="length"||s&&(h=="offset"||h=="parent")||i&&(h=="buffer"||h=="byteLength"||h=="byteOffset")||Vl(h,c)))&&a.push(h);return a}function to(n,t){return function(e){return n(t(e))}}var Cc=to(Object.keys,Object),Ic=Object.prototype,Rc=Ic.hasOwnProperty;function kc(n){if(!mi(n))return Cc(n);var t=[];for(var e in Object(n))Rc.call(n,e)&&e!="constructor"&&t.push(e);return t}function vi(n){return Tr(n)?Jl(n):kc(n)}function Bc(n){var t=[];if(n!=null)for(var e in Object(n))t.push(e);return t}var Mc=Object.prototype,$c=Mc.hasOwnProperty;function Dc(n){if(!ee(n))return Bc(n);var t=mi(n),e=[];for(var r in n)r=="constructor"&&(t||!$c.call(n,r))||e.push(r);return e}function qn(n){return Tr(n)?Jl(n,!0):Dc(n)}var An=me(Object,"create");function jc(){this.__data__=An?An(null):{},this.size=0}function Pc(n){var t=this.has(n)&&delete this.__data__[n];return this.size-=t?1:0,t}var Uc="__lodash_hash_undefined__",Fc=Object.prototype,Hc=Fc.hasOwnProperty;function zc(n){var t=this.__data__;if(An){var e=t[n];return e===Uc?void 0:e}return Hc.call(t,n)?t[n]:void 0}var Gc=Object.prototype,Kc=Gc.hasOwnProperty;function Vc(n){var t=this.__data__;return An?t[n]!==void 0:Kc.call(t,n)}var Wc="__lodash_hash_undefined__";function Zc(n,t){var e=this.__data__;return this.size+=this.has(n)?0:1,e[n]=An&&t===void 0?Wc:t,this}function de(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var r=n[t];this.set(r[0],r[1])}}de.prototype.clear=jc;de.prototype.delete=Pc;de.prototype.get=zc;de.prototype.has=Vc;de.prototype.set=Zc;function Xc(){this.__data__=[],this.size=0}function xr(n,t){for(var e=n.length;e--;)if(_n(n[e][0],t))return e;return-1}var Yc=Array.prototype,Qc=Yc.splice;function Jc(n){var t=this.__data__,e=xr(t,n);if(e<0)return!1;var r=t.length-1;return e==r?t.pop():Qc.call(t,e,1),--this.size,!0}function tu(n){var t=this.__data__,e=xr(t,n);return e<0?void 0:t[e][1]}function eu(n){return xr(this.__data__,n)>-1}function nu(n,t){var e=this.__data__,r=xr(e,n);return r<0?(++this.size,e.push([n,t])):e[r][1]=t,this}function zt(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var r=n[t];this.set(r[0],r[1])}}zt.prototype.clear=Xc;zt.prototype.delete=Jc;zt.prototype.get=tu;zt.prototype.has=eu;zt.prototype.set=nu;var Nn=me(kt,"Map");function ru(){this.size=0,this.__data__={hash:new de,map:new(Nn||zt),string:new de}}function su(n){var t=typeof n;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?n!=="__proto__":n===null}function Lr(n,t){var e=n.__data__;return su(t)?e[typeof t=="string"?"string":"hash"]:e.map}function iu(n){var t=Lr(this,n).delete(n);return this.size-=t?1:0,t}function lu(n){return Lr(this,n).get(n)}function ou(n){return Lr(this,n).has(n)}function au(n,t){var e=Lr(this,n),r=e.size;return e.set(n,t),this.size+=e.size==r?0:1,this}function be(n){var t=-1,e=n==null?0:n.length;for(this.clear();++t<e;){var r=n[t];this.set(r[0],r[1])}}be.prototype.clear=ru;be.prototype.delete=iu;be.prototype.get=lu;be.prototype.has=ou;be.prototype.set=au;function eo(n,t){for(var e=-1,r=t.length,s=n.length;++e<r;)n[s+e]=t[e];return n}var Ei=to(Object.getPrototypeOf,Object),cu="[object Object]",uu=Function.prototype,hu=Object.prototype,no=uu.toString,fu=hu.hasOwnProperty,du=no.call(Object);function gu(n){if(!Ut(n)||Ke(n)!=cu)return!1;var t=Ei(n);if(t===null)return!0;var e=fu.call(t,"constructor")&&t.constructor;return typeof e=="function"&&e instanceof e&&no.call(e)==du}function pu(){this.__data__=new zt,this.size=0}function mu(n){var t=this.__data__,e=t.delete(n);return this.size=t.size,e}function bu(n){return this.__data__.get(n)}function yu(n){return this.__data__.has(n)}var vu=200;function Eu(n,t){var e=this.__data__;if(e instanceof zt){var r=e.__data__;if(!Nn||r.length<vu-1)return r.push([n,t]),this.size=++e.size,this;e=this.__data__=new be(r)}return e.set(n,t),this.size=e.size,this}function qt(n){var t=this.__data__=new zt(n);this.size=t.size}qt.prototype.clear=pu;qt.prototype.delete=mu;qt.prototype.get=bu;qt.prototype.has=yu;qt.prototype.set=Eu;function Au(n,t){return n&&On(t,vi(t),n)}function Nu(n,t){return n&&On(t,qn(t),n)}var ro=typeof exports=="object"&&exports&&!exports.nodeType&&exports,Zi=ro&&typeof module=="object"&&module&&!module.nodeType&&module,wu=Zi&&Zi.exports===ro,Xi=wu?kt.Buffer:void 0,Yi=Xi?Xi.allocUnsafe:void 0;function so(n,t){if(t)return n.slice();var e=n.length,r=Yi?Yi(e):new n.constructor(e);return n.copy(r),r}function Tu(n,t){for(var e=-1,r=n==null?0:n.length,s=0,i=[];++e<r;){var l=n[e];t(l,e,n)&&(i[s++]=l)}return i}function io(){return[]}var xu=Object.prototype,Lu=xu.propertyIsEnumerable,Qi=Object.getOwnPropertySymbols,Ai=Qi?function(n){return n==null?[]:(n=Object(n),Tu(Qi(n),function(t){return Lu.call(n,t)}))}:io;function Su(n,t){return On(n,Ai(n),t)}var _u=Object.getOwnPropertySymbols,lo=_u?function(n){for(var t=[];n;)eo(t,Ai(n)),n=Ei(n);return t}:io;function Ou(n,t){return On(n,lo(n),t)}function oo(n,t,e){var r=t(n);return fe(n)?r:eo(r,e(n))}function zs(n){return oo(n,vi,Ai)}function qu(n){return oo(n,qn,lo)}var Gs=me(kt,"DataView"),Ks=me(kt,"Promise"),Vs=me(kt,"Set"),Ji="[object Map]",Cu="[object Object]",tl="[object Promise]",el="[object Set]",nl="[object WeakMap]",rl="[object DataView]",Iu=pe(Gs),Ru=pe(Nn),ku=pe(Ks),Bu=pe(Vs),Mu=pe(Fs),yt=Ke;(Gs&&yt(new Gs(new ArrayBuffer(1)))!=rl||Nn&&yt(new Nn)!=Ji||Ks&&yt(Ks.resolve())!=tl||Vs&&yt(new Vs)!=el||Fs&&yt(new Fs)!=nl)&&(yt=function(n){var t=Ke(n),e=t==Cu?n.constructor:void 0,r=e?pe(e):"";if(r)switch(r){case Iu:return rl;case Ru:return Ji;case ku:return tl;case Bu:return el;case Mu:return nl}return t});var $u=Object.prototype,Du=$u.hasOwnProperty;function ju(n){var t=n.length,e=new n.constructor(t);return t&&typeof n[0]=="string"&&Du.call(n,"index")&&(e.index=n.index,e.input=n.input),e}var vr=kt.Uint8Array;function Ni(n){var t=new n.constructor(n.byteLength);return new vr(t).set(new vr(n)),t}function Pu(n,t){var e=t?Ni(n.buffer):n.buffer;return new n.constructor(e,n.byteOffset,n.byteLength)}var Uu=/\w*$/;function Fu(n){var t=new n.constructor(n.source,Uu.exec(n));return t.lastIndex=n.lastIndex,t}var sl=te?te.prototype:void 0,il=sl?sl.valueOf:void 0;function Hu(n){return il?Object(il.call(n)):{}}function ao(n,t){var e=t?Ni(n.buffer):n.buffer;return new n.constructor(e,n.byteOffset,n.length)}var zu="[object Boolean]",Gu="[object Date]",Ku="[object Map]",Vu="[object Number]",Wu="[object RegExp]",Zu="[object Set]",Xu="[object String]",Yu="[object Symbol]",Qu="[object ArrayBuffer]",Ju="[object DataView]",th="[object Float32Array]",eh="[object Float64Array]",nh="[object Int8Array]",rh="[object Int16Array]",sh="[object Int32Array]",ih="[object Uint8Array]",lh="[object Uint8ClampedArray]",oh="[object Uint16Array]",ah="[object Uint32Array]";function ch(n,t,e){var r=n.constructor;switch(t){case Qu:return Ni(n);case zu:case Gu:return new r(+n);case Ju:return Pu(n,e);case th:case eh:case nh:case rh:case sh:case ih:case lh:case oh:case ah:return ao(n,e);case Ku:return new r;case Vu:case Xu:return new r(n);case Wu:return Fu(n);case Zu:return new r;case Yu:return Hu(n)}}function co(n){return typeof n.constructor=="function"&&!mi(n)?qa(Ei(n)):{}}var uh="[object Map]";function hh(n){return Ut(n)&&yt(n)==uh}var ll=Ue&&Ue.isMap,fh=ll?bi(ll):hh,dh="[object Set]";function gh(n){return Ut(n)&&yt(n)==dh}var ol=Ue&&Ue.isSet,ph=ol?bi(ol):gh,mh=1,bh=2,yh=4,uo="[object Arguments]",vh="[object Array]",Eh="[object Boolean]",Ah="[object Date]",Nh="[object Error]",ho="[object Function]",wh="[object GeneratorFunction]",Th="[object Map]",xh="[object Number]",fo="[object Object]",Lh="[object RegExp]",Sh="[object Set]",_h="[object String]",Oh="[object Symbol]",qh="[object WeakMap]",Ch="[object ArrayBuffer]",Ih="[object DataView]",Rh="[object Float32Array]",kh="[object Float64Array]",Bh="[object Int8Array]",Mh="[object Int16Array]",$h="[object Int32Array]",Dh="[object Uint8Array]",jh="[object Uint8ClampedArray]",Ph="[object Uint16Array]",Uh="[object Uint32Array]",P={};P[uo]=P[vh]=P[Ch]=P[Ih]=P[Eh]=P[Ah]=P[Rh]=P[kh]=P[Bh]=P[Mh]=P[$h]=P[Th]=P[xh]=P[fo]=P[Lh]=P[Sh]=P[_h]=P[Oh]=P[Dh]=P[jh]=P[Ph]=P[Uh]=!0;P[Nh]=P[ho]=P[qh]=!1;function pr(n,t,e,r,s,i){var l,a=t&mh,c=t&bh,h=t&yh;if(l!==void 0)return l;if(!ee(n))return n;var d=fe(n);if(d){if(l=ju(n),!a)return Kl(n,l)}else{var m=yt(n),g=m==ho||m==wh;if(En(n))return so(n,a);if(m==fo||m==uo||g&&!s){if(l=c||g?{}:co(n),!a)return c?Ou(n,Nu(l,n)):Su(n,Au(l,n))}else{if(!P[m])return s?n:{};l=ch(n,m,a)}}i||(i=new qt);var p=i.get(n);if(p)return p;i.set(n,l),ph(n)?n.forEach(function(y){l.add(pr(y,t,e,y,n,i))}):fh(n)&&n.forEach(function(y,E){l.set(E,pr(y,t,e,E,n,i))});var b=h?c?qu:zs:c?qn:vi,v=d?void 0:b(n);return ja(v||n,function(y,E){v&&(E=y,y=n[E]),Wl(l,E,pr(y,t,e,E,n,i))}),l}var Fh=1,Hh=4;function De(n){return pr(n,Fh|Hh)}var zh="__lodash_hash_undefined__";function Gh(n){return this.__data__.set(n,zh),this}function Kh(n){return this.__data__.has(n)}function Er(n){var t=-1,e=n==null?0:n.length;for(this.__data__=new be;++t<e;)this.add(n[t])}Er.prototype.add=Er.prototype.push=Gh;Er.prototype.has=Kh;function Vh(n,t){for(var e=-1,r=n==null?0:n.length;++e<r;)if(t(n[e],e,n))return!0;return!1}function Wh(n,t){return n.has(t)}var Zh=1,Xh=2;function go(n,t,e,r,s,i){var l=e&Zh,a=n.length,c=t.length;if(a!=c&&!(l&&c>a))return!1;var h=i.get(n),d=i.get(t);if(h&&d)return h==t&&d==n;var m=-1,g=!0,p=e&Xh?new Er:void 0;for(i.set(n,t),i.set(t,n);++m<a;){var b=n[m],v=t[m];if(r)var y=l?r(v,b,m,t,n,i):r(b,v,m,n,t,i);if(y!==void 0){if(y)continue;g=!1;break}if(p){if(!Vh(t,function(E,w){if(!Wh(p,w)&&(b===E||s(b,E,e,r,i)))return p.push(w)})){g=!1;break}}else if(!(b===v||s(b,v,e,r,i))){g=!1;break}}return i.delete(n),i.delete(t),g}function Yh(n){var t=-1,e=Array(n.size);return n.forEach(function(r,s){e[++t]=[s,r]}),e}function Qh(n){var t=-1,e=Array(n.size);return n.forEach(function(r){e[++t]=r}),e}var Jh=1,tf=2,ef="[object Boolean]",nf="[object Date]",rf="[object Error]",sf="[object Map]",lf="[object Number]",of="[object RegExp]",af="[object Set]",cf="[object String]",uf="[object Symbol]",hf="[object ArrayBuffer]",ff="[object DataView]",al=te?te.prototype:void 0,Cs=al?al.valueOf:void 0;function df(n,t,e,r,s,i,l){switch(e){case ff:if(n.byteLength!=t.byteLength||n.byteOffset!=t.byteOffset)return!1;n=n.buffer,t=t.buffer;case hf:return!(n.byteLength!=t.byteLength||!i(new vr(n),new vr(t)));case ef:case nf:case lf:return _n(+n,+t);case rf:return n.name==t.name&&n.message==t.message;case of:case cf:return n==t+"";case sf:var a=Yh;case af:var c=r&Jh;if(a||(a=Qh),n.size!=t.size&&!c)return!1;var h=l.get(n);if(h)return h==t;r|=tf,l.set(n,t);var d=go(a(n),a(t),r,s,i,l);return l.delete(n),d;case uf:if(Cs)return Cs.call(n)==Cs.call(t)}return!1}var gf=1,pf=Object.prototype,mf=pf.hasOwnProperty;function bf(n,t,e,r,s,i){var l=e&gf,a=zs(n),c=a.length,h=zs(t),d=h.length;if(c!=d&&!l)return!1;for(var m=c;m--;){var g=a[m];if(!(l?g in t:mf.call(t,g)))return!1}var p=i.get(n),b=i.get(t);if(p&&b)return p==t&&b==n;var v=!0;i.set(n,t),i.set(t,n);for(var y=l;++m<c;){g=a[m];var E=n[g],w=t[g];if(r)var T=l?r(w,E,g,t,n,i):r(E,w,g,n,t,i);if(!(T===void 0?E===w||s(E,w,e,r,i):T)){v=!1;break}y||(y=g=="constructor")}if(v&&!y){var _=n.constructor,L=t.constructor;_!=L&&"constructor"in n&&"constructor"in t&&!(typeof _=="function"&&_ instanceof _&&typeof L=="function"&&L instanceof L)&&(v=!1)}return i.delete(n),i.delete(t),v}var yf=1,cl="[object Arguments]",ul="[object Array]",cr="[object Object]",vf=Object.prototype,hl=vf.hasOwnProperty;function Ef(n,t,e,r,s,i){var l=fe(n),a=fe(t),c=l?ul:yt(n),h=a?ul:yt(t);c=c==cl?cr:c,h=h==cl?cr:h;var d=c==cr,m=h==cr,g=c==h;if(g&&En(n)){if(!En(t))return!1;l=!0,d=!1}if(g&&!d)return i||(i=new qt),l||yi(n)?go(n,t,e,r,s,i):df(n,t,c,e,r,s,i);if(!(e&yf)){var p=d&&hl.call(n,"__wrapped__"),b=m&&hl.call(t,"__wrapped__");if(p||b){var v=p?n.value():n,y=b?t.value():t;return i||(i=new qt),s(v,y,e,r,i)}}return g?(i||(i=new qt),bf(n,t,e,r,s,i)):!1}function po(n,t,e,r,s){return n===t?!0:n==null||t==null||!Ut(n)&&!Ut(t)?n!==n&&t!==t:Ef(n,t,e,r,po,s)}function Af(n){return function(t,e,r){for(var s=-1,i=Object(t),l=r(t),a=l.length;a--;){var c=l[++s];if(e(i[c],c,i)===!1)break}return t}}var Nf=Af();function Ws(n,t,e){(e!==void 0&&!_n(n[t],e)||e===void 0&&!(t in n))&&pi(n,t,e)}function wf(n){return Ut(n)&&Tr(n)}function Zs(n,t){if(!(t==="constructor"&&typeof n[t]=="function")&&t!="__proto__")return n[t]}function Tf(n){return On(n,qn(n))}function xf(n,t,e,r,s,i,l){var a=Zs(n,e),c=Zs(t,e),h=l.get(c);if(h){Ws(n,e,h);return}var d=i?i(a,c,e+"",n,t,l):void 0,m=d===void 0;if(m){var g=fe(c),p=!g&&En(c),b=!g&&!p&&yi(c);d=c,g||p||b?fe(a)?d=a:wf(a)?d=Kl(a):p?(m=!1,d=so(c,!0)):b?(m=!1,d=ao(c,!0)):d=[]:gu(c)||Hs(c)?(d=a,Hs(a)?d=Tf(a):(!ee(a)||gi(a))&&(d=co(c))):m=!1}m&&(l.set(c,d),s(d,c,r,i,l),l.delete(c)),Ws(n,e,d)}function mo(n,t,e,r,s){n!==t&&Nf(t,function(i,l){if(s||(s=new qt),ee(i))xf(n,t,l,e,mo,r,s);else{var a=r?r(Zs(n,l),i,l+"",n,t,s):void 0;a===void 0&&(a=i),Ws(n,l,a)}},qn)}function wi(n,t){return po(n,t)}var Jt=Wa(function(n,t,e){mo(n,t,e)}),q=(n=>(n[n.TYPE=3]="TYPE",n[n.LEVEL=12]="LEVEL",n[n.ATTRIBUTE=13]="ATTRIBUTE",n[n.BLOT=14]="BLOT",n[n.INLINE=7]="INLINE",n[n.BLOCK=11]="BLOCK",n[n.BLOCK_BLOT=10]="BLOCK_BLOT",n[n.INLINE_BLOT=6]="INLINE_BLOT",n[n.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",n[n.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",n[n.ANY=15]="ANY",n))(q||{});class It{constructor(t,e,r={}){this.attrName=t,this.keyName=e;const s=q.TYPE&q.ATTRIBUTE;this.scope=r.scope!=null?r.scope&q.LEVEL|s:q.ATTRIBUTE,r.whitelist!=null&&(this.whitelist=r.whitelist)}static keys(t){return Array.from(t.attributes).map(e=>e.name)}add(t,e){return this.canAdd(t,e)?(t.setAttribute(this.keyName,e),!0):!1}canAdd(t,e){return this.whitelist==null?!0:typeof e=="string"?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1}remove(t){t.removeAttribute(this.keyName)}value(t){const e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""}}class je extends Error{constructor(t){t="[Parchment] "+t,super(t),this.message=t,this.name=this.constructor.name}}const bo=class Xs{constructor(){this.attributes={},this.classes={},this.tags={},this.types={}}static find(t,e=!1){if(t==null)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){let r=null;try{r=t.parentNode}catch{return null}return this.find(r,e)}return null}create(t,e,r){const s=this.query(e);if(s==null)throw new je(`Unable to create ${e} blot`);const i=s,l=e instanceof Node||e.nodeType===Node.TEXT_NODE?e:i.create(r),a=new i(t,l,r);return Xs.blots.set(a.domNode,a),a}find(t,e=!1){return Xs.find(t,e)}query(t,e=q.ANY){let r;return typeof t=="string"?r=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?r=this.types.text:typeof t=="number"?t&q.LEVEL&q.BLOCK?r=this.types.block:t&q.LEVEL&q.INLINE&&(r=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some(s=>(r=this.classes[s],!!r)),r=r||this.tags[t.tagName]),r==null?null:"scope"in r&&e&q.LEVEL&r.scope&&e&q.TYPE&r.scope?r:null}register(...t){return t.map(e=>{const r="blotName"in e,s="attrName"in e;if(!r&&!s)throw new je("Invalid definition");if(r&&e.blotName==="abstract")throw new je("Cannot register abstract class");const i=r?e.blotName:s?e.attrName:void 0;return this.types[i]=e,s?typeof e.keyName=="string"&&(this.attributes[e.keyName]=e):r&&(e.className&&(this.classes[e.className]=e),e.tagName&&(Array.isArray(e.tagName)?e.tagName=e.tagName.map(l=>l.toUpperCase()):e.tagName=e.tagName.toUpperCase(),(Array.isArray(e.tagName)?e.tagName:[e.tagName]).forEach(l=>{(this.tags[l]==null||e.className==null)&&(this.tags[l]=e)}))),e})}};bo.blots=new WeakMap;let Fe=bo;function fl(n,t){return(n.getAttribute("class")||"").split(/\s+/).filter(e=>e.indexOf(`${t}-`)===0)}class Lf extends It{static keys(t){return(t.getAttribute("class")||"").split(/\s+/).map(e=>e.split("-").slice(0,-1).join("-"))}add(t,e){return this.canAdd(t,e)?(this.remove(t),t.classList.add(`${this.keyName}-${e}`),!0):!1}remove(t){fl(t,this.keyName).forEach(e=>{t.classList.remove(e)}),t.classList.length===0&&t.removeAttribute("class")}value(t){const e=(fl(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""}}const Nt=Lf;function Is(n){const t=n.split("-"),e=t.slice(1).map(r=>r[0].toUpperCase()+r.slice(1)).join("");return t[0]+e}class Sf extends It{static keys(t){return(t.getAttribute("style")||"").split(";").map(e=>e.split(":")[0].trim())}add(t,e){return this.canAdd(t,e)?(t.style[Is(this.keyName)]=e,!0):!1}remove(t){t.style[Is(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")}value(t){const e=t.style[Is(this.keyName)];return this.canAdd(t,e)?e:""}}const ne=Sf;class _f{constructor(t){this.attributes={},this.domNode=t,this.build()}attribute(t,e){e?t.add(this.domNode,e)&&(t.value(this.domNode)!=null?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])}build(){this.attributes={};const t=Fe.find(this.domNode);if(t==null)return;const e=It.keys(this.domNode),r=Nt.keys(this.domNode),s=ne.keys(this.domNode);e.concat(r).concat(s).forEach(i=>{const l=t.scroll.query(i,q.ATTRIBUTE);l instanceof It&&(this.attributes[l.attrName]=l)})}copy(t){Object.keys(this.attributes).forEach(e=>{const r=this.attributes[e].value(this.domNode);t.format(e,r)})}move(t){this.copy(t),Object.keys(this.attributes).forEach(e=>{this.attributes[e].remove(this.domNode)}),this.attributes={}}values(){return Object.keys(this.attributes).reduce((t,e)=>(t[e]=this.attributes[e].value(this.domNode),t),{})}}const Sr=_f,yo=class{constructor(t,e){this.scroll=t,this.domNode=e,Fe.blots.set(e,this),this.prev=null,this.next=null}static create(t){if(this.tagName==null)throw new je("Blot definition missing tagName");let e,r;return Array.isArray(this.tagName)?(typeof t=="string"?(r=t.toUpperCase(),parseInt(r,10).toString()===r&&(r=parseInt(r,10))):typeof t=="number"&&(r=t),typeof r=="number"?e=document.createElement(this.tagName[r-1]):r&&this.tagName.indexOf(r)>-1?e=document.createElement(r):e=document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e}get statics(){return this.constructor}attach(){}clone(){const t=this.domNode.cloneNode(!1);return this.scroll.create(t)}detach(){this.parent!=null&&this.parent.removeChild(this),Fe.blots.delete(this.domNode)}deleteAt(t,e){this.isolate(t,e).remove()}formatAt(t,e,r,s){const i=this.isolate(t,e);if(this.scroll.query(r,q.BLOT)!=null&&s)i.wrap(r,s);else if(this.scroll.query(r,q.ATTRIBUTE)!=null){const l=this.scroll.create(this.statics.scope);i.wrap(l),l.format(r,s)}}insertAt(t,e,r){const s=r==null?this.scroll.create("text",e):this.scroll.create(e,r),i=this.split(t);this.parent.insertBefore(s,i||void 0)}isolate(t,e){const r=this.split(t);if(r==null)throw new Error("Attempt to isolate at end");return r.split(e),r}length(){return 1}offset(t=this.parent){return this.parent==null||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)}optimize(t){this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)&&this.wrap(this.statics.requiredContainer.blotName)}remove(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()}replaceWith(t,e){const r=typeof t=="string"?this.scroll.create(t,e):t;return this.parent!=null&&(this.parent.insertBefore(r,this.next||void 0),this.remove()),r}split(t,e){return t===0?this:this.next}update(t,e){}wrap(t,e){const r=typeof t=="string"?this.scroll.create(t,e):t;if(this.parent!=null&&this.parent.insertBefore(r,this.next||void 0),typeof r.appendChild!="function")throw new je(`Cannot wrap ${t}`);return r.appendChild(this),r}};yo.blotName="abstract";let vo=yo;const Eo=class extends vo{static value(t){return!0}index(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1}position(t,e){let r=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(r+=1),[this.parent.domNode,r]}value(){return{[this.statics.blotName]:this.statics.value(this.domNode)||!0}}};Eo.scope=q.INLINE_BLOT;let Of=Eo;const Q=Of;class qf{constructor(){this.head=null,this.tail=null,this.length=0}append(...t){if(this.insertBefore(t[0],null),t.length>1){const e=t.slice(1);this.append(...e)}}at(t){const e=this.iterator();let r=e();for(;r&&t>0;)t-=1,r=e();return r}contains(t){const e=this.iterator();let r=e();for(;r;){if(r===t)return!0;r=e()}return!1}indexOf(t){const e=this.iterator();let r=e(),s=0;for(;r;){if(r===t)return s;s+=1,r=e()}return-1}insertBefore(t,e){t!=null&&(this.remove(t),t.next=e,e!=null?(t.prev=e.prev,e.prev!=null&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):this.tail!=null?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)}offset(t){let e=0,r=this.head;for(;r!=null;){if(r===t)return e;e+=r.length(),r=r.next}return-1}remove(t){this.contains(t)&&(t.prev!=null&&(t.prev.next=t.next),t.next!=null&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)}iterator(t=this.head){return()=>{const e=t;return t!=null&&(t=t.next),e}}find(t,e=!1){const r=this.iterator();let s=r();for(;s;){const i=s.length();if(t<i||e&&t===i&&(s.next==null||s.next.length()!==0))return[s,t];t-=i,s=r()}return[null,0]}forEach(t){const e=this.iterator();let r=e();for(;r;)t(r),r=e()}forEachAt(t,e,r){if(e<=0)return;const[s,i]=this.find(t);let l=t-i;const a=this.iterator(s);let c=a();for(;c&&l<t+e;){const h=c.length();t>l?r(c,t-l,Math.min(e,l+h-t)):r(c,0,Math.min(h,t+e-l)),l+=h,c=a()}}map(t){return this.reduce((e,r)=>(e.push(t(r)),e),[])}reduce(t,e){const r=this.iterator();let s=r();for(;s;)e=t(e,s),s=r();return e}}function dl(n,t){const e=t.find(n);if(e)return e;try{return t.create(n)}catch{const r=t.create(q.INLINE);return Array.from(n.childNodes).forEach(s=>{r.domNode.appendChild(s)}),n.parentNode&&n.parentNode.replaceChild(r.domNode,n),r.attach(),r}}const Ao=class Zt extends vo{constructor(t,e){super(t,e),this.uiNode=null,this.build()}appendChild(t){this.insertBefore(t)}attach(){super.attach(),this.children.forEach(t=>{t.attach()})}attachUI(t){this.uiNode!=null&&this.uiNode.remove(),this.uiNode=t,Zt.uiClass&&this.uiNode.classList.add(Zt.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)}build(){this.children=new qf,Array.from(this.domNode.childNodes).filter(t=>t!==this.uiNode).reverse().forEach(t=>{try{const e=dl(t,this.scroll);this.insertBefore(e,this.children.head||void 0)}catch(e){if(e instanceof je)return;throw e}})}deleteAt(t,e){if(t===0&&e===this.length())return this.remove();this.children.forEachAt(t,e,(r,s,i)=>{r.deleteAt(s,i)})}descendant(t,e=0){const[r,s]=this.children.find(e);return t.blotName==null&&t(r)||t.blotName!=null&&r instanceof t?[r,s]:r instanceof Zt?r.descendant(t,s):[null,-1]}descendants(t,e=0,r=Number.MAX_VALUE){let s=[],i=r;return this.children.forEachAt(e,r,(l,a,c)=>{(t.blotName==null&&t(l)||t.blotName!=null&&l instanceof t)&&s.push(l),l instanceof Zt&&(s=s.concat(l.descendants(t,a,i))),i-=c}),s}detach(){this.children.forEach(t=>{t.detach()}),super.detach()}enforceAllowedChildren(){let t=!1;this.children.forEach(e=>{t||this.statics.allowedChildren.some(r=>e instanceof r)||(e.statics.scope===q.BLOCK_BLOT?(e.next!=null&&this.splitAfter(e),e.prev!=null&&this.splitAfter(e.prev),e.parent.unwrap(),t=!0):e instanceof Zt?e.unwrap():e.remove())})}formatAt(t,e,r,s){this.children.forEachAt(t,e,(i,l,a)=>{i.formatAt(l,a,r,s)})}insertAt(t,e,r){const[s,i]=this.children.find(t);if(s)s.insertAt(i,e,r);else{const l=r==null?this.scroll.create("text",e):this.scroll.create(e,r);this.appendChild(l)}}insertBefore(t,e){t.parent!=null&&t.parent.children.remove(t);let r=null;this.children.insertBefore(t,e||null),t.parent=this,e!=null&&(r=e.domNode),(this.domNode.parentNode!==t.domNode||this.domNode.nextSibling!==r)&&this.domNode.insertBefore(t.domNode,r),t.attach()}length(){return this.children.reduce((t,e)=>t+e.length(),0)}moveChildren(t,e){this.children.forEach(r=>{t.insertBefore(r,e)})}optimize(t){if(super.optimize(t),this.enforceAllowedChildren(),this.uiNode!=null&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),this.children.length===0)if(this.statics.defaultChild!=null){const e=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(e)}else this.remove()}path(t,e=!1){const[r,s]=this.children.find(t,e),i=[[this,t]];return r instanceof Zt?i.concat(r.path(s,e)):(r!=null&&i.push([r,s]),i)}removeChild(t){this.children.remove(t)}replaceWith(t,e){const r=typeof t=="string"?this.scroll.create(t,e):t;return r instanceof Zt&&this.moveChildren(r),super.replaceWith(r)}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const r=this.clone();return this.parent&&this.parent.insertBefore(r,this.next||void 0),this.children.forEachAt(t,this.length(),(s,i,l)=>{const a=s.split(i,e);a!=null&&r.appendChild(a)}),r}splitAfter(t){const e=this.clone();for(;t.next!=null;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e}unwrap(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()}update(t,e){const r=[],s=[];t.forEach(i=>{i.target===this.domNode&&i.type==="childList"&&(r.push(...i.addedNodes),s.push(...i.removedNodes))}),s.forEach(i=>{if(i.parentNode!=null&&i.tagName!=="IFRAME"&&document.body.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY)return;const l=this.scroll.find(i);l!=null&&(l.domNode.parentNode==null||l.domNode.parentNode===this.domNode)&&l.detach()}),r.filter(i=>i.parentNode===this.domNode&&i!==this.uiNode).sort((i,l)=>i===l?0:i.compareDocumentPosition(l)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1).forEach(i=>{let l=null;i.nextSibling!=null&&(l=this.scroll.find(i.nextSibling));const a=dl(i,this.scroll);(a.next!==l||a.next==null)&&(a.parent!=null&&a.parent.removeChild(this),this.insertBefore(a,l||void 0))}),this.enforceAllowedChildren()}};Ao.uiClass="";let Cf=Ao;const Et=Cf;function If(n,t){if(Object.keys(n).length!==Object.keys(t).length)return!1;for(const e in n)if(n[e]!==t[e])return!1;return!0}const Ie=class Re extends Et{static create(t){return super.create(t)}static formats(t,e){const r=e.query(Re.blotName);if(!(r!=null&&t.tagName===r.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Sr(this.domNode)}format(t,e){if(t===this.statics.blotName&&!e)this.children.forEach(r=>{r instanceof Re||(r=r.wrap(Re.blotName,!0)),this.attributes.copy(r)}),this.unwrap();else{const r=this.scroll.query(t,q.INLINE);if(r==null)return;r instanceof It?this.attributes.attribute(r,e):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e)}}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,r,s){this.formats()[r]!=null||this.scroll.query(r,q.ATTRIBUTE)?this.isolate(t,e).format(r,s):super.formatAt(t,e,r,s)}optimize(t){super.optimize(t);const e=this.formats();if(Object.keys(e).length===0)return this.unwrap();const r=this.next;r instanceof Re&&r.prev===this&&If(e,r.formats())&&(r.moveChildren(this),r.remove())}replaceWith(t,e){const r=super.replaceWith(t,e);return this.attributes.copy(r),r}update(t,e){super.update(t,e),t.some(r=>r.target===this.domNode&&r.type==="attributes")&&this.attributes.build()}wrap(t,e){const r=super.wrap(t,e);return r instanceof Re&&this.attributes.move(r),r}};Ie.allowedChildren=[Ie,Q],Ie.blotName="inline",Ie.scope=q.INLINE_BLOT,Ie.tagName="SPAN";let Rf=Ie;const Ti=Rf,ke=class Ys extends Et{static create(t){return super.create(t)}static formats(t,e){const r=e.query(Ys.blotName);if(!(r!=null&&t.tagName===r.tagName)){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()}}constructor(t,e){super(t,e),this.attributes=new Sr(this.domNode)}format(t,e){const r=this.scroll.query(t,q.BLOCK);r!=null&&(r instanceof It?this.attributes.attribute(r,e):t===this.statics.blotName&&!e?this.replaceWith(Ys.blotName):e&&(t!==this.statics.blotName||this.formats()[t]!==e)&&this.replaceWith(t,e))}formats(){const t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return e!=null&&(t[this.statics.blotName]=e),t}formatAt(t,e,r,s){this.scroll.query(r,q.BLOCK)!=null?this.format(r,s):super.formatAt(t,e,r,s)}insertAt(t,e,r){if(r==null||this.scroll.query(e,q.INLINE)!=null)super.insertAt(t,e,r);else{const s=this.split(t);if(s!=null){const i=this.scroll.create(e,r);s.parent.insertBefore(i,s)}else throw new Error("Attempt to insertAt after block boundaries")}}replaceWith(t,e){const r=super.replaceWith(t,e);return this.attributes.copy(r),r}update(t,e){super.update(t,e),t.some(r=>r.target===this.domNode&&r.type==="attributes")&&this.attributes.build()}};ke.blotName="block",ke.scope=q.BLOCK_BLOT,ke.tagName="P",ke.allowedChildren=[Ti,ke,Q];let kf=ke;const wn=kf,Qs=class extends Et{checkMerge(){return this.next!==null&&this.next.statics.blotName===this.statics.blotName}deleteAt(t,e){super.deleteAt(t,e),this.enforceAllowedChildren()}formatAt(t,e,r,s){super.formatAt(t,e,r,s),this.enforceAllowedChildren()}insertAt(t,e,r){super.insertAt(t,e,r),this.enforceAllowedChildren()}optimize(t){super.optimize(t),this.children.length>0&&this.next!=null&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())}};Qs.blotName="container",Qs.scope=q.BLOCK_BLOT;let Bf=Qs;const _r=Bf;class Mf extends Q{static formats(t,e){}format(t,e){super.formatAt(0,this.length(),t,e)}formatAt(t,e,r,s){t===0&&e===this.length()?this.format(r,s):super.formatAt(t,e,r,s)}formats(){return this.statics.formats(this.domNode,this.scroll)}}const lt=Mf,$f={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},Df=100,Be=class extends Et{constructor(t,e){super(null,e),this.registry=t,this.scroll=this,this.build(),this.observer=new MutationObserver(r=>{this.update(r)}),this.observer.observe(this.domNode,$f),this.attach()}create(t,e){return this.registry.create(this,t,e)}find(t,e=!1){const r=this.registry.find(t,e);return r?r.scroll===this?r:e?this.find(r.scroll.domNode.parentNode,!0):null:null}query(t,e=q.ANY){return this.registry.query(t,e)}register(...t){return this.registry.register(...t)}build(){this.scroll!=null&&super.build()}detach(){super.detach(),this.observer.disconnect()}deleteAt(t,e){this.update(),t===0&&e===this.length()?this.children.forEach(r=>{r.remove()}):super.deleteAt(t,e)}formatAt(t,e,r,s){this.update(),super.formatAt(t,e,r,s)}insertAt(t,e,r){this.update(),super.insertAt(t,e,r)}optimize(t=[],e={}){super.optimize(e);const r=e.mutationsMap||new WeakMap;let s=Array.from(this.observer.takeRecords());for(;s.length>0;)t.push(s.pop());const i=(c,h=!0)=>{c==null||c===this||c.domNode.parentNode!=null&&(r.has(c.domNode)||r.set(c.domNode,[]),h&&i(c.parent))},l=c=>{r.has(c.domNode)&&(c instanceof Et&&c.children.forEach(l),r.delete(c.domNode),c.optimize(e))};let a=t;for(let c=0;a.length>0;c+=1){if(c>=Df)throw new Error("[Parchment] Maximum optimize iterations reached");for(a.forEach(h=>{const d=this.find(h.target,!0);d!=null&&(d.domNode===h.target&&(h.type==="childList"?(i(this.find(h.previousSibling,!1)),Array.from(h.addedNodes).forEach(m=>{const g=this.find(m,!1);i(g,!1),g instanceof Et&&g.children.forEach(p=>{i(p,!1)})})):h.type==="attributes"&&i(d.prev)),i(d))}),this.children.forEach(l),a=Array.from(this.observer.takeRecords()),s=a.slice();s.length>0;)t.push(s.pop())}}update(t,e={}){t=t||this.observer.takeRecords();const r=new WeakMap;t.map(s=>{const i=this.find(s.target,!0);return i==null?null:r.has(i.domNode)?(r.get(i.domNode).push(s),null):(r.set(i.domNode,[s]),i)}).forEach(s=>{s!=null&&s!==this&&r.has(s.domNode)&&s.update(r.get(s.domNode)||[],e)}),e.mutationsMap=r,r.has(this.domNode)&&super.update(r.get(this.domNode),e),this.optimize(t,e)}};Be.blotName="scroll",Be.defaultChild=wn,Be.allowedChildren=[wn,_r],Be.scope=q.BLOCK_BLOT,Be.tagName="DIV";let jf=Be;const xi=jf,Js=class No extends Q{static create(t){return document.createTextNode(t)}static value(t){return t.data}constructor(t,e){super(t,e),this.text=this.statics.value(this.domNode)}deleteAt(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)}index(t,e){return this.domNode===t?e:-1}insertAt(t,e,r){r==null?(this.text=this.text.slice(0,t)+e+this.text.slice(t),this.domNode.data=this.text):super.insertAt(t,e,r)}length(){return this.text.length}optimize(t){super.optimize(t),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof No&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())}position(t,e=!1){return[this.domNode,t]}split(t,e=!1){if(!e){if(t===0)return this;if(t===this.length())return this.next}const r=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(r,this.next||void 0),this.text=this.statics.value(this.domNode),r}update(t,e){t.some(r=>r.type==="characterData"&&r.target===this.domNode)&&(this.text=this.statics.value(this.domNode))}value(){return this.text}};Js.blotName="text",Js.scope=q.INLINE_BLOT;let Pf=Js;const Ar=Pf,Uf=Object.freeze(Object.defineProperty({__proto__:null,Attributor:It,AttributorStore:Sr,BlockBlot:wn,ClassAttributor:Nt,ContainerBlot:_r,EmbedBlot:lt,InlineBlot:Ti,LeafBlot:Q,ParentBlot:Et,Registry:Fe,Scope:q,ScrollBlot:xi,StyleAttributor:ne,TextBlot:Ar},Symbol.toStringTag,{value:"Module"}));var ti={exports:{}},it=-1,et=1,G=0;function Tn(n,t,e,r,s){if(n===t)return n?[[G,n]]:[];if(e!=null){var i=Xf(n,t,e);if(i)return i}var l=Li(n,t),a=n.substring(0,l);n=n.substring(l),t=t.substring(l),l=Or(n,t);var c=n.substring(n.length-l);n=n.substring(0,n.length-l),t=t.substring(0,t.length-l);var h=Ff(n,t);return a&&h.unshift([G,a]),c&&h.push([G,c]),Si(h,s),r&&Gf(h),h}function Ff(n,t){var e;if(!n)return[[et,t]];if(!t)return[[it,n]];var r=n.length>t.length?n:t,s=n.length>t.length?t:n,i=r.indexOf(s);if(i!==-1)return e=[[et,r.substring(0,i)],[G,s],[et,r.substring(i+s.length)]],n.length>t.length&&(e[0][0]=e[2][0]=it),e;if(s.length===1)return[[it,n],[et,t]];var l=zf(n,t);if(l){var a=l[0],c=l[1],h=l[2],d=l[3],m=l[4],g=Tn(a,h),p=Tn(c,d);return g.concat([[G,m]],p)}return Hf(n,t)}function Hf(n,t){for(var e=n.length,r=t.length,s=Math.ceil((e+r)/2),i=s,l=2*s,a=new Array(l),c=new Array(l),h=0;h<l;h++)a[h]=-1,c[h]=-1;a[i+1]=0,c[i+1]=0;for(var d=e-r,m=d%2!==0,g=0,p=0,b=0,v=0,y=0;y<s;y++){for(var E=-y+g;E<=y-p;E+=2){var w=i+E,T;E===-y||E!==y&&a[w-1]<a[w+1]?T=a[w+1]:T=a[w-1]+1;for(var _=T-E;T<e&&_<r&&n.charAt(T)===t.charAt(_);)T++,_++;if(a[w]=T,T>e)p+=2;else if(_>r)g+=2;else if(m){var L=i+d-E;if(L>=0&&L<l&&c[L]!==-1){var I=e-c[L];if(T>=I)return gl(n,t,T,_)}}}for(var k=-y+b;k<=y-v;k+=2){var L=i+k,I;k===-y||k!==y&&c[L-1]<c[L+1]?I=c[L+1]:I=c[L-1]+1;for(var U=I-k;I<e&&U<r&&n.charAt(e-I-1)===t.charAt(r-U-1);)I++,U++;if(c[L]=I,I>e)v+=2;else if(U>r)b+=2;else if(!m){var w=i+d-k;if(w>=0&&w<l&&a[w]!==-1){var T=a[w],_=i+T-w;if(I=e-I,T>=I)return gl(n,t,T,_)}}}}return[[it,n],[et,t]]}function gl(n,t,e,r){var s=n.substring(0,e),i=t.substring(0,r),l=n.substring(e),a=t.substring(r),c=Tn(s,i),h=Tn(l,a);return c.concat(h)}function Li(n,t){if(!n||!t||n.charAt(0)!==t.charAt(0))return 0;for(var e=0,r=Math.min(n.length,t.length),s=r,i=0;e<s;)n.substring(i,s)==t.substring(i,s)?(e=s,i=e):r=s,s=Math.floor((r-e)/2+e);return wo(n.charCodeAt(s-1))&&s--,s}function pl(n,t){var e=n.length,r=t.length;if(e==0||r==0)return 0;e>r?n=n.substring(e-r):e<r&&(t=t.substring(0,e));var s=Math.min(e,r);if(n==t)return s;for(var i=0,l=1;;){var a=n.substring(s-l),c=t.indexOf(a);if(c==-1)return i;l+=c,(c==0||n.substring(s-l)==t.substring(0,l))&&(i=l,l++)}}function Or(n,t){if(!n||!t||n.slice(-1)!==t.slice(-1))return 0;for(var e=0,r=Math.min(n.length,t.length),s=r,i=0;e<s;)n.substring(n.length-s,n.length-i)==t.substring(t.length-s,t.length-i)?(e=s,i=e):r=s,s=Math.floor((r-e)/2+e);return To(n.charCodeAt(n.length-s))&&s--,s}function zf(n,t){var e=n.length>t.length?n:t,r=n.length>t.length?t:n;if(e.length<4||r.length*2<e.length)return null;function s(p,b,v){for(var y=p.substring(v,v+Math.floor(p.length/4)),E=-1,w="",T,_,L,I;(E=b.indexOf(y,E+1))!==-1;){var k=Li(p.substring(v),b.substring(E)),U=Or(p.substring(0,v),b.substring(0,E));w.length<U+k&&(w=b.substring(E-U,E)+b.substring(E,E+k),T=p.substring(0,v-U),_=p.substring(v+k),L=b.substring(0,E-U),I=b.substring(E+k))}return w.length*2>=p.length?[T,_,L,I,w]:null}var i=s(e,r,Math.ceil(e.length/4)),l=s(e,r,Math.ceil(e.length/2)),a;if(!i&&!l)return null;l?i?a=i[4].length>l[4].length?i:l:a=l:a=i;var c,h,d,m;n.length>t.length?(c=a[0],h=a[1],d=a[2],m=a[3]):(d=a[0],m=a[1],c=a[2],h=a[3]);var g=a[4];return[c,h,d,m,g]}function Gf(n){for(var t=!1,e=[],r=0,s=null,i=0,l=0,a=0,c=0,h=0;i<n.length;)n[i][0]==G?(e[r++]=i,l=c,a=h,c=0,h=0,s=n[i][1]):(n[i][0]==et?c+=n[i][1].length:h+=n[i][1].length,s&&s.length<=Math.max(l,a)&&s.length<=Math.max(c,h)&&(n.splice(e[r-1],0,[it,s]),n[e[r-1]+1][0]=et,r--,r--,i=r>0?e[r-1]:-1,l=0,a=0,c=0,h=0,s=null,t=!0)),i++;for(t&&Si(n),Wf(n),i=1;i<n.length;){if(n[i-1][0]==it&&n[i][0]==et){var d=n[i-1][1],m=n[i][1],g=pl(d,m),p=pl(m,d);g>=p?(g>=d.length/2||g>=m.length/2)&&(n.splice(i,0,[G,m.substring(0,g)]),n[i-1][1]=d.substring(0,d.length-g),n[i+1][1]=m.substring(g),i++):(p>=d.length/2||p>=m.length/2)&&(n.splice(i,0,[G,d.substring(0,p)]),n[i-1][0]=et,n[i-1][1]=m.substring(0,m.length-p),n[i+1][0]=it,n[i+1][1]=d.substring(p),i++),i++}i++}}var ml=/[^a-zA-Z0-9]/,bl=/\s/,yl=/[\r\n]/,Kf=/\n\r?\n$/,Vf=/^\r?\n\r?\n/;function Wf(n){function t(p,b){if(!p||!b)return 6;var v=p.charAt(p.length-1),y=b.charAt(0),E=v.match(ml),w=y.match(ml),T=E&&v.match(bl),_=w&&y.match(bl),L=T&&v.match(yl),I=_&&y.match(yl),k=L&&p.match(Kf),U=I&&b.match(Vf);return k||U?5:L||I?4:E&&!T&&_?3:T||_?2:E||w?1:0}for(var e=1;e<n.length-1;){if(n[e-1][0]==G&&n[e+1][0]==G){var r=n[e-1][1],s=n[e][1],i=n[e+1][1],l=Or(r,s);if(l){var a=s.substring(s.length-l);r=r.substring(0,r.length-l),s=a+s.substring(0,s.length-l),i=a+i}for(var c=r,h=s,d=i,m=t(r,s)+t(s,i);s.charAt(0)===i.charAt(0);){r+=s.charAt(0),s=s.substring(1)+i.charAt(0),i=i.substring(1);var g=t(r,s)+t(s,i);g>=m&&(m=g,c=r,h=s,d=i)}n[e-1][1]!=c&&(c?n[e-1][1]=c:(n.splice(e-1,1),e--),n[e][1]=h,d?n[e+1][1]=d:(n.splice(e+1,1),e--))}e++}}function Si(n,t){n.push([G,""]);for(var e=0,r=0,s=0,i="",l="",a;e<n.length;){if(e<n.length-1&&!n[e][1]){n.splice(e,1);continue}switch(n[e][0]){case et:s++,l+=n[e][1],e++;break;case it:r++,i+=n[e][1],e++;break;case G:var c=e-s-r-1;if(t){if(c>=0&&Lo(n[c][1])){var h=n[c][1].slice(-1);if(n[c][1]=n[c][1].slice(0,-1),i=h+i,l=h+l,!n[c][1]){n.splice(c,1),e--;var d=c-1;n[d]&&n[d][0]===et&&(s++,l=n[d][1]+l,d--),n[d]&&n[d][0]===it&&(r++,i=n[d][1]+i,d--),c=d}}if(xo(n[e][1])){var h=n[e][1].charAt(0);n[e][1]=n[e][1].slice(1),i+=h,l+=h}}if(e<n.length-1&&!n[e][1]){n.splice(e,1);break}if(i.length>0||l.length>0){i.length>0&&l.length>0&&(a=Li(l,i),a!==0&&(c>=0?n[c][1]+=l.substring(0,a):(n.splice(0,0,[G,l.substring(0,a)]),e++),l=l.substring(a),i=i.substring(a)),a=Or(l,i),a!==0&&(n[e][1]=l.substring(l.length-a)+n[e][1],l=l.substring(0,l.length-a),i=i.substring(0,i.length-a)));var m=s+r;i.length===0&&l.length===0?(n.splice(e-m,m),e=e-m):i.length===0?(n.splice(e-m,m,[et,l]),e=e-m+1):l.length===0?(n.splice(e-m,m,[it,i]),e=e-m+1):(n.splice(e-m,m,[it,i],[et,l]),e=e-m+2)}e!==0&&n[e-1][0]===G?(n[e-1][1]+=n[e][1],n.splice(e,1)):e++,s=0,r=0,i="",l="";break}}n[n.length-1][1]===""&&n.pop();var g=!1;for(e=1;e<n.length-1;)n[e-1][0]===G&&n[e+1][0]===G&&(n[e][1].substring(n[e][1].length-n[e-1][1].length)===n[e-1][1]?(n[e][1]=n[e-1][1]+n[e][1].substring(0,n[e][1].length-n[e-1][1].length),n[e+1][1]=n[e-1][1]+n[e+1][1],n.splice(e-1,1),g=!0):n[e][1].substring(0,n[e+1][1].length)==n[e+1][1]&&(n[e-1][1]+=n[e+1][1],n[e][1]=n[e][1].substring(n[e+1][1].length)+n[e+1][1],n.splice(e+1,1),g=!0)),e++;g&&Si(n,t)}function wo(n){return n>=55296&&n<=56319}function To(n){return n>=56320&&n<=57343}function xo(n){return To(n.charCodeAt(0))}function Lo(n){return wo(n.charCodeAt(n.length-1))}function Zf(n){for(var t=[],e=0;e<n.length;e++)n[e][1].length>0&&t.push(n[e]);return t}function Rs(n,t,e,r){return Lo(n)||xo(r)?null:Zf([[G,n],[it,t],[et,e],[G,r]])}function Xf(n,t,e){var r=typeof e=="number"?{index:e,length:0}:e.oldRange,s=typeof e=="number"?null:e.newRange,i=n.length,l=t.length;if(r.length===0&&(s===null||s.length===0)){var a=r.index,c=n.slice(0,a),h=n.slice(a),d=s?s.index:null;t:{var m=a+l-i;if(d!==null&&d!==m||m<0||m>l)break t;var g=t.slice(0,m),p=t.slice(m);if(p!==h)break t;var b=Math.min(a,m),v=c.slice(0,b),y=g.slice(0,b);if(v!==y)break t;var E=c.slice(b),w=g.slice(b);return Rs(v,E,w,h)}t:{if(d!==null&&d!==a)break t;var T=a,g=t.slice(0,T),p=t.slice(T);if(g!==c)break t;var _=Math.min(i-T,l-T),L=h.slice(h.length-_),I=p.slice(p.length-_);if(L!==I)break t;var E=h.slice(0,h.length-_),w=p.slice(0,p.length-_);return Rs(c,E,w,L)}}if(r.length>0&&s&&s.length===0)t:{var v=n.slice(0,r.index),L=n.slice(r.index+r.length),b=v.length,_=L.length;if(l<b+_)break t;var y=t.slice(0,b),I=t.slice(l-_);if(v!==y||L!==I)break t;var E=n.slice(b,i-_),w=t.slice(b,l-_);return Rs(v,E,w,L)}return null}function qr(n,t,e,r){return Tn(n,t,e,r,!0)}qr.INSERT=et;qr.DELETE=it;qr.EQUAL=G;var Yf=qr,Nr={exports:{}};Nr.exports;(function(n,t){var e=200,r="__lodash_hash_undefined__",s=9007199254740991,i="[object Arguments]",l="[object Array]",a="[object Boolean]",c="[object Date]",h="[object Error]",d="[object Function]",m="[object GeneratorFunction]",g="[object Map]",p="[object Number]",b="[object Object]",v="[object Promise]",y="[object RegExp]",E="[object Set]",w="[object String]",T="[object Symbol]",_="[object WeakMap]",L="[object ArrayBuffer]",I="[object DataView]",k="[object Float32Array]",U="[object Float64Array]",Bt="[object Int8Array]",Kt="[object Int16Array]",re="[object Int32Array]",se="[object Uint8Array]",Bn="[object Uint8ClampedArray]",Mn="[object Uint16Array]",$n="[object Uint32Array]",Br=/[\\^$.*+?()[\]{}|]/g,Mr=/\w*$/,$r=/^\[object .+?Constructor\]$/,Dr=/^(?:0|[1-9]\d*)$/,D={};D[i]=D[l]=D[L]=D[I]=D[a]=D[c]=D[k]=D[U]=D[Bt]=D[Kt]=D[re]=D[g]=D[p]=D[b]=D[y]=D[E]=D[w]=D[T]=D[se]=D[Bn]=D[Mn]=D[$n]=!0,D[h]=D[d]=D[_]=!1;var jr=typeof Xt=="object"&&Xt&&Xt.Object===Object&&Xt,Pr=typeof self=="object"&&self&&self.Object===Object&&self,ft=jr||Pr||Function("return this")(),Dn=t&&!t.nodeType&&t,j=Dn&&!0&&n&&!n.nodeType&&n,jn=j&&j.exports===Dn;function Ur(o,u){return o.set(u[0],u[1]),o}function dt(o,u){return o.add(u),o}function Pn(o,u){for(var f=-1,A=o?o.length:0;++f<A&&u(o[f],f,o)!==!1;);return o}function Un(o,u){for(var f=-1,A=u.length,R=o.length;++f<A;)o[R+f]=u[f];return o}function Ve(o,u,f,A){for(var R=-1,C=o?o.length:0;++R<C;)f=u(f,o[R],R,o);return f}function We(o,u){for(var f=-1,A=Array(o);++f<o;)A[f]=u(f);return A}function Fn(o,u){return o==null?void 0:o[u]}function Ze(o){var u=!1;if(o!=null&&typeof o.toString!="function")try{u=!!(o+"")}catch{}return u}function Hn(o){var u=-1,f=Array(o.size);return o.forEach(function(A,R){f[++u]=[R,A]}),f}function Xe(o,u){return function(f){return o(u(f))}}function zn(o){var u=-1,f=Array(o.size);return o.forEach(function(A){f[++u]=A}),f}var Fr=Array.prototype,Hr=Function.prototype,Ae=Object.prototype,Ye=ft["__core-js_shared__"],Gn=function(){var o=/[^.]+$/.exec(Ye&&Ye.keys&&Ye.keys.IE_PROTO||"");return o?"Symbol(src)_1."+o:""}(),Kn=Hr.toString,xt=Ae.hasOwnProperty,Ne=Ae.toString,zr=RegExp("^"+Kn.call(xt).replace(Br,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ie=jn?ft.Buffer:void 0,we=ft.Symbol,Qe=ft.Uint8Array,ot=Xe(Object.getPrototypeOf,Object),Vn=Object.create,Wn=Ae.propertyIsEnumerable,Gr=Fr.splice,Je=Object.getOwnPropertySymbols,Te=ie?ie.isBuffer:void 0,Zn=Xe(Object.keys,Object),xe=pt(ft,"DataView"),le=pt(ft,"Map"),gt=pt(ft,"Promise"),Le=pt(ft,"Set"),tn=pt(ft,"WeakMap"),oe=pt(Object,"create"),en=tt(xe),ae=tt(le),nn=tt(gt),rn=tt(Le),sn=tt(tn),Vt=we?we.prototype:void 0,Xn=Vt?Vt.valueOf:void 0;function Mt(o){var u=-1,f=o?o.length:0;for(this.clear();++u<f;){var A=o[u];this.set(A[0],A[1])}}function Kr(){this.__data__=oe?oe(null):{}}function Vr(o){return this.has(o)&&delete this.__data__[o]}function Wr(o){var u=this.__data__;if(oe){var f=u[o];return f===r?void 0:f}return xt.call(u,o)?u[o]:void 0}function Yn(o){var u=this.__data__;return oe?u[o]!==void 0:xt.call(u,o)}function ln(o,u){var f=this.__data__;return f[o]=oe&&u===void 0?r:u,this}Mt.prototype.clear=Kr,Mt.prototype.delete=Vr,Mt.prototype.get=Wr,Mt.prototype.has=Yn,Mt.prototype.set=ln;function K(o){var u=-1,f=o?o.length:0;for(this.clear();++u<f;){var A=o[u];this.set(A[0],A[1])}}function Zr(){this.__data__=[]}function Xr(o){var u=this.__data__,f=_e(u,o);if(f<0)return!1;var A=u.length-1;return f==A?u.pop():Gr.call(u,f,1),!0}function Yr(o){var u=this.__data__,f=_e(u,o);return f<0?void 0:u[f][1]}function Qr(o){return _e(this.__data__,o)>-1}function Jr(o,u){var f=this.__data__,A=_e(f,o);return A<0?f.push([o,u]):f[A][1]=u,this}K.prototype.clear=Zr,K.prototype.delete=Xr,K.prototype.get=Yr,K.prototype.has=Qr,K.prototype.set=Jr;function Z(o){var u=-1,f=o?o.length:0;for(this.clear();++u<f;){var A=o[u];this.set(A[0],A[1])}}function ts(){this.__data__={hash:new Mt,map:new(le||K),string:new Mt}}function es(o){return ue(this,o).delete(o)}function ns(o){return ue(this,o).get(o)}function rs(o){return ue(this,o).has(o)}function ss(o,u){return ue(this,o).set(o,u),this}Z.prototype.clear=ts,Z.prototype.delete=es,Z.prototype.get=ns,Z.prototype.has=rs,Z.prototype.set=ss;function rt(o){this.__data__=new K(o)}function is(){this.__data__=new K}function ls(o){return this.__data__.delete(o)}function os(o){return this.__data__.get(o)}function as(o){return this.__data__.has(o)}function cs(o,u){var f=this.__data__;if(f instanceof K){var A=f.__data__;if(!le||A.length<e-1)return A.push([o,u]),this;f=this.__data__=new Z(A)}return f.set(o,u),this}rt.prototype.clear=is,rt.prototype.delete=ls,rt.prototype.get=os,rt.prototype.has=as,rt.prototype.set=cs;function Se(o,u){var f=un(o)||qe(o)?We(o.length,String):[],A=f.length,R=!!A;for(var C in o)xt.call(o,C)&&!(R&&(C=="length"||ws(C,A)))&&f.push(C);return f}function Qn(o,u,f){var A=o[u];(!(xt.call(o,u)&&rr(A,f))||f===void 0&&!(u in o))&&(o[u]=f)}function _e(o,u){for(var f=o.length;f--;)if(rr(o[f][0],u))return f;return-1}function Lt(o,u){return o&&cn(u,fn(u),o)}function on(o,u,f,A,R,C,M){var B;if(A&&(B=C?A(o,R,C,M):A(o)),B!==void 0)return B;if(!_t(o))return o;var H=un(o);if(H){if(B=As(o),!u)return ys(o,B)}else{var $=Dt(o),X=$==d||$==m;if(sr(o))return Oe(o,u);if($==b||$==i||X&&!C){if(Ze(o))return C?o:{};if(B=St(X?{}:o),!u)return vs(o,Lt(B,o))}else{if(!D[$])return C?o:{};B=Ns(o,$,on,u)}}M||(M=new rt);var st=M.get(o);if(st)return st;if(M.set(o,B),!H)var z=f?Es(o):fn(o);return Pn(z||o,function(Y,V){z&&(V=Y,Y=o[V]),Qn(B,V,on(Y,u,f,A,V,o,M))}),B}function us(o){return _t(o)?Vn(o):{}}function hs(o,u,f){var A=u(o);return un(o)?A:Un(A,f(o))}function fs(o){return Ne.call(o)}function ds(o){if(!_t(o)||xs(o))return!1;var u=hn(o)||Ze(o)?zr:$r;return u.test(tt(o))}function gs(o){if(!er(o))return Zn(o);var u=[];for(var f in Object(o))xt.call(o,f)&&f!="constructor"&&u.push(f);return u}function Oe(o,u){if(u)return o.slice();var f=new o.constructor(o.length);return o.copy(f),f}function an(o){var u=new o.constructor(o.byteLength);return new Qe(u).set(new Qe(o)),u}function ce(o,u){var f=u?an(o.buffer):o.buffer;return new o.constructor(f,o.byteOffset,o.byteLength)}function Jn(o,u,f){var A=u?f(Hn(o),!0):Hn(o);return Ve(A,Ur,new o.constructor)}function tr(o){var u=new o.constructor(o.source,Mr.exec(o));return u.lastIndex=o.lastIndex,u}function ps(o,u,f){var A=u?f(zn(o),!0):zn(o);return Ve(A,dt,new o.constructor)}function ms(o){return Xn?Object(Xn.call(o)):{}}function bs(o,u){var f=u?an(o.buffer):o.buffer;return new o.constructor(f,o.byteOffset,o.length)}function ys(o,u){var f=-1,A=o.length;for(u||(u=Array(A));++f<A;)u[f]=o[f];return u}function cn(o,u,f,A){f||(f={});for(var R=-1,C=u.length;++R<C;){var M=u[R],B=void 0;Qn(f,M,B===void 0?o[M]:B)}return f}function vs(o,u){return cn(o,$t(o),u)}function Es(o){return hs(o,fn,$t)}function ue(o,u){var f=o.__data__;return Ts(u)?f[typeof u=="string"?"string":"hash"]:f.map}function pt(o,u){var f=Fn(o,u);return ds(f)?f:void 0}var $t=Je?Xe(Je,Object):Ss,Dt=fs;(xe&&Dt(new xe(new ArrayBuffer(1)))!=I||le&&Dt(new le)!=g||gt&&Dt(gt.resolve())!=v||Le&&Dt(new Le)!=E||tn&&Dt(new tn)!=_)&&(Dt=function(o){var u=Ne.call(o),f=u==b?o.constructor:void 0,A=f?tt(f):void 0;if(A)switch(A){case en:return I;case ae:return g;case nn:return v;case rn:return E;case sn:return _}return u});function As(o){var u=o.length,f=o.constructor(u);return u&&typeof o[0]=="string"&&xt.call(o,"index")&&(f.index=o.index,f.input=o.input),f}function St(o){return typeof o.constructor=="function"&&!er(o)?us(ot(o)):{}}function Ns(o,u,f,A){var R=o.constructor;switch(u){case L:return an(o);case a:case c:return new R(+o);case I:return ce(o,A);case k:case U:case Bt:case Kt:case re:case se:case Bn:case Mn:case $n:return bs(o,A);case g:return Jn(o,A,f);case p:case w:return new R(o);case y:return tr(o);case E:return ps(o,A,f);case T:return ms(o)}}function ws(o,u){return u=u??s,!!u&&(typeof o=="number"||Dr.test(o))&&o>-1&&o%1==0&&o<u}function Ts(o){var u=typeof o;return u=="string"||u=="number"||u=="symbol"||u=="boolean"?o!=="__proto__":o===null}function xs(o){return!!Gn&&Gn in o}function er(o){var u=o&&o.constructor,f=typeof u=="function"&&u.prototype||Ae;return o===f}function tt(o){if(o!=null){try{return Kn.call(o)}catch{}try{return o+""}catch{}}return""}function nr(o){return on(o,!0,!0)}function rr(o,u){return o===u||o!==o&&u!==u}function qe(o){return Ls(o)&&xt.call(o,"callee")&&(!Wn.call(o,"callee")||Ne.call(o)==i)}var un=Array.isArray;function Ce(o){return o!=null&&ir(o.length)&&!hn(o)}function Ls(o){return lr(o)&&Ce(o)}var sr=Te||_s;function hn(o){var u=_t(o)?Ne.call(o):"";return u==d||u==m}function ir(o){return typeof o=="number"&&o>-1&&o%1==0&&o<=s}function _t(o){var u=typeof o;return!!o&&(u=="object"||u=="function")}function lr(o){return!!o&&typeof o=="object"}function fn(o){return Ce(o)?Se(o):gs(o)}function Ss(){return[]}function _s(){return!1}n.exports=nr})(Nr,Nr.exports);var So=Nr.exports,wr={exports:{}};wr.exports;(function(n,t){var e=200,r="__lodash_hash_undefined__",s=1,i=2,l=9007199254740991,a="[object Arguments]",c="[object Array]",h="[object AsyncFunction]",d="[object Boolean]",m="[object Date]",g="[object Error]",p="[object Function]",b="[object GeneratorFunction]",v="[object Map]",y="[object Number]",E="[object Null]",w="[object Object]",T="[object Promise]",_="[object Proxy]",L="[object RegExp]",I="[object Set]",k="[object String]",U="[object Symbol]",Bt="[object Undefined]",Kt="[object WeakMap]",re="[object ArrayBuffer]",se="[object DataView]",Bn="[object Float32Array]",Mn="[object Float64Array]",$n="[object Int8Array]",Br="[object Int16Array]",Mr="[object Int32Array]",$r="[object Uint8Array]",Dr="[object Uint8ClampedArray]",D="[object Uint16Array]",jr="[object Uint32Array]",Pr=/[\\^$.*+?()[\]{}|]/g,ft=/^\[object .+?Constructor\]$/,Dn=/^(?:0|[1-9]\d*)$/,j={};j[Bn]=j[Mn]=j[$n]=j[Br]=j[Mr]=j[$r]=j[Dr]=j[D]=j[jr]=!0,j[a]=j[c]=j[re]=j[d]=j[se]=j[m]=j[g]=j[p]=j[v]=j[y]=j[w]=j[L]=j[I]=j[k]=j[Kt]=!1;var jn=typeof Xt=="object"&&Xt&&Xt.Object===Object&&Xt,Ur=typeof self=="object"&&self&&self.Object===Object&&self,dt=jn||Ur||Function("return this")(),Pn=t&&!t.nodeType&&t,Un=Pn&&!0&&n&&!n.nodeType&&n,Ve=Un&&Un.exports===Pn,We=Ve&&jn.process,Fn=function(){try{return We&&We.binding&&We.binding("util")}catch{}}(),Ze=Fn&&Fn.isTypedArray;function Hn(o,u){for(var f=-1,A=o==null?0:o.length,R=0,C=[];++f<A;){var M=o[f];u(M,f,o)&&(C[R++]=M)}return C}function Xe(o,u){for(var f=-1,A=u.length,R=o.length;++f<A;)o[R+f]=u[f];return o}function zn(o,u){for(var f=-1,A=o==null?0:o.length;++f<A;)if(u(o[f],f,o))return!0;return!1}function Fr(o,u){for(var f=-1,A=Array(o);++f<o;)A[f]=u(f);return A}function Hr(o){return function(u){return o(u)}}function Ae(o,u){return o.has(u)}function Ye(o,u){return o==null?void 0:o[u]}function Gn(o){var u=-1,f=Array(o.size);return o.forEach(function(A,R){f[++u]=[R,A]}),f}function Kn(o,u){return function(f){return o(u(f))}}function xt(o){var u=-1,f=Array(o.size);return o.forEach(function(A){f[++u]=A}),f}var Ne=Array.prototype,zr=Function.prototype,ie=Object.prototype,we=dt["__core-js_shared__"],Qe=zr.toString,ot=ie.hasOwnProperty,Vn=function(){var o=/[^.]+$/.exec(we&&we.keys&&we.keys.IE_PROTO||"");return o?"Symbol(src)_1."+o:""}(),Wn=ie.toString,Gr=RegExp("^"+Qe.call(ot).replace(Pr,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Je=Ve?dt.Buffer:void 0,Te=dt.Symbol,Zn=dt.Uint8Array,xe=ie.propertyIsEnumerable,le=Ne.splice,gt=Te?Te.toStringTag:void 0,Le=Object.getOwnPropertySymbols,tn=Je?Je.isBuffer:void 0,oe=Kn(Object.keys,Object),en=$t(dt,"DataView"),ae=$t(dt,"Map"),nn=$t(dt,"Promise"),rn=$t(dt,"Set"),sn=$t(dt,"WeakMap"),Vt=$t(Object,"create"),Xn=tt(en),Mt=tt(ae),Kr=tt(nn),Vr=tt(rn),Wr=tt(sn),Yn=Te?Te.prototype:void 0,ln=Yn?Yn.valueOf:void 0;function K(o){var u=-1,f=o==null?0:o.length;for(this.clear();++u<f;){var A=o[u];this.set(A[0],A[1])}}function Zr(){this.__data__=Vt?Vt(null):{},this.size=0}function Xr(o){var u=this.has(o)&&delete this.__data__[o];return this.size-=u?1:0,u}function Yr(o){var u=this.__data__;if(Vt){var f=u[o];return f===r?void 0:f}return ot.call(u,o)?u[o]:void 0}function Qr(o){var u=this.__data__;return Vt?u[o]!==void 0:ot.call(u,o)}function Jr(o,u){var f=this.__data__;return this.size+=this.has(o)?0:1,f[o]=Vt&&u===void 0?r:u,this}K.prototype.clear=Zr,K.prototype.delete=Xr,K.prototype.get=Yr,K.prototype.has=Qr,K.prototype.set=Jr;function Z(o){var u=-1,f=o==null?0:o.length;for(this.clear();++u<f;){var A=o[u];this.set(A[0],A[1])}}function ts(){this.__data__=[],this.size=0}function es(o){var u=this.__data__,f=Oe(u,o);if(f<0)return!1;var A=u.length-1;return f==A?u.pop():le.call(u,f,1),--this.size,!0}function ns(o){var u=this.__data__,f=Oe(u,o);return f<0?void 0:u[f][1]}function rs(o){return Oe(this.__data__,o)>-1}function ss(o,u){var f=this.__data__,A=Oe(f,o);return A<0?(++this.size,f.push([o,u])):f[A][1]=u,this}Z.prototype.clear=ts,Z.prototype.delete=es,Z.prototype.get=ns,Z.prototype.has=rs,Z.prototype.set=ss;function rt(o){var u=-1,f=o==null?0:o.length;for(this.clear();++u<f;){var A=o[u];this.set(A[0],A[1])}}function is(){this.size=0,this.__data__={hash:new K,map:new(ae||Z),string:new K}}function ls(o){var u=pt(this,o).delete(o);return this.size-=u?1:0,u}function os(o){return pt(this,o).get(o)}function as(o){return pt(this,o).has(o)}function cs(o,u){var f=pt(this,o),A=f.size;return f.set(o,u),this.size+=f.size==A?0:1,this}rt.prototype.clear=is,rt.prototype.delete=ls,rt.prototype.get=os,rt.prototype.has=as,rt.prototype.set=cs;function Se(o){var u=-1,f=o==null?0:o.length;for(this.__data__=new rt;++u<f;)this.add(o[u])}function Qn(o){return this.__data__.set(o,r),this}function _e(o){return this.__data__.has(o)}Se.prototype.add=Se.prototype.push=Qn,Se.prototype.has=_e;function Lt(o){var u=this.__data__=new Z(o);this.size=u.size}function on(){this.__data__=new Z,this.size=0}function us(o){var u=this.__data__,f=u.delete(o);return this.size=u.size,f}function hs(o){return this.__data__.get(o)}function fs(o){return this.__data__.has(o)}function ds(o,u){var f=this.__data__;if(f instanceof Z){var A=f.__data__;if(!ae||A.length<e-1)return A.push([o,u]),this.size=++f.size,this;f=this.__data__=new rt(A)}return f.set(o,u),this.size=f.size,this}Lt.prototype.clear=on,Lt.prototype.delete=us,Lt.prototype.get=hs,Lt.prototype.has=fs,Lt.prototype.set=ds;function gs(o,u){var f=qe(o),A=!f&&rr(o),R=!f&&!A&&Ce(o),C=!f&&!A&&!R&&lr(o),M=f||A||R||C,B=M?Fr(o.length,String):[],H=B.length;for(var $ in o)ot.call(o,$)&&!(M&&($=="length"||R&&($=="offset"||$=="parent")||C&&($=="buffer"||$=="byteLength"||$=="byteOffset")||Ns($,H)))&&B.push($);return B}function Oe(o,u){for(var f=o.length;f--;)if(nr(o[f][0],u))return f;return-1}function an(o,u,f){var A=u(o);return qe(o)?A:Xe(A,f(o))}function ce(o){return o==null?o===void 0?Bt:E:gt&&gt in Object(o)?Dt(o):er(o)}function Jn(o){return _t(o)&&ce(o)==a}function tr(o,u,f,A,R){return o===u?!0:o==null||u==null||!_t(o)&&!_t(u)?o!==o&&u!==u:ps(o,u,f,A,tr,R)}function ps(o,u,f,A,R,C){var M=qe(o),B=qe(u),H=M?c:St(o),$=B?c:St(u);H=H==a?w:H,$=$==a?w:$;var X=H==w,st=$==w,z=H==$;if(z&&Ce(o)){if(!Ce(u))return!1;M=!0,X=!1}if(z&&!X)return C||(C=new Lt),M||lr(o)?cn(o,u,f,A,R,C):vs(o,u,H,f,A,R,C);if(!(f&s)){var Y=X&&ot.call(o,"__wrapped__"),V=st&&ot.call(u,"__wrapped__");if(Y||V){var Wt=Y?o.value():o,jt=V?u.value():u;return C||(C=new Lt),R(Wt,jt,f,A,C)}}return z?(C||(C=new Lt),Es(o,u,f,A,R,C)):!1}function ms(o){if(!ir(o)||Ts(o))return!1;var u=sr(o)?Gr:ft;return u.test(tt(o))}function bs(o){return _t(o)&&hn(o.length)&&!!j[ce(o)]}function ys(o){if(!xs(o))return oe(o);var u=[];for(var f in Object(o))ot.call(o,f)&&f!="constructor"&&u.push(f);return u}function cn(o,u,f,A,R,C){var M=f&s,B=o.length,H=u.length;if(B!=H&&!(M&&H>B))return!1;var $=C.get(o);if($&&C.get(u))return $==u;var X=-1,st=!0,z=f&i?new Se:void 0;for(C.set(o,u),C.set(u,o);++X<B;){var Y=o[X],V=u[X];if(A)var Wt=M?A(V,Y,X,u,o,C):A(Y,V,X,o,u,C);if(Wt!==void 0){if(Wt)continue;st=!1;break}if(z){if(!zn(u,function(jt,he){if(!Ae(z,he)&&(Y===jt||R(Y,jt,f,A,C)))return z.push(he)})){st=!1;break}}else if(!(Y===V||R(Y,V,f,A,C))){st=!1;break}}return C.delete(o),C.delete(u),st}function vs(o,u,f,A,R,C,M){switch(f){case se:if(o.byteLength!=u.byteLength||o.byteOffset!=u.byteOffset)return!1;o=o.buffer,u=u.buffer;case re:return!(o.byteLength!=u.byteLength||!C(new Zn(o),new Zn(u)));case d:case m:case y:return nr(+o,+u);case g:return o.name==u.name&&o.message==u.message;case L:case k:return o==u+"";case v:var B=Gn;case I:var H=A&s;if(B||(B=xt),o.size!=u.size&&!H)return!1;var $=M.get(o);if($)return $==u;A|=i,M.set(o,u);var X=cn(B(o),B(u),A,R,C,M);return M.delete(o),X;case U:if(ln)return ln.call(o)==ln.call(u)}return!1}function Es(o,u,f,A,R,C){var M=f&s,B=ue(o),H=B.length,$=ue(u),X=$.length;if(H!=X&&!M)return!1;for(var st=H;st--;){var z=B[st];if(!(M?z in u:ot.call(u,z)))return!1}var Y=C.get(o);if(Y&&C.get(u))return Y==u;var V=!0;C.set(o,u),C.set(u,o);for(var Wt=M;++st<H;){z=B[st];var jt=o[z],he=u[z];if(A)var Pi=M?A(he,jt,z,u,o,C):A(jt,he,z,o,u,C);if(!(Pi===void 0?jt===he||R(jt,he,f,A,C):Pi)){V=!1;break}Wt||(Wt=z=="constructor")}if(V&&!Wt){var or=o.constructor,ar=u.constructor;or!=ar&&"constructor"in o&&"constructor"in u&&!(typeof or=="function"&&or instanceof or&&typeof ar=="function"&&ar instanceof ar)&&(V=!1)}return C.delete(o),C.delete(u),V}function ue(o){return an(o,fn,As)}function pt(o,u){var f=o.__data__;return ws(u)?f[typeof u=="string"?"string":"hash"]:f.map}function $t(o,u){var f=Ye(o,u);return ms(f)?f:void 0}function Dt(o){var u=ot.call(o,gt),f=o[gt];try{o[gt]=void 0;var A=!0}catch{}var R=Wn.call(o);return A&&(u?o[gt]=f:delete o[gt]),R}var As=Le?function(o){return o==null?[]:(o=Object(o),Hn(Le(o),function(u){return xe.call(o,u)}))}:Ss,St=ce;(en&&St(new en(new ArrayBuffer(1)))!=se||ae&&St(new ae)!=v||nn&&St(nn.resolve())!=T||rn&&St(new rn)!=I||sn&&St(new sn)!=Kt)&&(St=function(o){var u=ce(o),f=u==w?o.constructor:void 0,A=f?tt(f):"";if(A)switch(A){case Xn:return se;case Mt:return v;case Kr:return T;case Vr:return I;case Wr:return Kt}return u});function Ns(o,u){return u=u??l,!!u&&(typeof o=="number"||Dn.test(o))&&o>-1&&o%1==0&&o<u}function ws(o){var u=typeof o;return u=="string"||u=="number"||u=="symbol"||u=="boolean"?o!=="__proto__":o===null}function Ts(o){return!!Vn&&Vn in o}function xs(o){var u=o&&o.constructor,f=typeof u=="function"&&u.prototype||ie;return o===f}function er(o){return Wn.call(o)}function tt(o){if(o!=null){try{return Qe.call(o)}catch{}try{return o+""}catch{}}return""}function nr(o,u){return o===u||o!==o&&u!==u}var rr=Jn(function(){return arguments}())?Jn:function(o){return _t(o)&&ot.call(o,"callee")&&!xe.call(o,"callee")},qe=Array.isArray;function un(o){return o!=null&&hn(o.length)&&!sr(o)}var Ce=tn||_s;function Ls(o,u){return tr(o,u)}function sr(o){if(!ir(o))return!1;var u=ce(o);return u==p||u==b||u==h||u==_}function hn(o){return typeof o=="number"&&o>-1&&o%1==0&&o<=l}function ir(o){var u=typeof o;return o!=null&&(u=="object"||u=="function")}function _t(o){return o!=null&&typeof o=="object"}var lr=Ze?Hr(Ze):bs;function fn(o){return un(o)?gs(o):ys(o)}function Ss(){return[]}function _s(){return!1}n.exports=Ls})(wr,wr.exports);var _o=wr.exports,_i={};Object.defineProperty(_i,"__esModule",{value:!0});const Qf=So,Jf=_o;var ei;(function(n){function t(i={},l={},a=!1){typeof i!="object"&&(i={}),typeof l!="object"&&(l={});let c=Qf(l);a||(c=Object.keys(c).reduce((h,d)=>(c[d]!=null&&(h[d]=c[d]),h),{}));for(const h in i)i[h]!==void 0&&l[h]===void 0&&(c[h]=i[h]);return Object.keys(c).length>0?c:void 0}n.compose=t;function e(i={},l={}){typeof i!="object"&&(i={}),typeof l!="object"&&(l={});const a=Object.keys(i).concat(Object.keys(l)).reduce((c,h)=>(Jf(i[h],l[h])||(c[h]=l[h]===void 0?null:l[h]),c),{});return Object.keys(a).length>0?a:void 0}n.diff=e;function r(i={},l={}){i=i||{};const a=Object.keys(l).reduce((c,h)=>(l[h]!==i[h]&&i[h]!==void 0&&(c[h]=l[h]),c),{});return Object.keys(i).reduce((c,h)=>(i[h]!==l[h]&&l[h]===void 0&&(c[h]=null),c),a)}n.invert=r;function s(i,l,a=!1){if(typeof i!="object")return l;if(typeof l!="object")return;if(!a)return l;const c=Object.keys(l).reduce((h,d)=>(i[d]===void 0&&(h[d]=l[d]),h),{});return Object.keys(c).length>0?c:void 0}n.transform=s})(ei||(ei={}));_i.default=ei;var Cr={};Object.defineProperty(Cr,"__esModule",{value:!0});var ni;(function(n){function t(e){return typeof e.delete=="number"?e.delete:typeof e.retain=="number"?e.retain:typeof e.retain=="object"&&e.retain!==null?1:typeof e.insert=="string"?e.insert.length:1}n.length=t})(ni||(ni={}));Cr.default=ni;var Oi={};Object.defineProperty(Oi,"__esModule",{value:!0});const vl=Cr;class td{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);const e=this.ops[this.index];if(e){const r=this.offset,s=vl.default.length(e);if(t>=s-r?(t=s-r,this.index+=1,this.offset=0):this.offset+=t,typeof e.delete=="number")return{delete:t};{const i={};return e.attributes&&(i.attributes=e.attributes),typeof e.retain=="number"?i.retain=t:typeof e.retain=="object"&&e.retain!==null?i.retain=e.retain:typeof e.insert=="string"?i.insert=e.insert.substr(r,t):i.insert=e.insert,i}}else return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?vl.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const t=this.ops[this.index];return t?typeof t.delete=="number"?"delete":typeof t.retain=="number"||typeof t.retain=="object"&&t.retain!==null?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);{const t=this.offset,e=this.index,r=this.next(),s=this.ops.slice(this.index);return this.offset=t,this.index=e,[r].concat(s)}}else return[]}}Oi.default=td;(function(n,t){Object.defineProperty(t,"__esModule",{value:!0}),t.AttributeMap=t.OpIterator=t.Op=void 0;const e=Yf,r=So,s=_o,i=_i;t.AttributeMap=i.default;const l=Cr;t.Op=l.default;const a=Oi;t.OpIterator=a.default;const c="\0",h=(m,g)=>{if(typeof m!="object"||m===null)throw new Error(`cannot retain a ${typeof m}`);if(typeof g!="object"||g===null)throw new Error(`cannot retain a ${typeof g}`);const p=Object.keys(m)[0];if(!p||p!==Object.keys(g)[0])throw new Error(`embed types not matched: ${p} != ${Object.keys(g)[0]}`);return[p,m[p],g[p]]};class d{constructor(g){Array.isArray(g)?this.ops=g:g!=null&&Array.isArray(g.ops)?this.ops=g.ops:this.ops=[]}static registerEmbed(g,p){this.handlers[g]=p}static unregisterEmbed(g){delete this.handlers[g]}static getHandler(g){const p=this.handlers[g];if(!p)throw new Error(`no handlers for embed type "${g}"`);return p}insert(g,p){const b={};return typeof g=="string"&&g.length===0?this:(b.insert=g,p!=null&&typeof p=="object"&&Object.keys(p).length>0&&(b.attributes=p),this.push(b))}delete(g){return g<=0?this:this.push({delete:g})}retain(g,p){if(typeof g=="number"&&g<=0)return this;const b={retain:g};return p!=null&&typeof p=="object"&&Object.keys(p).length>0&&(b.attributes=p),this.push(b)}push(g){let p=this.ops.length,b=this.ops[p-1];if(g=r(g),typeof b=="object"){if(typeof g.delete=="number"&&typeof b.delete=="number")return this.ops[p-1]={delete:b.delete+g.delete},this;if(typeof b.delete=="number"&&g.insert!=null&&(p-=1,b=this.ops[p-1],typeof b!="object"))return this.ops.unshift(g),this;if(s(g.attributes,b.attributes)){if(typeof g.insert=="string"&&typeof b.insert=="string")return this.ops[p-1]={insert:b.insert+g.insert},typeof g.attributes=="object"&&(this.ops[p-1].attributes=g.attributes),this;if(typeof g.retain=="number"&&typeof b.retain=="number")return this.ops[p-1]={retain:b.retain+g.retain},typeof g.attributes=="object"&&(this.ops[p-1].attributes=g.attributes),this}}return p===this.ops.length?this.ops.push(g):this.ops.splice(p,0,g),this}chop(){const g=this.ops[this.ops.length-1];return g&&typeof g.retain=="number"&&!g.attributes&&this.ops.pop(),this}filter(g){return this.ops.filter(g)}forEach(g){this.ops.forEach(g)}map(g){return this.ops.map(g)}partition(g){const p=[],b=[];return this.forEach(v=>{(g(v)?p:b).push(v)}),[p,b]}reduce(g,p){return this.ops.reduce(g,p)}changeLength(){return this.reduce((g,p)=>p.insert?g+l.default.length(p):p.delete?g-p.delete:g,0)}length(){return this.reduce((g,p)=>g+l.default.length(p),0)}slice(g=0,p=1/0){const b=[],v=new a.default(this.ops);let y=0;for(;y<p&&v.hasNext();){let E;y<g?E=v.next(g-y):(E=v.next(p-y),b.push(E)),y+=l.default.length(E)}return new d(b)}compose(g){const p=new a.default(this.ops),b=new a.default(g.ops),v=[],y=b.peek();if(y!=null&&typeof y.retain=="number"&&y.attributes==null){let w=y.retain;for(;p.peekType()==="insert"&&p.peekLength()<=w;)w-=p.peekLength(),v.push(p.next());y.retain-w>0&&b.next(y.retain-w)}const E=new d(v);for(;p.hasNext()||b.hasNext();)if(b.peekType()==="insert")E.push(b.next());else if(p.peekType()==="delete")E.push(p.next());else{const w=Math.min(p.peekLength(),b.peekLength()),T=p.next(w),_=b.next(w);if(_.retain){const L={};if(typeof T.retain=="number")L.retain=typeof _.retain=="number"?w:_.retain;else if(typeof _.retain=="number")T.retain==null?L.insert=T.insert:L.retain=T.retain;else{const k=T.retain==null?"insert":"retain",[U,Bt,Kt]=h(T[k],_.retain),re=d.getHandler(U);L[k]={[U]:re.compose(Bt,Kt,k==="retain")}}const I=i.default.compose(T.attributes,_.attributes,typeof T.retain=="number");if(I&&(L.attributes=I),E.push(L),!b.hasNext()&&s(E.ops[E.ops.length-1],L)){const k=new d(p.rest());return E.concat(k).chop()}}else typeof _.delete=="number"&&(typeof T.retain=="number"||typeof T.retain=="object"&&T.retain!==null)&&E.push(_)}return E.chop()}concat(g){const p=new d(this.ops.slice());return g.ops.length>0&&(p.push(g.ops[0]),p.ops=p.ops.concat(g.ops.slice(1))),p}diff(g,p){if(this.ops===g.ops)return new d;const b=[this,g].map(T=>T.map(_=>{if(_.insert!=null)return typeof _.insert=="string"?_.insert:c;const L=T===g?"on":"with";throw new Error("diff() called "+L+" non-document")}).join("")),v=new d,y=e(b[0],b[1],p,!0),E=new a.default(this.ops),w=new a.default(g.ops);return y.forEach(T=>{let _=T[1].length;for(;_>0;){let L=0;switch(T[0]){case e.INSERT:L=Math.min(w.peekLength(),_),v.push(w.next(L));break;case e.DELETE:L=Math.min(_,E.peekLength()),E.next(L),v.delete(L);break;case e.EQUAL:L=Math.min(E.peekLength(),w.peekLength(),_);const I=E.next(L),k=w.next(L);s(I.insert,k.insert)?v.retain(L,i.default.diff(I.attributes,k.attributes)):v.push(k).delete(L);break}_-=L}}),v.chop()}eachLine(g,p=`
`){const b=new a.default(this.ops);let v=new d,y=0;for(;b.hasNext();){if(b.peekType()!=="insert")return;const E=b.peek(),w=l.default.length(E)-b.peekLength(),T=typeof E.insert=="string"?E.insert.indexOf(p,w)-w:-1;if(T<0)v.push(b.next());else if(T>0)v.push(b.next(T));else{if(g(v,b.next(1).attributes||{},y)===!1)return;y+=1,v=new d}}v.length()>0&&g(v,{},y)}invert(g){const p=new d;return this.reduce((b,v)=>{if(v.insert)p.delete(l.default.length(v));else{if(typeof v.retain=="number"&&v.attributes==null)return p.retain(v.retain),b+v.retain;if(v.delete||typeof v.retain=="number"){const y=v.delete||v.retain;return g.slice(b,b+y).forEach(w=>{v.delete?p.push(w):v.retain&&v.attributes&&p.retain(l.default.length(w),i.default.invert(v.attributes,w.attributes))}),b+y}else if(typeof v.retain=="object"&&v.retain!==null){const y=g.slice(b,b+1),E=new a.default(y.ops).next(),[w,T,_]=h(v.retain,E.insert),L=d.getHandler(w);return p.retain({[w]:L.invert(T,_)},i.default.invert(v.attributes,E.attributes)),b+1}}return b},0),p.chop()}transform(g,p=!1){if(p=!!p,typeof g=="number")return this.transformPosition(g,p);const b=g,v=new a.default(this.ops),y=new a.default(b.ops),E=new d;for(;v.hasNext()||y.hasNext();)if(v.peekType()==="insert"&&(p||y.peekType()!=="insert"))E.retain(l.default.length(v.next()));else if(y.peekType()==="insert")E.push(y.next());else{const w=Math.min(v.peekLength(),y.peekLength()),T=v.next(w),_=y.next(w);if(T.delete)continue;if(_.delete)E.push(_);else{const L=T.retain,I=_.retain;let k=typeof I=="object"&&I!==null?I:w;if(typeof L=="object"&&L!==null&&typeof I=="object"&&I!==null){const U=Object.keys(L)[0];if(U===Object.keys(I)[0]){const Bt=d.getHandler(U);Bt&&(k={[U]:Bt.transform(L[U],I[U],p)})}}E.retain(k,i.default.transform(T.attributes,_.attributes,p))}}return E.chop()}transformPosition(g,p=!1){p=!!p;const b=new a.default(this.ops);let v=0;for(;b.hasNext()&&v<=g;){const y=b.peekLength(),E=b.peekType();if(b.next(),E==="delete"){g-=Math.min(y,g-v);continue}else E==="insert"&&(v<g||!p)&&(g+=y);v+=y}return g}}d.Op=l.default,d.OpIterator=a.default,d.AttributeMap=i.default,d.handlers={},t.default=d,n.exports=d,n.exports.default=d})(ti,ti.exports);var nt=ti.exports;const O=Fl(nt);class wt extends lt{static value(){}optimize(){(this.prev||this.next)&&this.remove()}length(){return 0}value(){return""}}wt.blotName="break";wt.tagName="BR";let At=class extends Ar{};const ed={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Ir(n){return n.replace(/[&<>"']/g,t=>ed[t])}const Ot=class Ot extends Ti{static compare(t,e){const r=Ot.order.indexOf(t),s=Ot.order.indexOf(e);return r>=0||s>=0?r-s:t===e?0:t<e?-1:1}formatAt(t,e,r,s){if(Ot.compare(this.statics.blotName,r)<0&&this.scroll.query(r,q.BLOT)){const i=this.isolate(t,e);s&&i.wrap(r,s)}else super.formatAt(t,e,r,s)}optimize(t){if(super.optimize(t),this.parent instanceof Ot&&Ot.compare(this.statics.blotName,this.parent.statics.blotName)>0){const e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}};x(Ot,"allowedChildren",[Ot,wt,lt,At]),x(Ot,"order",["cursor","inline","link","underline","strike","italic","bold","script","code"]);let Rt=Ot;const El=1;class W extends wn{constructor(){super(...arguments);x(this,"cache",{})}delta(){return this.cache.delta==null&&(this.cache.delta=Oo(this)),this.cache.delta}deleteAt(e,r){super.deleteAt(e,r),this.cache={}}formatAt(e,r,s,i){r<=0||(this.scroll.query(s,q.BLOCK)?e+r===this.length()&&this.format(s,i):super.formatAt(e,Math.min(r,this.length()-e-1),s,i),this.cache={})}insertAt(e,r,s){if(s!=null){super.insertAt(e,r,s),this.cache={};return}if(r.length===0)return;const i=r.split(`
`),l=i.shift();l.length>0&&(e<this.length()-1||this.children.tail==null?super.insertAt(Math.min(e,this.length()-1),l):this.children.tail.insertAt(this.children.tail.length(),l),this.cache={});let a=this;i.reduce((c,h)=>(a=a.split(c,!0),a.insertAt(0,h),h.length),e+l.length)}insertBefore(e,r){const{head:s}=this.children;super.insertBefore(e,r),s instanceof wt&&s.remove(),this.cache={}}length(){return this.cache.length==null&&(this.cache.length=super.length()+El),this.cache.length}moveChildren(e,r){super.moveChildren(e,r),this.cache={}}optimize(e){super.optimize(e),this.cache={}}path(e){return super.path(e,!0)}removeChild(e){super.removeChild(e),this.cache={}}split(e){let r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(r&&(e===0||e>=this.length()-El)){const i=this.clone();return e===0?(this.parent.insertBefore(i,this),this):(this.parent.insertBefore(i,this.next),i)}const s=super.split(e,r);return this.cache={},s}}W.blotName="block";W.tagName="P";W.defaultChild=wt;W.allowedChildren=[wt,Rt,lt,At];class ht extends lt{attach(){super.attach(),this.attributes=new Sr(this.domNode)}delta(){return new O().insert(this.value(),{...this.formats(),...this.attributes.values()})}format(t,e){const r=this.scroll.query(t,q.BLOCK_ATTRIBUTE);r!=null&&this.attributes.attribute(r,e)}formatAt(t,e,r,s){this.format(r,s)}insertAt(t,e,r){if(r!=null){super.insertAt(t,e,r);return}const s=e.split(`
`),i=s.pop(),l=s.map(c=>{const h=this.scroll.create(W.blotName);return h.insertAt(0,c),h}),a=this.split(t);l.forEach(c=>{this.parent.insertBefore(c,a)}),i&&this.parent.insertBefore(this.scroll.create("text",i),a)}}ht.scope=q.BLOCK_BLOT;function Oo(n){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return n.descendants(Q).reduce((e,r)=>r.length()===0?e:e.insert(r.value(),ct(r,{},t)),new O).insert(`
`,ct(n))}function ct(n){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;return n==null||("formats"in n&&typeof n.formats=="function"&&(t={...t,...n.formats()},e&&delete t["code-token"]),n.parent==null||n.parent.statics.blotName==="scroll"||n.parent.statics.scope!==n.statics.scope)?t:ct(n.parent,t,e)}const at=class at extends lt{static value(){}constructor(t,e,r){super(t,e),this.selection=r,this.textNode=document.createTextNode(at.CONTENTS),this.domNode.appendChild(this.textNode),this.savedLength=0}detach(){this.parent!=null&&this.parent.removeChild(this)}format(t,e){if(this.savedLength!==0){super.format(t,e);return}let r=this,s=0;for(;r!=null&&r.statics.scope!==q.BLOCK_BLOT;)s+=r.offset(r.parent),r=r.parent;r!=null&&(this.savedLength=at.CONTENTS.length,r.optimize(),r.formatAt(s,at.CONTENTS.length,t,e),this.savedLength=0)}index(t,e){return t===this.textNode?0:super.index(t,e)}length(){return this.savedLength}position(){return[this.textNode,this.textNode.data.length]}remove(){super.remove(),this.parent=null}restore(){if(this.selection.composing||this.parent==null)return null;const t=this.selection.getNativeRange();for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);const e=this.prev instanceof At?this.prev:null,r=e?e.length():0,s=this.next instanceof At?this.next:null,i=s?s.text:"",{textNode:l}=this,a=l.data.split(at.CONTENTS).join("");l.data=at.CONTENTS;let c;if(e)c=e,(a||s)&&(e.insertAt(e.length(),a+i),s&&s.remove());else if(s)c=s,s.insertAt(0,a);else{const h=document.createTextNode(a);c=this.scroll.create(h),this.parent.insertBefore(c,this)}if(this.remove(),t){const h=(g,p)=>e&&g===e.domNode?p:g===l?r+p-1:s&&g===s.domNode?r+a.length+p:null,d=h(t.start.node,t.start.offset),m=h(t.end.node,t.end.offset);if(d!==null&&m!==null)return{startNode:c.domNode,startOffset:d,endNode:c.domNode,endOffset:m}}return null}update(t,e){if(t.some(r=>r.type==="characterData"&&r.target===this.textNode)){const r=this.restore();r&&(e.range=r)}}optimize(t){super.optimize(t);let{parent:e}=this;for(;e;){if(e.domNode.tagName==="A"){this.savedLength=at.CONTENTS.length,e.isolate(this.offset(e),this.length()).unwrap(),this.savedLength=0;break}e=e.parent}}value(){return""}};x(at,"blotName","cursor"),x(at,"className","ql-cursor"),x(at,"tagName","span"),x(at,"CONTENTS","\uFEFF");let He=at;var qo={exports:{}};(function(n){var t=Object.prototype.hasOwnProperty,e="~";function r(){}Object.create&&(r.prototype=Object.create(null),new r().__proto__||(e=!1));function s(c,h,d){this.fn=c,this.context=h,this.once=d||!1}function i(c,h,d,m,g){if(typeof d!="function")throw new TypeError("The listener must be a function");var p=new s(d,m||c,g),b=e?e+h:h;return c._events[b]?c._events[b].fn?c._events[b]=[c._events[b],p]:c._events[b].push(p):(c._events[b]=p,c._eventsCount++),c}function l(c,h){--c._eventsCount===0?c._events=new r:delete c._events[h]}function a(){this._events=new r,this._eventsCount=0}a.prototype.eventNames=function(){var h=[],d,m;if(this._eventsCount===0)return h;for(m in d=this._events)t.call(d,m)&&h.push(e?m.slice(1):m);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(d)):h},a.prototype.listeners=function(h){var d=e?e+h:h,m=this._events[d];if(!m)return[];if(m.fn)return[m.fn];for(var g=0,p=m.length,b=new Array(p);g<p;g++)b[g]=m[g].fn;return b},a.prototype.listenerCount=function(h){var d=e?e+h:h,m=this._events[d];return m?m.fn?1:m.length:0},a.prototype.emit=function(h,d,m,g,p,b){var v=e?e+h:h;if(!this._events[v])return!1;var y=this._events[v],E=arguments.length,w,T;if(y.fn){switch(y.once&&this.removeListener(h,y.fn,void 0,!0),E){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,d),!0;case 3:return y.fn.call(y.context,d,m),!0;case 4:return y.fn.call(y.context,d,m,g),!0;case 5:return y.fn.call(y.context,d,m,g,p),!0;case 6:return y.fn.call(y.context,d,m,g,p,b),!0}for(T=1,w=new Array(E-1);T<E;T++)w[T-1]=arguments[T];y.fn.apply(y.context,w)}else{var _=y.length,L;for(T=0;T<_;T++)switch(y[T].once&&this.removeListener(h,y[T].fn,void 0,!0),E){case 1:y[T].fn.call(y[T].context);break;case 2:y[T].fn.call(y[T].context,d);break;case 3:y[T].fn.call(y[T].context,d,m);break;case 4:y[T].fn.call(y[T].context,d,m,g);break;default:if(!w)for(L=1,w=new Array(E-1);L<E;L++)w[L-1]=arguments[L];y[T].fn.apply(y[T].context,w)}}return!0},a.prototype.on=function(h,d,m){return i(this,h,d,m,!1)},a.prototype.once=function(h,d,m){return i(this,h,d,m,!0)},a.prototype.removeListener=function(h,d,m,g){var p=e?e+h:h;if(!this._events[p])return this;if(!d)return l(this,p),this;var b=this._events[p];if(b.fn)b.fn===d&&(!g||b.once)&&(!m||b.context===m)&&l(this,p);else{for(var v=0,y=[],E=b.length;v<E;v++)(b[v].fn!==d||g&&!b[v].once||m&&b[v].context!==m)&&y.push(b[v]);y.length?this._events[p]=y.length===1?y[0]:y:l(this,p)}return this},a.prototype.removeAllListeners=function(h){var d;return h?(d=e?e+h:h,this._events[d]&&l(this,d)):(this._events=new r,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=e,a.EventEmitter=a,n.exports=a})(qo);var nd=qo.exports;const rd=Fl(nd),ri=new WeakMap,si=["error","warn","log","info"];let ii="warn";function Co(n){if(ii&&si.indexOf(n)<=si.indexOf(ii)){for(var t=arguments.length,e=new Array(t>1?t-1:0),r=1;r<t;r++)e[r-1]=arguments[r];console[n](...e)}}function Gt(n){return si.reduce((t,e)=>(t[e]=Co.bind(console,e,n),t),{})}Gt.level=n=>{ii=n};Co.level=Gt.level;const ks=Gt("quill:events"),sd=["selectionchange","mousedown","mouseup","click"];sd.forEach(n=>{document.addEventListener(n,function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];Array.from(document.querySelectorAll(".ql-container")).forEach(s=>{const i=ri.get(s);i&&i.emitter&&i.emitter.handleDOM(...e)})})});class S extends rd{constructor(){super(),this.domListeners={},this.on("error",ks.error)}emit(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return ks.log.call(ks,...e),super.emit(...e)}handleDOM(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),s=1;s<e;s++)r[s-1]=arguments[s];(this.domListeners[t.type]||[]).forEach(i=>{let{node:l,handler:a}=i;(t.target===l||l.contains(t.target))&&a(t,...r)})}listenDOM(t,e,r){this.domListeners[t]||(this.domListeners[t]=[]),this.domListeners[t].push({node:e,handler:r})}}x(S,"events",{EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SCROLL_EMBED_UPDATE:"scroll-embed-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"}),x(S,"sources",{API:"api",SILENT:"silent",USER:"user"});const Bs=Gt("quill:selection");class ge{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.index=t,this.length=e}}class id{constructor(t,e){this.emitter=e,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new ge(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,()=>{!this.mouseDown&&!this.composing&&setTimeout(this.update.bind(this,S.sources.USER),1)}),this.emitter.on(S.events.SCROLL_BEFORE_UPDATE,()=>{if(!this.hasFocus())return;const r=this.getNativeRange();r!=null&&r.start.node!==this.cursor.textNode&&this.emitter.once(S.events.SCROLL_UPDATE,(s,i)=>{try{this.root.contains(r.start.node)&&this.root.contains(r.end.node)&&this.setNativeRange(r.start.node,r.start.offset,r.end.node,r.end.offset);const l=i.some(a=>a.type==="characterData"||a.type==="childList"||a.type==="attributes"&&a.target===this.root);this.update(l?S.sources.SILENT:s)}catch{}})}),this.emitter.on(S.events.SCROLL_OPTIMIZE,(r,s)=>{if(s.range){const{startNode:i,startOffset:l,endNode:a,endOffset:c}=s.range;this.setNativeRange(i,l,a,c),this.update(S.sources.SILENT)}}),this.update(S.sources.SILENT)}handleComposition(){this.emitter.on(S.events.COMPOSITION_BEFORE_START,()=>{this.composing=!0}),this.emitter.on(S.events.COMPOSITION_END,()=>{if(this.composing=!1,this.cursor.parent){const t=this.cursor.restore();if(!t)return;setTimeout(()=>{this.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}handleDragging(){this.emitter.listenDOM("mousedown",document.body,()=>{this.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,()=>{this.mouseDown=!1,this.update(S.sources.USER)})}focus(){this.hasFocus()||(this.root.focus({preventScroll:!0}),this.setRange(this.savedRange))}format(t,e){this.scroll.update();const r=this.getNativeRange();if(!(r==null||!r.native.collapsed||this.scroll.query(t,q.BLOCK))){if(r.start.node!==this.cursor.textNode){const s=this.scroll.find(r.start.node,!1);if(s==null)return;if(s instanceof Q){const i=s.split(r.start.offset);s.parent.insertBefore(this.cursor,i)}else s.insertBefore(this.cursor,r.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;const r=this.scroll.length();t=Math.min(t,r-1),e=Math.min(t+e,r-1)-t;let s,[i,l]=this.scroll.leaf(t);if(i==null)return null;if(e>0&&l===i.length()){const[d]=this.scroll.leaf(t+1);if(d){const[m]=this.scroll.line(t),[g]=this.scroll.line(t+1);m===g&&(i=d,l=0)}}[s,l]=i.position(l,!0);const a=document.createRange();if(e>0)return a.setStart(s,l),[i,l]=this.scroll.leaf(t+e),i==null?null:([s,l]=i.position(l,!0),a.setEnd(s,l),a.getBoundingClientRect());let c="left",h;if(s instanceof Text){if(!s.data.length)return null;l<s.data.length?(a.setStart(s,l),a.setEnd(s,l+1)):(a.setStart(s,l-1),a.setEnd(s,l),c="right"),h=a.getBoundingClientRect()}else{if(!(i.domNode instanceof Element))return null;h=i.domNode.getBoundingClientRect(),l>0&&(c="right")}return{bottom:h.top+h.height,height:h.height,left:h[c],right:h[c],top:h.top,width:0}}getNativeRange(){const t=document.getSelection();if(t==null||t.rangeCount<=0)return null;const e=t.getRangeAt(0);if(e==null)return null;const r=this.normalizeNative(e);return Bs.info("getNativeRange",r),r}getRange(){const t=this.scroll.domNode;if("isConnected"in t&&!t.isConnected)return[null,null];const e=this.getNativeRange();return e==null?[null,null]:[this.normalizedToRange(e),e]}hasFocus(){return document.activeElement===this.root||document.activeElement!=null&&Ms(this.root,document.activeElement)}normalizedToRange(t){const e=[[t.start.node,t.start.offset]];t.native.collapsed||e.push([t.end.node,t.end.offset]);const r=e.map(l=>{const[a,c]=l,h=this.scroll.find(a,!0),d=h.offset(this.scroll);return c===0?d:h instanceof Q?d+h.index(a,c):d+h.length()}),s=Math.min(Math.max(...r),this.scroll.length()-1),i=Math.min(s,...r);return new ge(i,s-i)}normalizeNative(t){if(!Ms(this.root,t.startContainer)||!t.collapsed&&!Ms(this.root,t.endContainer))return null;const e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach(r=>{let{node:s,offset:i}=r;for(;!(s instanceof Text)&&s.childNodes.length>0;)if(s.childNodes.length>i)s=s.childNodes[i],i=0;else if(s.childNodes.length===i)s=s.lastChild,s instanceof Text?i=s.data.length:s.childNodes.length>0?i=s.childNodes.length:i=s.childNodes.length+1;else break;r.node=s,r.offset=i}),e}rangeToNative(t){const e=this.scroll.length(),r=(s,i)=>{s=Math.min(e-1,s);const[l,a]=this.scroll.leaf(s);return l?l.position(a,i):[null,-1]};return[...r(t.index,!1),...r(t.index+t.length,!0)]}setNativeRange(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t,s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:e,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(Bs.info("setNativeRange",t,e,r,s),t!=null&&(this.root.parentNode==null||t.parentNode==null||r.parentNode==null))return;const l=document.getSelection();if(l!=null)if(t!=null){this.hasFocus()||this.root.focus({preventScroll:!0});const{native:a}=this.getNativeRange()||{};if(a==null||i||t!==a.startContainer||e!==a.startOffset||r!==a.endContainer||s!==a.endOffset){t instanceof Element&&t.tagName==="BR"&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),r instanceof Element&&r.tagName==="BR"&&(s=Array.from(r.parentNode.childNodes).indexOf(r),r=r.parentNode);const c=document.createRange();c.setStart(t,e),c.setEnd(r,s),l.removeAllRanges(),l.addRange(c)}}else l.removeAllRanges(),this.root.blur()}setRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:S.sources.API;if(typeof e=="string"&&(r=e,e=!1),Bs.info("setRange",t),t!=null){const s=this.rangeToNative(t);this.setNativeRange(...s,e)}else this.setNativeRange(null);this.update(r)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:S.sources.USER;const e=this.lastRange,[r,s]=this.getRange();if(this.lastRange=r,this.lastNative=s,this.lastRange!=null&&(this.savedRange=this.lastRange),!wi(e,this.lastRange)){if(!this.composing&&s!=null&&s.native.collapsed&&s.start.node!==this.cursor.textNode){const l=this.cursor.restore();l&&this.setNativeRange(l.startNode,l.startOffset,l.endNode,l.endOffset)}const i=[S.events.SELECTION_CHANGE,De(this.lastRange),De(e),t];this.emitter.emit(S.events.EDITOR_CHANGE,...i),t!==S.sources.SILENT&&this.emitter.emit(...i)}}}function Ms(n,t){try{t.parentNode}catch{return!1}return n.contains(t)}const ld=/^[ -~]*$/;class od{constructor(t){this.scroll=t,this.delta=this.getDelta()}applyDelta(t){this.scroll.update();let e=this.scroll.length();this.scroll.batchStart();const r=Al(t),s=new O;return cd(r.ops.slice()).reduce((l,a)=>{const c=nt.Op.length(a);let h=a.attributes||{},d=!1,m=!1;if(a.insert!=null){if(s.retain(c),typeof a.insert=="string"){const b=a.insert;m=!b.endsWith(`
`)&&(e<=l||!!this.scroll.descendant(ht,l)[0]),this.scroll.insertAt(l,b);const[v,y]=this.scroll.line(l);let E=Jt({},ct(v));if(v instanceof W){const[w]=v.descendant(Q,y);w&&(E=Jt(E,ct(w)))}h=nt.AttributeMap.diff(E,h)||{}}else if(typeof a.insert=="object"){const b=Object.keys(a.insert)[0];if(b==null)return l;const v=this.scroll.query(b,q.INLINE)!=null;if(v)(e<=l||this.scroll.descendant(ht,l)[0])&&(m=!0);else if(l>0){const[y,E]=this.scroll.descendant(Q,l-1);y instanceof At?y.value()[E]!==`
`&&(d=!0):y instanceof lt&&y.statics.scope===q.INLINE_BLOT&&(d=!0)}if(this.scroll.insertAt(l,b,a.insert[b]),v){const[y]=this.scroll.descendant(Q,l);if(y){const E=Jt({},ct(y));h=nt.AttributeMap.diff(E,h)||{}}}}e+=c}else if(s.push(a),a.retain!==null&&typeof a.retain=="object"){const b=Object.keys(a.retain)[0];if(b==null)return l;this.scroll.updateEmbedAt(l,b,a.retain[b])}Object.keys(h).forEach(b=>{this.scroll.formatAt(l,c,b,h[b])});const g=d?1:0,p=m?1:0;return e+=g+p,s.retain(g),s.delete(p),l+c+g+p},0),s.reduce((l,a)=>typeof a.delete=="number"?(this.scroll.deleteAt(l,a.delete),l):l+nt.Op.length(a),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(r)}deleteText(t,e){return this.scroll.deleteAt(t,e),this.update(new O().retain(t).delete(e))}formatLine(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.scroll.update(),Object.keys(r).forEach(i=>{this.scroll.lines(t,Math.max(e,1)).forEach(l=>{l.format(i,r[i])})}),this.scroll.optimize();const s=new O().retain(t).retain(e,De(r));return this.update(s)}formatText(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};Object.keys(r).forEach(i=>{this.scroll.formatAt(t,e,i,r[i])});const s=new O().retain(t).retain(e,De(r));return this.update(s)}getContents(t,e){return this.delta.slice(t,t+e)}getDelta(){return this.scroll.lines().reduce((t,e)=>t.concat(e.delta()),new O)}getFormat(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=[],s=[];e===0?this.scroll.path(t).forEach(a=>{const[c]=a;c instanceof W?r.push(c):c instanceof Q&&s.push(c)}):(r=this.scroll.lines(t,e),s=this.scroll.descendants(Q,t,e));const[i,l]=[r,s].map(a=>{const c=a.shift();if(c==null)return{};let h=ct(c);for(;Object.keys(h).length>0;){const d=a.shift();if(d==null)return h;h=ad(ct(d),h)}return h});return{...i,...l}}getHTML(t,e){const[r,s]=this.scroll.line(t);if(r){const i=r.length();return r.length()>=s+e&&!(s===0&&e===i)?xn(r,s,e,!0):xn(this.scroll,t,e,!0)}return""}getText(t,e){return this.getContents(t,e).filter(r=>typeof r.insert=="string").map(r=>r.insert).join("")}insertContents(t,e){const r=Al(e),s=new O().retain(t).concat(r);return this.scroll.insertContents(t,r),this.update(s)}insertEmbed(t,e,r){return this.scroll.insertAt(t,e,r),this.update(new O().retain(t).insert({[e]:r}))}insertText(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return e=e.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(t,e),Object.keys(r).forEach(s=>{this.scroll.formatAt(t,e.length,s,r[s])}),this.update(new O().retain(t).insert(e,De(r)))}isBlank(){if(this.scroll.children.length===0)return!0;if(this.scroll.children.length>1)return!1;const t=this.scroll.children.head;if((t==null?void 0:t.statics.blotName)!==W.blotName)return!1;const e=t;return e.children.length>1?!1:e.children.head instanceof wt}removeFormat(t,e){const r=this.getText(t,e),[s,i]=this.scroll.line(t+e);let l=0,a=new O;s!=null&&(l=s.length()-i,a=s.delta().slice(i,i+l-1).insert(`
`));const h=this.getContents(t,e+l).diff(new O().insert(r).concat(a)),d=new O().retain(t).concat(h);return this.applyDelta(d)}update(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;const s=this.delta;if(e.length===1&&e[0].type==="characterData"&&e[0].target.data.match(ld)&&this.scroll.find(e[0].target)){const i=this.scroll.find(e[0].target),l=ct(i),a=i.offset(this.scroll),c=e[0].oldValue.replace(He.CONTENTS,""),h=new O().insert(c),d=new O().insert(i.value()),m=r&&{oldRange:Nl(r.oldRange,-a),newRange:Nl(r.newRange,-a)};t=new O().retain(a).concat(h.diff(d,m)).reduce((p,b)=>b.insert?p.insert(b.insert,l):p.push(b),new O),this.delta=s.compose(t)}else this.delta=this.getDelta(),(!t||!wi(s.compose(t),this.delta))&&(t=s.diff(this.delta,r));return t}}function Me(n,t,e){if(n.length===0){const[p]=$s(e.pop());return t<=0?`</li></${p}>`:`</li></${p}>${Me([],t-1,e)}`}const[{child:r,offset:s,length:i,indent:l,type:a},...c]=n,[h,d]=$s(a);if(l>t)return e.push(a),l===t+1?`<${h}><li${d}>${xn(r,s,i)}${Me(c,l,e)}`:`<${h}><li>${Me(n,t+1,e)}`;const m=e[e.length-1];if(l===t&&a===m)return`</li><li${d}>${xn(r,s,i)}${Me(c,l,e)}`;const[g]=$s(e.pop());return`</li></${g}>${Me(n,t-1,e)}`}function xn(n,t,e){let r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if("html"in n&&typeof n.html=="function")return n.html(t,e);if(n instanceof At)return Ir(n.value().slice(t,t+e)).replaceAll(" ","&nbsp;");if(n instanceof Et){if(n.statics.blotName==="list-container"){const h=[];return n.children.forEachAt(t,e,(d,m,g)=>{const p="formats"in d&&typeof d.formats=="function"?d.formats():{};h.push({child:d,offset:m,length:g,indent:p.indent||0,type:p.list})}),Me(h,-1,[])}const s=[];if(n.children.forEachAt(t,e,(h,d,m)=>{s.push(xn(h,d,m))}),r||n.statics.blotName==="list")return s.join("");const{outerHTML:i,innerHTML:l}=n.domNode,[a,c]=i.split(`>${l}<`);return a==="<table"?`<table style="border: 1px solid #000;">${s.join("")}<${c}`:`${a}>${s.join("")}<${c}`}return n.domNode instanceof Element?n.domNode.outerHTML:""}function ad(n,t){return Object.keys(t).reduce((e,r)=>{if(n[r]==null)return e;const s=t[r];return s===n[r]?e[r]=s:Array.isArray(s)?s.indexOf(n[r])<0?e[r]=s.concat([n[r]]):e[r]=s:e[r]=[s,n[r]],e},{})}function $s(n){const t=n==="ordered"?"ol":"ul";switch(n){case"checked":return[t,' data-list="checked"'];case"unchecked":return[t,' data-list="unchecked"'];default:return[t,""]}}function Al(n){return n.reduce((t,e)=>{if(typeof e.insert=="string"){const r=e.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return t.insert(r,e.attributes)}return t.push(e)},new O)}function Nl(n,t){let{index:e,length:r}=n;return new ge(e+t,r)}function cd(n){const t=[];return n.forEach(e=>{typeof e.insert=="string"?e.insert.split(`
`).forEach((s,i)=>{i&&t.push({insert:`
`,attributes:e.attributes}),s&&t.push({insert:s,attributes:e.attributes})}):t.push(e)}),t}class Tt{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.quill=t,this.options=e}}x(Tt,"DEFAULTS",{});const ur="\uFEFF";class qi extends lt{constructor(t,e){super(t,e),this.contentNode=document.createElement("span"),this.contentNode.setAttribute("contenteditable","false"),Array.from(this.domNode.childNodes).forEach(r=>{this.contentNode.appendChild(r)}),this.leftGuard=document.createTextNode(ur),this.rightGuard=document.createTextNode(ur),this.domNode.appendChild(this.leftGuard),this.domNode.appendChild(this.contentNode),this.domNode.appendChild(this.rightGuard)}index(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:super.index(t,e)}restore(t){let e=null,r;const s=t.data.split(ur).join("");if(t===this.leftGuard)if(this.prev instanceof At){const i=this.prev.length();this.prev.insertAt(i,s),e={startNode:this.prev.domNode,startOffset:i+s.length}}else r=document.createTextNode(s),this.parent.insertBefore(this.scroll.create(r),this),e={startNode:r,startOffset:s.length};else t===this.rightGuard&&(this.next instanceof At?(this.next.insertAt(0,s),e={startNode:this.next.domNode,startOffset:s.length}):(r=document.createTextNode(s),this.parent.insertBefore(this.scroll.create(r),this.next),e={startNode:r,startOffset:s.length}));return t.data=ur,e}update(t,e){t.forEach(r=>{if(r.type==="characterData"&&(r.target===this.leftGuard||r.target===this.rightGuard)){const s=this.restore(r.target);s&&(e.range=s)}})}}class ud{constructor(t,e){x(this,"isComposing",!1);this.scroll=t,this.emitter=e,this.setupListeners()}setupListeners(){this.scroll.domNode.addEventListener("compositionstart",t=>{this.isComposing||this.handleCompositionStart(t)}),this.scroll.domNode.addEventListener("compositionend",t=>{this.isComposing&&queueMicrotask(()=>{this.handleCompositionEnd(t)})})}handleCompositionStart(t){const e=t.target instanceof Node?this.scroll.find(t.target,!0):null;e&&!(e instanceof qi)&&(this.emitter.emit(S.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(S.events.COMPOSITION_START,t),this.isComposing=!0)}handleCompositionEnd(t){this.emitter.emit(S.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(S.events.COMPOSITION_END,t),this.isComposing=!1}}const yn=class yn{constructor(t,e){x(this,"modules",{});this.quill=t,this.options=e}init(){Object.keys(this.options.modules).forEach(t=>{this.modules[t]==null&&this.addModule(t)})}addModule(t){const e=this.quill.constructor.import(`modules/${t}`);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}};x(yn,"DEFAULTS",{modules:{}}),x(yn,"themes",{default:yn});let ze=yn;const hd=n=>n.parentElement||n.getRootNode().host||null,fd=n=>{const t=n.getBoundingClientRect(),e="offsetWidth"in n&&Math.abs(t.width)/n.offsetWidth||1,r="offsetHeight"in n&&Math.abs(t.height)/n.offsetHeight||1;return{top:t.top,right:t.left+n.clientWidth*e,bottom:t.top+n.clientHeight*r,left:t.left}},hr=n=>{const t=parseInt(n,10);return Number.isNaN(t)?0:t},wl=(n,t,e,r,s,i)=>n<e&&t>r?0:n<e?-(e-n+s):t>r?t-n>r-e?n+s-e:t-r+i:0,dd=(n,t)=>{var i,l,a;const e=n.ownerDocument;let r=t,s=n;for(;s;){const c=s===e.body,h=c?{top:0,right:((i=window.visualViewport)==null?void 0:i.width)??e.documentElement.clientWidth,bottom:((l=window.visualViewport)==null?void 0:l.height)??e.documentElement.clientHeight,left:0}:fd(s),d=getComputedStyle(s),m=wl(r.left,r.right,h.left,h.right,hr(d.scrollPaddingLeft),hr(d.scrollPaddingRight)),g=wl(r.top,r.bottom,h.top,h.bottom,hr(d.scrollPaddingTop),hr(d.scrollPaddingBottom));if(m||g)if(c)(a=e.defaultView)==null||a.scrollBy(m,g);else{const{scrollLeft:p,scrollTop:b}=s;g&&(s.scrollTop+=g),m&&(s.scrollLeft+=m);const v=s.scrollLeft-p,y=s.scrollTop-b;r={left:r.left-v,top:r.top-y,right:r.right-v,bottom:r.bottom-y}}s=c||d.position==="fixed"?null:hd(s)}},gd=100,pd=["block","break","cursor","inline","scroll","text"],md=(n,t,e)=>{const r=new Fe;return pd.forEach(s=>{const i=t.query(s);i&&r.register(i)}),n.forEach(s=>{let i=t.query(s);i||e.error(`Cannot register "${s}" specified in "formats" config. Are you sure it was registered?`);let l=0;for(;i;)if(r.register(i),i="blotName"in i?i.requiredContainer??null:null,l+=1,l>gd){e.error(`Cycle detected in registering blot requiredContainer: "${s}"`);break}}),r},Pe=Gt("quill"),fr=new Fe;Et.uiClass="ql-ui";const bt=class bt{static debug(t){t===!0&&(t="log"),Gt.level(t)}static find(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return ri.get(t)||fr.find(t,e)}static import(t){return this.imports[t]==null&&Pe.error(`Cannot import ${t}. Are you sure it was registered?`),this.imports[t]}static register(){if(typeof(arguments.length<=0?void 0:arguments[0])!="string"){const t=arguments.length<=0?void 0:arguments[0],e=!!(!(arguments.length<=1)&&arguments[1]),r="attrName"in t?t.attrName:t.blotName;typeof r=="string"?this.register(`formats/${r}`,t,e):Object.keys(t).forEach(s=>{this.register(s,t[s],e)})}else{const t=arguments.length<=0?void 0:arguments[0],e=arguments.length<=1?void 0:arguments[1],r=!!(!(arguments.length<=2)&&arguments[2]);this.imports[t]!=null&&!r&&Pe.warn(`Overwriting ${t} with`,e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&e&&typeof e!="boolean"&&e.blotName!=="abstract"&&fr.register(e),typeof e.register=="function"&&e.register(fr)}}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.options=bd(t,e),this.container=this.options.container,this.container==null){Pe.error("Invalid Quill container",t);return}this.options.debug&&bt.debug(this.options.debug);const r=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",ri.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.emitter=new S;const s=xi.blotName,i=this.options.registry.query(s);if(!i||!("blotName"in i))throw new Error(`Cannot initialize Quill without "${s}" blot`);if(this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter}),this.editor=new od(this.scroll),this.selection=new id(this.scroll,this.emitter),this.composition=new ud(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.addModule("uiNode"),this.theme.init(),this.emitter.on(S.events.EDITOR_CHANGE,l=>{l===S.events.TEXT_CHANGE&&this.root.classList.toggle("ql-blank",this.editor.isBlank())}),this.emitter.on(S.events.SCROLL_UPDATE,(l,a)=>{const c=this.selection.lastRange,[h]=this.selection.getRange(),d=c&&h?{oldRange:c,newRange:h}:void 0;mt.call(this,()=>this.editor.update(null,a,d),l)}),this.emitter.on(S.events.SCROLL_EMBED_UPDATE,(l,a)=>{const c=this.selection.lastRange,[h]=this.selection.getRange(),d=c&&h?{oldRange:c,newRange:h}:void 0;mt.call(this,()=>{const m=new O().retain(l.offset(this)).retain({[l.statics.blotName]:a});return this.editor.update(m,[],d)},bt.sources.USER)}),r){const l=this.clipboard.convert({html:`${r}<p><br></p>`,text:`
`});this.setContents(l)}this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}addContainer(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof t=="string"){const r=t;t=document.createElement("div"),t.classList.add(r)}return this.container.insertBefore(t,e),t}blur(){this.selection.setRange(null)}deleteText(t,e,r){return[t,e,,r]=Pt(t,e,r),mt.call(this,()=>this.editor.deleteText(t,e),r,t,-1*e)}disable(){this.enable(!1)}editReadOnly(t){this.allowReadOnlyEdits=!0;const e=t();return this.allowReadOnlyEdits=!1,e}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}focus(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.selection.focus(),t.preventScroll||this.scrollSelectionIntoView()}format(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:S.sources.API;return mt.call(this,()=>{const s=this.getSelection(!0);let i=new O;if(s==null)return i;if(this.scroll.query(t,q.BLOCK))i=this.editor.formatLine(s.index,s.length,{[t]:e});else{if(s.length===0)return this.selection.format(t,e),i;i=this.editor.formatText(s.index,s.length,{[t]:e})}return this.setSelection(s,S.sources.SILENT),i},r)}formatLine(t,e,r,s,i){let l;return[t,e,l,i]=Pt(t,e,r,s,i),mt.call(this,()=>this.editor.formatLine(t,e,l),i,t,0)}formatText(t,e,r,s,i){let l;return[t,e,l,i]=Pt(t,e,r,s,i),mt.call(this,()=>this.editor.formatText(t,e,l),i,t,0)}getBounds(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=null;if(typeof t=="number"?r=this.selection.getBounds(t,e):r=this.selection.getBounds(t.index,t.length),!r)return null;const s=this.container.getBoundingClientRect();return{bottom:r.bottom-s.top,height:r.height,left:r.left-s.left,right:r.right-s.left,top:r.top-s.top,width:r.width}}getContents(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-t;return[t,e]=Pt(t,e),this.editor.getContents(t,e)}getFormat(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof t=="number"?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}getIndex(t){return t.offset(this.scroll)}getLength(){return this.scroll.length()}getLeaf(t){return this.scroll.leaf(t)}getLine(t){return this.scroll.line(t)}getLines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof t!="number"?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}getModule(t){return this.theme.modules[t]}getSelection(){return(arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1)&&this.focus(),this.update(),this.selection.getRange()[0]}getSemanticHTML(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=Pt(t,e),this.editor.getHTML(t,e)}getText(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;return typeof t=="number"&&(e=e??this.getLength()-t),[t,e]=Pt(t,e),this.editor.getText(t,e)}hasFocus(){return this.selection.hasFocus()}insertEmbed(t,e,r){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:bt.sources.API;return mt.call(this,()=>this.editor.insertEmbed(t,e,r),s,t)}insertText(t,e,r,s,i){let l;return[t,,l,i]=Pt(t,0,r,s,i),mt.call(this,()=>this.editor.insertText(t,e,l),i,t,e.length)}isEnabled(){return this.scroll.isEnabled()}off(){return this.emitter.off(...arguments)}on(){return this.emitter.on(...arguments)}once(){return this.emitter.once(...arguments)}removeFormat(t,e,r){return[t,e,,r]=Pt(t,e,r),mt.call(this,()=>this.editor.removeFormat(t,e),r,t)}scrollRectIntoView(t){dd(this.root,t)}scrollIntoView(){console.warn("Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead."),this.scrollSelectionIntoView()}scrollSelectionIntoView(){const t=this.selection.lastRange,e=t&&this.selection.getBounds(t.index,t.length);e&&this.scrollRectIntoView(e)}setContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S.sources.API;return mt.call(this,()=>{t=new O(t);const r=this.getLength(),s=this.editor.deleteText(0,r),i=this.editor.insertContents(0,t),l=this.editor.deleteText(this.getLength()-1,1);return s.compose(i).compose(l)},e)}setSelection(t,e,r){t==null?this.selection.setRange(null,e||bt.sources.API):([t,e,,r]=Pt(t,e,r),this.selection.setRange(new ge(Math.max(0,t),e),r),r!==S.sources.SILENT&&this.scrollSelectionIntoView())}setText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S.sources.API;const r=new O().insert(t);return this.setContents(r,e)}update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:S.sources.USER;const e=this.scroll.update(t);return this.selection.update(t),e}updateContents(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:S.sources.API;return mt.call(this,()=>(t=new O(t),this.editor.applyDelta(t)),e,!0)}};x(bt,"DEFAULTS",{bounds:null,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0},placeholder:"",readOnly:!1,registry:fr,theme:"default"}),x(bt,"events",S.events),x(bt,"sources",S.sources),x(bt,"version","2.0.3"),x(bt,"imports",{delta:O,parchment:Uf,"core/module":Tt,"core/theme":ze});let N=bt;function Tl(n){return typeof n=="string"?document.querySelector(n):n}function Ds(n){return Object.entries(n??{}).reduce((t,e)=>{let[r,s]=e;return{...t,[r]:s===!0?{}:s}},{})}function xl(n){return Object.fromEntries(Object.entries(n).filter(t=>t[1]!==void 0))}function bd(n,t){const e=Tl(n);if(!e)throw new Error("Invalid Quill container");const s=!t.theme||t.theme===N.DEFAULTS.theme?ze:N.import(`themes/${t.theme}`);if(!s)throw new Error(`Invalid theme ${t.theme}. Did you register it?`);const{modules:i,...l}=N.DEFAULTS,{modules:a,...c}=s.DEFAULTS;let h=Ds(t.modules);h!=null&&h.toolbar&&h.toolbar.constructor!==Object&&(h={...h,toolbar:{container:h.toolbar}});const d=Jt({},Ds(i),Ds(a),h),m={...l,...xl(c),...xl(t)};let g=t.registry;return g?t.formats&&Pe.warn('Ignoring "formats" option because "registry" is specified'):g=t.formats?md(t.formats,m.registry,Pe):m.registry,{...m,registry:g,container:e,theme:s,modules:Object.entries(d).reduce((p,b)=>{let[v,y]=b;if(!y)return p;const E=N.import(`modules/${v}`);return E==null?(Pe.error(`Cannot load ${v} module. Are you sure you registered it?`),p):{...p,[v]:Jt({},E.DEFAULTS||{},y)}},{}),bounds:Tl(m.bounds)}}function mt(n,t,e,r){if(!this.isEnabled()&&t===S.sources.USER&&!this.allowReadOnlyEdits)return new O;let s=e==null?null:this.getSelection();const i=this.editor.delta,l=n();if(s!=null&&(e===!0&&(e=s.index),r==null?s=Ll(s,l,t):r!==0&&(s=Ll(s,e,r,t)),this.setSelection(s,S.sources.SILENT)),l.length()>0){const a=[S.events.TEXT_CHANGE,l,i,t];this.emitter.emit(S.events.EDITOR_CHANGE,...a),t!==S.sources.SILENT&&this.emitter.emit(...a)}return l}function Pt(n,t,e,r,s){let i={};return typeof n.index=="number"&&typeof n.length=="number"?typeof t!="number"?(s=r,r=e,e=t,t=n.length,n=n.index):(t=n.length,n=n.index):typeof t!="number"&&(s=r,r=e,e=t,t=0),typeof e=="object"?(i=e,s=r):typeof e=="string"&&(r!=null?i[e]=r:s=e),s=s||S.sources.API,[n,t,i,s]}function Ll(n,t,e,r){const s=typeof e=="number"?e:0;if(n==null)return null;let i,l;return t&&typeof t.transformPosition=="function"?[i,l]=[n.index,n.index+n.length].map(a=>t.transformPosition(a,r!==S.sources.USER)):[i,l]=[n.index,n.index+n.length].map(a=>a<t||a===t&&r===S.sources.USER?a:s>=0?a+s:Math.max(t,a+s)),new ge(i,l-i)}class ye extends _r{}function Sl(n){return n instanceof W||n instanceof ht}function _l(n){return typeof n.updateContent=="function"}class $e extends xi{constructor(t,e,r){let{emitter:s}=r;super(t,e),this.emitter=s,this.batch=!1,this.optimize(),this.enable(),this.domNode.addEventListener("dragstart",i=>this.handleDragStart(i))}batchStart(){Array.isArray(this.batch)||(this.batch=[])}batchEnd(){if(!this.batch)return;const t=this.batch;this.batch=!1,this.update(t)}emitMount(t){this.emitter.emit(S.events.SCROLL_BLOT_MOUNT,t)}emitUnmount(t){this.emitter.emit(S.events.SCROLL_BLOT_UNMOUNT,t)}emitEmbedUpdate(t,e){this.emitter.emit(S.events.SCROLL_EMBED_UPDATE,t,e)}deleteAt(t,e){const[r,s]=this.line(t),[i]=this.line(t+e);if(super.deleteAt(t,e),i!=null&&r!==i&&s>0){if(r instanceof ht||i instanceof ht){this.optimize();return}const l=i.children.head instanceof wt?null:i.children.head;r.moveChildren(i,l),r.remove()}this.optimize()}enable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",t?"true":"false")}formatAt(t,e,r,s){super.formatAt(t,e,r,s),this.optimize()}insertAt(t,e,r){if(t>=this.length())if(r==null||this.scroll.query(e,q.BLOCK)==null){const s=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(s),r==null&&e.endsWith(`
`)?s.insertAt(0,e.slice(0,-1),r):s.insertAt(0,e,r)}else{const s=this.scroll.create(e,r);this.appendChild(s)}else super.insertAt(t,e,r);this.optimize()}insertBefore(t,e){if(t.statics.scope===q.INLINE_BLOT){const r=this.scroll.create(this.statics.defaultChild.blotName);r.appendChild(t),super.insertBefore(r,e)}else super.insertBefore(t,e)}insertContents(t,e){const r=this.deltaToRenderBlocks(e.concat(new O().insert(`
`))),s=r.pop();if(s==null)return;this.batchStart();const i=r.shift();if(i){const c=i.type==="block"&&(i.delta.length()===0||!this.descendant(ht,t)[0]&&t<this.length()),h=i.type==="block"?i.delta:new O().insert({[i.key]:i.value});js(this,t,h);const d=i.type==="block"?1:0,m=t+h.length()+d;c&&this.insertAt(m-1,`
`);const g=ct(this.line(t)[0]),p=nt.AttributeMap.diff(g,i.attributes)||{};Object.keys(p).forEach(b=>{this.formatAt(m-1,1,b,p[b])}),t=m}let[l,a]=this.children.find(t);if(r.length&&(l&&(l=l.split(a),a=0),r.forEach(c=>{if(c.type==="block"){const h=this.createBlock(c.attributes,l||void 0);js(h,0,c.delta)}else{const h=this.create(c.key,c.value);this.insertBefore(h,l||void 0),Object.keys(c.attributes).forEach(d=>{h.format(d,c.attributes[d])})}})),s.type==="block"&&s.delta.length()){const c=l?l.offset(l.scroll)+a:this.length();js(this,c,s.delta)}this.batchEnd(),this.optimize()}isEnabled(){return this.domNode.getAttribute("contenteditable")==="true"}leaf(t){const e=this.path(t).pop();if(!e)return[null,-1];const[r,s]=e;return r instanceof Q?[r,s]:[null,-1]}line(t){return t===this.length()?this.line(t-1):this.descendant(Sl,t)}lines(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;const r=(s,i,l)=>{let a=[],c=l;return s.children.forEachAt(i,l,(h,d,m)=>{Sl(h)?a.push(h):h instanceof _r&&(a=a.concat(r(h,d,c))),c-=m}),a};return r(this,t,e)}optimize(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch||(super.optimize(t,e),t.length>0&&this.emitter.emit(S.events.SCROLL_OPTIMIZE,t,e))}path(t){return super.path(t).slice(1)}remove(){}update(t){if(this.batch){Array.isArray(t)&&(this.batch=this.batch.concat(t));return}let e=S.sources.USER;typeof t=="string"&&(e=t),Array.isArray(t)||(t=this.observer.takeRecords()),t=t.filter(r=>{let{target:s}=r;const i=this.find(s,!0);return i&&!_l(i)}),t.length>0&&this.emitter.emit(S.events.SCROLL_BEFORE_UPDATE,e,t),super.update(t.concat([])),t.length>0&&this.emitter.emit(S.events.SCROLL_UPDATE,e,t)}updateEmbedAt(t,e,r){const[s]=this.descendant(i=>i instanceof ht,t);s&&s.statics.blotName===e&&_l(s)&&s.updateContent(r)}handleDragStart(t){t.preventDefault()}deltaToRenderBlocks(t){const e=[];let r=new O;return t.forEach(s=>{const i=s==null?void 0:s.insert;if(i)if(typeof i=="string"){const l=i.split(`
`);l.slice(0,-1).forEach(c=>{r.insert(c,s.attributes),e.push({type:"block",delta:r,attributes:s.attributes??{}}),r=new O});const a=l[l.length-1];a&&r.insert(a,s.attributes)}else{const l=Object.keys(i)[0];if(!l)return;this.query(l,q.INLINE)?r.push(s):(r.length()&&e.push({type:"block",delta:r,attributes:{}}),r=new O,e.push({type:"blockEmbed",key:l,value:i[l],attributes:s.attributes??{}}))}}),r.length()&&e.push({type:"block",delta:r,attributes:{}}),e}createBlock(t,e){let r;const s={};Object.entries(t).forEach(a=>{let[c,h]=a;this.query(c,q.BLOCK&q.BLOT)!=null?r=c:s[c]=h});const i=this.create(r||this.statics.defaultChild.blotName,r?t[r]:void 0);this.insertBefore(i,e||void 0);const l=i.length();return Object.entries(s).forEach(a=>{let[c,h]=a;i.formatAt(0,l,c,h)}),i}}x($e,"blotName","scroll"),x($e,"className","ql-editor"),x($e,"tagName","DIV"),x($e,"defaultChild",W),x($e,"allowedChildren",[W,ht,ye]);function js(n,t,e){e.reduce((r,s)=>{const i=nt.Op.length(s);let l=s.attributes||{};if(s.insert!=null){if(typeof s.insert=="string"){const a=s.insert;n.insertAt(r,a);const[c]=n.descendant(Q,r),h=ct(c);l=nt.AttributeMap.diff(h,l)||{}}else if(typeof s.insert=="object"){const a=Object.keys(s.insert)[0];if(a==null)return r;if(n.insertAt(r,a,s.insert[a]),n.scroll.query(a,q.INLINE)!=null){const[h]=n.descendant(Q,r),d=ct(h);l=nt.AttributeMap.diff(d,l)||{}}}}return Object.keys(l).forEach(a=>{n.formatAt(r,i,a,l[a])}),r+i},t)}const Ci={scope:q.BLOCK,whitelist:["right","center","justify"]},yd=new It("align","align",Ci),Io=new Nt("align","ql-align",Ci),Ro=new ne("align","text-align",Ci);class ko extends ne{value(t){let e=super.value(t);return e.startsWith("rgb(")?(e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),`#${e.split(",").map(s=>`00${parseInt(s,10).toString(16)}`.slice(-2)).join("")}`):e}}const vd=new Nt("color","ql-color",{scope:q.INLINE}),Ii=new ko("color","color",{scope:q.INLINE}),Ed=new Nt("background","ql-bg",{scope:q.INLINE}),Ri=new ko("background","background-color",{scope:q.INLINE});class ve extends ye{static create(t){const e=super.create(t);return e.setAttribute("spellcheck","false"),e}code(t,e){return this.children.map(r=>r.length()<=1?"":r.domNode.innerText).join(`
`).slice(t,t+e)}html(t,e){return`<pre>
${Ir(this.code(t,e))}
</pre>`}}class J extends W{static register(){N.register(ve)}}x(J,"TAB","  ");class ki extends Rt{}ki.blotName="code";ki.tagName="CODE";J.blotName="code-block";J.className="ql-code-block";J.tagName="DIV";ve.blotName="code-block-container";ve.className="ql-code-block-container";ve.tagName="DIV";ve.allowedChildren=[J];J.allowedChildren=[At,wt,He];J.requiredContainer=ve;const Bi={scope:q.BLOCK,whitelist:["rtl"]},Bo=new It("direction","dir",Bi),Mo=new Nt("direction","ql-direction",Bi),$o=new ne("direction","direction",Bi),Do={scope:q.INLINE,whitelist:["serif","monospace"]},jo=new Nt("font","ql-font",Do);class Ad extends ne{value(t){return super.value(t).replace(/["']/g,"")}}const Po=new Ad("font","font-family",Do),Uo=new Nt("size","ql-size",{scope:q.INLINE,whitelist:["small","large","huge"]}),Fo=new ne("size","font-size",{scope:q.INLINE,whitelist:["10px","18px","32px"]}),Nd=Gt("quill:keyboard"),wd=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey";class Rr extends Tt{static match(t,e){return["altKey","ctrlKey","metaKey","shiftKey"].some(r=>!!e[r]!==t[r]&&e[r]!==null)?!1:e.key===t.key||e.key===t.which}constructor(t,e){super(t,e),this.bindings={},Object.keys(this.options.bindings).forEach(r=>{this.options.bindings[r]&&this.addBinding(this.options.bindings[r])}),this.addBinding({key:"Enter",shiftKey:null},this.handleEnter),this.addBinding({key:"Enter",metaKey:null,ctrlKey:null,altKey:null},()=>{}),/Firefox/i.test(navigator.userAgent)?(this.addBinding({key:"Backspace"},{collapsed:!0},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0},this.handleDelete)):(this.addBinding({key:"Backspace"},{collapsed:!0,prefix:/^.?$/},this.handleBackspace),this.addBinding({key:"Delete"},{collapsed:!0,suffix:/^.?$/},this.handleDelete)),this.addBinding({key:"Backspace"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Delete"},{collapsed:!1},this.handleDeleteRange),this.addBinding({key:"Backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},this.handleBackspace),this.listen()}addBinding(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const s=xd(t);if(s==null){Nd.warn("Attempted to add invalid keyboard binding",s);return}typeof e=="function"&&(e={handler:e}),typeof r=="function"&&(r={handler:r}),(Array.isArray(s.key)?s.key:[s.key]).forEach(l=>{const a={...s,key:l,...e,...r};this.bindings[a.key]=this.bindings[a.key]||[],this.bindings[a.key].push(a)})}listen(){this.quill.root.addEventListener("keydown",t=>{if(t.defaultPrevented||t.isComposing||t.keyCode===229&&(t.key==="Enter"||t.key==="Backspace"))return;const s=(this.bindings[t.key]||[]).concat(this.bindings[t.which]||[]).filter(E=>Rr.match(t,E));if(s.length===0)return;const i=N.find(t.target,!0);if(i&&i.scroll!==this.quill.scroll)return;const l=this.quill.getSelection();if(l==null||!this.quill.hasFocus())return;const[a,c]=this.quill.getLine(l.index),[h,d]=this.quill.getLeaf(l.index),[m,g]=l.length===0?[h,d]:this.quill.getLeaf(l.index+l.length),p=h instanceof Ar?h.value().slice(0,d):"",b=m instanceof Ar?m.value().slice(g):"",v={collapsed:l.length===0,empty:l.length===0&&a.length()<=1,format:this.quill.getFormat(l),line:a,offset:c,prefix:p,suffix:b,event:t};s.some(E=>{if(E.collapsed!=null&&E.collapsed!==v.collapsed||E.empty!=null&&E.empty!==v.empty||E.offset!=null&&E.offset!==v.offset)return!1;if(Array.isArray(E.format)){if(E.format.every(w=>v.format[w]==null))return!1}else if(typeof E.format=="object"&&!Object.keys(E.format).every(w=>E.format[w]===!0?v.format[w]!=null:E.format[w]===!1?v.format[w]==null:wi(E.format[w],v.format[w])))return!1;return E.prefix!=null&&!E.prefix.test(v.prefix)||E.suffix!=null&&!E.suffix.test(v.suffix)?!1:E.handler.call(this,l,v,E)!==!0})&&t.preventDefault()})}handleBackspace(t,e){const r=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(t.index===0||this.quill.getLength()<=1)return;let s={};const[i]=this.quill.getLine(t.index);let l=new O().retain(t.index-r).delete(r);if(e.offset===0){const[a]=this.quill.getLine(t.index-1);if(a&&!(a.statics.blotName==="block"&&a.length()<=1)){const h=i.formats(),d=this.quill.getFormat(t.index-1,1);if(s=nt.AttributeMap.diff(h,d)||{},Object.keys(s).length>0){const m=new O().retain(t.index+i.length()-2).retain(1,s);l=l.compose(m)}}}this.quill.updateContents(l,N.sources.USER),this.quill.focus()}handleDelete(t,e){const r=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(t.index>=this.quill.getLength()-r)return;let s={};const[i]=this.quill.getLine(t.index);let l=new O().retain(t.index).delete(r);if(e.offset>=i.length()-1){const[a]=this.quill.getLine(t.index+1);if(a){const c=i.formats(),h=this.quill.getFormat(t.index,1);s=nt.AttributeMap.diff(c,h)||{},Object.keys(s).length>0&&(l=l.retain(a.length()-1).retain(1,s))}}this.quill.updateContents(l,N.sources.USER),this.quill.focus()}handleDeleteRange(t){Mi({range:t,quill:this.quill}),this.quill.focus()}handleEnter(t,e){const r=Object.keys(e.format).reduce((i,l)=>(this.quill.scroll.query(l,q.BLOCK)&&!Array.isArray(e.format[l])&&(i[l]=e.format[l]),i),{}),s=new O().retain(t.index).delete(t.length).insert(`
`,r);this.quill.updateContents(s,N.sources.USER),this.quill.setSelection(t.index+1,N.sources.SILENT),this.quill.focus()}}const Td={bindings:{bold:Ps("bold"),italic:Ps("italic"),underline:Ps("underline"),indent:{key:"Tab",format:["blockquote","indent","list"],handler(n,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","+1",N.sources.USER),!1)}},outdent:{key:"Tab",shiftKey:!0,format:["blockquote","indent","list"],handler(n,t){return t.collapsed&&t.offset!==0?!0:(this.quill.format("indent","-1",N.sources.USER),!1)}},"outdent backspace":{key:"Backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler(n,t){t.format.indent!=null?this.quill.format("indent","-1",N.sources.USER):t.format.list!=null&&this.quill.format("list",!1,N.sources.USER)}},"indent code-block":Ol(!0),"outdent code-block":Ol(!1),"remove tab":{key:"Tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler(n){this.quill.deleteText(n.index-1,1,N.sources.USER)}},tab:{key:"Tab",handler(n,t){if(t.format.table)return!0;this.quill.history.cutoff();const e=new O().retain(n.index).delete(n.length).insert("	");return this.quill.updateContents(e,N.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(n.index+1,N.sources.SILENT),!1}},"blockquote empty enter":{key:"Enter",collapsed:!0,format:["blockquote"],empty:!0,handler(){this.quill.format("blockquote",!1,N.sources.USER)}},"list empty enter":{key:"Enter",collapsed:!0,format:["list"],empty:!0,handler(n,t){const e={list:!1};t.format.indent&&(e.indent=!1),this.quill.formatLine(n.index,n.length,e,N.sources.USER)}},"checklist enter":{key:"Enter",collapsed:!0,format:{list:"checked"},handler(n){const[t,e]=this.quill.getLine(n.index),r={...t.formats(),list:"checked"},s=new O().retain(n.index).insert(`
`,r).retain(t.length()-e-1).retain(1,{list:"unchecked"});this.quill.updateContents(s,N.sources.USER),this.quill.setSelection(n.index+1,N.sources.SILENT),this.quill.scrollSelectionIntoView()}},"header enter":{key:"Enter",collapsed:!0,format:["header"],suffix:/^$/,handler(n,t){const[e,r]=this.quill.getLine(n.index),s=new O().retain(n.index).insert(`
`,t.format).retain(e.length()-r-1).retain(1,{header:null});this.quill.updateContents(s,N.sources.USER),this.quill.setSelection(n.index+1,N.sources.SILENT),this.quill.scrollSelectionIntoView()}},"table backspace":{key:"Backspace",format:["table"],collapsed:!0,offset:0,handler(){}},"table delete":{key:"Delete",format:["table"],collapsed:!0,suffix:/^$/,handler(){}},"table enter":{key:"Enter",shiftKey:null,format:["table"],handler(n){const t=this.quill.getModule("table");if(t){const[e,r,s,i]=t.getTable(n),l=Ld(e,r,s,i);if(l==null)return;let a=e.offset();if(l<0){const c=new O().retain(a).insert(`
`);this.quill.updateContents(c,N.sources.USER),this.quill.setSelection(n.index+1,n.length,N.sources.SILENT)}else if(l>0){a+=e.length();const c=new O().retain(a).insert(`
`);this.quill.updateContents(c,N.sources.USER),this.quill.setSelection(a,N.sources.USER)}}}},"table tab":{key:"Tab",shiftKey:null,format:["table"],handler(n,t){const{event:e,line:r}=t,s=r.offset(this.quill.scroll);e.shiftKey?this.quill.setSelection(s-1,N.sources.USER):this.quill.setSelection(s+r.length(),N.sources.USER)}},"list autofill":{key:" ",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler(n,t){if(this.quill.scroll.query("list")==null)return!0;const{length:e}=t.prefix,[r,s]=this.quill.getLine(n.index);if(s>e)return!0;let i;switch(t.prefix.trim()){case"[]":case"[ ]":i="unchecked";break;case"[x]":i="checked";break;case"-":case"*":i="bullet";break;default:i="ordered"}this.quill.insertText(n.index," ",N.sources.USER),this.quill.history.cutoff();const l=new O().retain(n.index-s).delete(e+1).retain(r.length()-2-s).retain(1,{list:i});return this.quill.updateContents(l,N.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(n.index-e,N.sources.SILENT),!1}},"code exit":{key:"Enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler(n){const[t,e]=this.quill.getLine(n.index);let r=2,s=t;for(;s!=null&&s.length()<=1&&s.formats()["code-block"];)if(s=s.prev,r-=1,r<=0){const i=new O().retain(n.index+t.length()-e-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(i,N.sources.USER),this.quill.setSelection(n.index-1,N.sources.SILENT),!1}return!0}},"embed left":dr("ArrowLeft",!1),"embed left shift":dr("ArrowLeft",!0),"embed right":dr("ArrowRight",!1),"embed right shift":dr("ArrowRight",!0),"table down":ql(!1),"table up":ql(!0)}};Rr.DEFAULTS=Td;function Ol(n){return{key:"Tab",shiftKey:!n,format:{"code-block":!0},handler(t,e){let{event:r}=e;const s=this.quill.scroll.query("code-block"),{TAB:i}=s;if(t.length===0&&!r.shiftKey){this.quill.insertText(t.index,i,N.sources.USER),this.quill.setSelection(t.index+i.length,N.sources.SILENT);return}const l=t.length===0?this.quill.getLines(t.index,1):this.quill.getLines(t);let{index:a,length:c}=t;l.forEach((h,d)=>{n?(h.insertAt(0,i),d===0?a+=i.length:c+=i.length):h.domNode.textContent.startsWith(i)&&(h.deleteAt(0,i.length),d===0?a-=i.length:c-=i.length)}),this.quill.update(N.sources.USER),this.quill.setSelection(a,c,N.sources.SILENT)}}}function dr(n,t){return{key:n,shiftKey:t,altKey:null,[n==="ArrowLeft"?"prefix":"suffix"]:/^$/,handler(r){let{index:s}=r;n==="ArrowRight"&&(s+=r.length+1);const[i]=this.quill.getLeaf(s);return i instanceof lt?(n==="ArrowLeft"?t?this.quill.setSelection(r.index-1,r.length+1,N.sources.USER):this.quill.setSelection(r.index-1,N.sources.USER):t?this.quill.setSelection(r.index,r.length+1,N.sources.USER):this.quill.setSelection(r.index+r.length+1,N.sources.USER),!1):!0}}}function Ps(n){return{key:n[0],shortKey:!0,handler(t,e){this.quill.format(n,!e.format[n],N.sources.USER)}}}function ql(n){return{key:n?"ArrowUp":"ArrowDown",collapsed:!0,format:["table"],handler(t,e){const r=n?"prev":"next",s=e.line,i=s.parent[r];if(i!=null){if(i.statics.blotName==="table-row"){let l=i.children.head,a=s;for(;a.prev!=null;)a=a.prev,l=l.next;const c=l.offset(this.quill.scroll)+Math.min(e.offset,l.length()-1);this.quill.setSelection(c,0,N.sources.USER)}}else{const l=s.table()[r];l!=null&&(n?this.quill.setSelection(l.offset(this.quill.scroll)+l.length()-1,0,N.sources.USER):this.quill.setSelection(l.offset(this.quill.scroll),0,N.sources.USER))}return!1}}}function xd(n){if(typeof n=="string"||typeof n=="number")n={key:n};else if(typeof n=="object")n=De(n);else return null;return n.shortKey&&(n[wd]=n.shortKey,delete n.shortKey),n}function Mi(n){let{quill:t,range:e}=n;const r=t.getLines(e);let s={};if(r.length>1){const i=r[0].formats(),l=r[r.length-1].formats();s=nt.AttributeMap.diff(l,i)||{}}t.deleteText(e,N.sources.USER),Object.keys(s).length>0&&t.formatLine(e.index,1,s,N.sources.USER),t.setSelection(e.index,N.sources.SILENT)}function Ld(n,t,e,r){return t.prev==null&&t.next==null?e.prev==null&&e.next==null?r===0?-1:1:e.prev==null?-1:1:t.prev==null?-1:t.next==null?1:null}const Sd=/font-weight:\s*normal/,_d=["P","OL","UL"],Cl=n=>n&&_d.includes(n.tagName),Od=n=>{Array.from(n.querySelectorAll("br")).filter(t=>Cl(t.previousElementSibling)&&Cl(t.nextElementSibling)).forEach(t=>{var e;(e=t.parentNode)==null||e.removeChild(t)})},qd=n=>{Array.from(n.querySelectorAll('b[style*="font-weight"]')).filter(t=>{var e;return(e=t.getAttribute("style"))==null?void 0:e.match(Sd)}).forEach(t=>{var r;const e=n.createDocumentFragment();e.append(...t.childNodes),(r=t.parentNode)==null||r.replaceChild(e,t)})};function Cd(n){n.querySelector('[id^="docs-internal-guid-"]')&&(qd(n),Od(n))}const Id=/\bmso-list:[^;]*ignore/i,Rd=/\bmso-list:[^;]*\bl(\d+)/i,kd=/\bmso-list:[^;]*\blevel(\d+)/i,Bd=(n,t)=>{const e=n.getAttribute("style"),r=e==null?void 0:e.match(Rd);if(!r)return null;const s=Number(r[1]),i=e==null?void 0:e.match(kd),l=i?Number(i[1]):1,a=new RegExp(`@list l${s}:level${l}\\s*\\{[^\\}]*mso-level-number-format:\\s*([\\w-]+)`,"i"),c=t.match(a),h=c&&c[1]==="bullet"?"bullet":"ordered";return{id:s,indent:l,type:h,element:n}},Md=n=>{var l,a;const t=Array.from(n.querySelectorAll("[style*=mso-list]")),e=[],r=[];t.forEach(c=>{(c.getAttribute("style")||"").match(Id)?e.push(c):r.push(c)}),e.forEach(c=>{var h;return(h=c.parentNode)==null?void 0:h.removeChild(c)});const s=n.documentElement.innerHTML,i=r.map(c=>Bd(c,s)).filter(c=>c);for(;i.length;){const c=[];let h=i.shift();for(;h;)c.push(h),h=i.length&&((l=i[0])==null?void 0:l.element)===h.element.nextElementSibling&&i[0].id===h.id?i.shift():null;const d=document.createElement("ul");c.forEach(p=>{const b=document.createElement("li");b.setAttribute("data-list",p.type),p.indent>1&&b.setAttribute("class",`ql-indent-${p.indent-1}`),b.innerHTML=p.element.innerHTML,d.appendChild(b)});const m=(a=c[0])==null?void 0:a.element,{parentNode:g}=m??{};m&&(g==null||g.replaceChild(d,m)),c.slice(1).forEach(p=>{let{element:b}=p;g==null||g.removeChild(b)})}};function $d(n){n.documentElement.getAttribute("xmlns:w")==="urn:schemas-microsoft-com:office:word"&&Md(n)}const Dd=[$d,Cd],jd=n=>{n.documentElement&&Dd.forEach(t=>{t(n)})},Pd=Gt("quill:clipboard"),Ud=[[Node.TEXT_NODE,Jd],[Node.TEXT_NODE,Rl],["br",Kd],[Node.ELEMENT_NODE,Rl],[Node.ELEMENT_NODE,Gd],[Node.ELEMENT_NODE,zd],[Node.ELEMENT_NODE,Yd],["li",Zd],["ol, ul",Xd],["pre",Vd],["tr",Qd],["b",Us("bold")],["i",Us("italic")],["strike",Us("strike")],["style",Wd]],Fd=[yd,Bo].reduce((n,t)=>(n[t.keyName]=t,n),{}),Il=[Ro,Ri,Ii,$o,Po,Fo].reduce((n,t)=>(n[t.keyName]=t,n),{});class Ho extends Tt{constructor(t,e){super(t,e),this.quill.root.addEventListener("copy",r=>this.onCaptureCopy(r,!1)),this.quill.root.addEventListener("cut",r=>this.onCaptureCopy(r,!0)),this.quill.root.addEventListener("paste",this.onCapturePaste.bind(this)),this.matchers=[],Ud.concat(this.options.matchers??[]).forEach(r=>{let[s,i]=r;this.addMatcher(s,i)})}addMatcher(t,e){this.matchers.push([t,e])}convert(t){let{html:e,text:r}=t,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(s[J.blotName])return new O().insert(r||"",{[J.blotName]:s[J.blotName]});if(!e)return new O().insert(r||"",s);const i=this.convertHTML(e);return Cn(i,`
`)&&(i.ops[i.ops.length-1].attributes==null||s.table)?i.compose(new O().retain(i.length()-1).delete(1)):i}normalizeHTML(t){jd(t)}convertHTML(t){const e=new DOMParser().parseFromString(t,"text/html");this.normalizeHTML(e);const r=e.body,s=new WeakMap,[i,l]=this.prepareMatching(r,s);return $i(this.quill.scroll,r,i,l,s)}dangerouslyPasteHTML(t,e){let r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:N.sources.API;if(typeof t=="string"){const s=this.convert({html:t,text:""});this.quill.setContents(s,e),this.quill.setSelection(0,N.sources.SILENT)}else{const s=this.convert({html:e,text:""});this.quill.updateContents(new O().retain(t).concat(s),r),this.quill.setSelection(t+s.length(),N.sources.SILENT)}}onCaptureCopy(t){var l,a;let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(t.defaultPrevented)return;t.preventDefault();const[r]=this.quill.selection.getRange();if(r==null)return;const{html:s,text:i}=this.onCopy(r,e);(l=t.clipboardData)==null||l.setData("text/plain",i),(a=t.clipboardData)==null||a.setData("text/html",s),e&&Mi({range:r,quill:this.quill})}normalizeURIList(t){return t.split(/\r?\n/).filter(e=>e[0]!=="#").join(`
`)}onCapturePaste(t){var l,a,c,h,d;if(t.defaultPrevented||!this.quill.isEnabled())return;t.preventDefault();const e=this.quill.getSelection(!0);if(e==null)return;const r=(l=t.clipboardData)==null?void 0:l.getData("text/html");let s=(a=t.clipboardData)==null?void 0:a.getData("text/plain");if(!r&&!s){const m=(c=t.clipboardData)==null?void 0:c.getData("text/uri-list");m&&(s=this.normalizeURIList(m))}const i=Array.from(((h=t.clipboardData)==null?void 0:h.files)||[]);if(!r&&i.length>0){this.quill.uploader.upload(e,i);return}if(r&&i.length>0){const m=new DOMParser().parseFromString(r,"text/html");if(m.body.childElementCount===1&&((d=m.body.firstElementChild)==null?void 0:d.tagName)==="IMG"){this.quill.uploader.upload(e,i);return}}this.onPaste(e,{html:r,text:s})}onCopy(t){const e=this.quill.getText(t);return{html:this.quill.getSemanticHTML(t),text:e}}onPaste(t,e){let{text:r,html:s}=e;const i=this.quill.getFormat(t.index),l=this.convert({text:r,html:s},i);Pd.log("onPaste",l,{text:r,html:s});const a=new O().retain(t.index).delete(t.length).concat(l);this.quill.updateContents(a,N.sources.USER),this.quill.setSelection(a.length()-t.length,N.sources.SILENT),this.quill.scrollSelectionIntoView()}prepareMatching(t,e){const r=[],s=[];return this.matchers.forEach(i=>{const[l,a]=i;switch(l){case Node.TEXT_NODE:s.push(a);break;case Node.ELEMENT_NODE:r.push(a);break;default:Array.from(t.querySelectorAll(l)).forEach(c=>{if(e.has(c)){const h=e.get(c);h==null||h.push(a)}else e.set(c,[a])});break}}),[r,s]}}x(Ho,"DEFAULTS",{matchers:[]});function Ee(n,t,e,r){return r.query(t)?n.reduce((s,i)=>{if(!i.insert)return s;if(i.attributes&&i.attributes[t])return s.push(i);const l=e?{[t]:e}:{};return s.insert(i.insert,{...l,...i.attributes})},new O):n}function Cn(n,t){let e="";for(let r=n.ops.length-1;r>=0&&e.length<t.length;--r){const s=n.ops[r];if(typeof s.insert!="string")break;e=s.insert+e}return e.slice(-1*t.length)===t}function Yt(n,t){if(!(n instanceof Element))return!1;const e=t.query(n);return e&&e.prototype instanceof lt?!1:["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].includes(n.tagName.toLowerCase())}function Hd(n,t){return n.previousElementSibling&&n.nextElementSibling&&!Yt(n.previousElementSibling,t)&&!Yt(n.nextElementSibling,t)}const gr=new WeakMap;function zo(n){return n==null?!1:(gr.has(n)||(n.tagName==="PRE"?gr.set(n,!0):gr.set(n,zo(n.parentNode))),gr.get(n))}function $i(n,t,e,r,s){return t.nodeType===t.TEXT_NODE?r.reduce((i,l)=>l(t,i,n),new O):t.nodeType===t.ELEMENT_NODE?Array.from(t.childNodes||[]).reduce((i,l)=>{let a=$i(n,l,e,r,s);return l.nodeType===t.ELEMENT_NODE&&(a=e.reduce((c,h)=>h(l,c,n),a),a=(s.get(l)||[]).reduce((c,h)=>h(l,c,n),a)),i.concat(a)},new O):new O}function Us(n){return(t,e,r)=>Ee(e,n,!0,r)}function zd(n,t,e){const r=It.keys(n),s=Nt.keys(n),i=ne.keys(n),l={};return r.concat(s).concat(i).forEach(a=>{let c=e.query(a,q.ATTRIBUTE);c!=null&&(l[c.attrName]=c.value(n),l[c.attrName])||(c=Fd[a],c!=null&&(c.attrName===a||c.keyName===a)&&(l[c.attrName]=c.value(n)||void 0),c=Il[a],c!=null&&(c.attrName===a||c.keyName===a)&&(c=Il[a],l[c.attrName]=c.value(n)||void 0))}),Object.entries(l).reduce((a,c)=>{let[h,d]=c;return Ee(a,h,d,e)},t)}function Gd(n,t,e){const r=e.query(n);if(r==null)return t;if(r.prototype instanceof lt){const s={},i=r.value(n);if(i!=null)return s[r.blotName]=i,new O().insert(s,r.formats(n,e))}else if(r.prototype instanceof wn&&!Cn(t,`
`)&&t.insert(`
`),"blotName"in r&&"formats"in r&&typeof r.formats=="function")return Ee(t,r.blotName,r.formats(n,e),e);return t}function Kd(n,t){return Cn(t,`
`)||t.insert(`
`),t}function Vd(n,t,e){const r=e.query("code-block"),s=r&&"formats"in r&&typeof r.formats=="function"?r.formats(n,e):!0;return Ee(t,"code-block",s,e)}function Wd(){return new O}function Zd(n,t,e){const r=e.query(n);if(r==null||r.blotName!=="list"||!Cn(t,`
`))return t;let s=-1,i=n.parentNode;for(;i!=null;)["OL","UL"].includes(i.tagName)&&(s+=1),i=i.parentNode;return s<=0?t:t.reduce((l,a)=>a.insert?a.attributes&&typeof a.attributes.indent=="number"?l.push(a):l.insert(a.insert,{indent:s,...a.attributes||{}}):l,new O)}function Xd(n,t,e){const r=n;let s=r.tagName==="OL"?"ordered":"bullet";const i=r.getAttribute("data-checked");return i&&(s=i==="true"?"checked":"unchecked"),Ee(t,"list",s,e)}function Rl(n,t,e){if(!Cn(t,`
`)){if(Yt(n,e)&&(n.childNodes.length>0||n instanceof HTMLParagraphElement))return t.insert(`
`);if(t.length()>0&&n.nextSibling){let r=n.nextSibling;for(;r!=null;){if(Yt(r,e))return t.insert(`
`);const s=e.query(r);if(s&&s.prototype instanceof ht)return t.insert(`
`);r=r.firstChild}}}return t}function Yd(n,t,e){var i;const r={},s=n.style||{};return s.fontStyle==="italic"&&(r.italic=!0),s.textDecoration==="underline"&&(r.underline=!0),s.textDecoration==="line-through"&&(r.strike=!0),((i=s.fontWeight)!=null&&i.startsWith("bold")||parseInt(s.fontWeight,10)>=700)&&(r.bold=!0),t=Object.entries(r).reduce((l,a)=>{let[c,h]=a;return Ee(l,c,h,e)},t),parseFloat(s.textIndent||0)>0?new O().insert("	").concat(t):t}function Qd(n,t,e){var s,i;const r=((s=n.parentElement)==null?void 0:s.tagName)==="TABLE"?n.parentElement:(i=n.parentElement)==null?void 0:i.parentElement;if(r!=null){const a=Array.from(r.querySelectorAll("tr")).indexOf(n)+1;return Ee(t,"table",a,e)}return t}function Jd(n,t,e){var s;let r=n.data;if(((s=n.parentElement)==null?void 0:s.tagName)==="O:P")return t.insert(r.trim());if(!zo(n)){if(r.trim().length===0&&r.includes(`
`)&&!Hd(n,e))return t;r=r.replace(/[^\S\u00a0]/g," "),r=r.replace(/ {2,}/g," "),(n.previousSibling==null&&n.parentElement!=null&&Yt(n.parentElement,e)||n.previousSibling instanceof Element&&Yt(n.previousSibling,e))&&(r=r.replace(/^ /,"")),(n.nextSibling==null&&n.parentElement!=null&&Yt(n.parentElement,e)||n.nextSibling instanceof Element&&Yt(n.nextSibling,e))&&(r=r.replace(/ $/,"")),r=r.replaceAll(" "," ")}return t.insert(r)}class Go extends Tt{constructor(e,r){super(e,r);x(this,"lastRecorded",0);x(this,"ignoreChange",!1);x(this,"stack",{undo:[],redo:[]});x(this,"currentRange",null);this.quill.on(N.events.EDITOR_CHANGE,(s,i,l,a)=>{s===N.events.SELECTION_CHANGE?i&&a!==N.sources.SILENT&&(this.currentRange=i):s===N.events.TEXT_CHANGE&&(this.ignoreChange||(!this.options.userOnly||a===N.sources.USER?this.record(i,l):this.transform(i)),this.currentRange=li(this.currentRange,i))}),this.quill.keyboard.addBinding({key:"z",shortKey:!0},this.undo.bind(this)),this.quill.keyboard.addBinding({key:["z","Z"],shortKey:!0,shiftKey:!0},this.redo.bind(this)),/Win/i.test(navigator.platform)&&this.quill.keyboard.addBinding({key:"y",shortKey:!0},this.redo.bind(this)),this.quill.root.addEventListener("beforeinput",s=>{s.inputType==="historyUndo"?(this.undo(),s.preventDefault()):s.inputType==="historyRedo"&&(this.redo(),s.preventDefault())})}change(e,r){if(this.stack[e].length===0)return;const s=this.stack[e].pop();if(!s)return;const i=this.quill.getContents(),l=s.delta.invert(i);this.stack[r].push({delta:l,range:li(s.range,l)}),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(s.delta,N.sources.USER),this.ignoreChange=!1,this.restoreSelection(s)}clear(){this.stack={undo:[],redo:[]}}cutoff(){this.lastRecorded=0}record(e,r){if(e.ops.length===0)return;this.stack.redo=[];let s=e.invert(r),i=this.currentRange;const l=Date.now();if(this.lastRecorded+this.options.delay>l&&this.stack.undo.length>0){const a=this.stack.undo.pop();a&&(s=s.compose(a.delta),i=a.range)}else this.lastRecorded=l;s.length()!==0&&(this.stack.undo.push({delta:s,range:i}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}redo(){this.change("redo","undo")}transform(e){kl(this.stack.undo,e),kl(this.stack.redo,e)}undo(){this.change("undo","redo")}restoreSelection(e){if(e.range)this.quill.setSelection(e.range,N.sources.USER);else{const r=eg(this.quill.scroll,e.delta);this.quill.setSelection(r,N.sources.USER)}}}x(Go,"DEFAULTS",{delay:1e3,maxStack:100,userOnly:!1});function kl(n,t){let e=t;for(let r=n.length-1;r>=0;r-=1){const s=n[r];n[r]={delta:e.transform(s.delta,!0),range:s.range&&li(s.range,e)},e=s.delta.transform(e),n[r].delta.length()===0&&n.splice(r,1)}}function tg(n,t){const e=t.ops[t.ops.length-1];return e==null?!1:e.insert!=null?typeof e.insert=="string"&&e.insert.endsWith(`
`):e.attributes!=null?Object.keys(e.attributes).some(r=>n.query(r,q.BLOCK)!=null):!1}function eg(n,t){const e=t.reduce((s,i)=>s+(i.delete||0),0);let r=t.length()-e;return tg(n,t)&&(r-=1),r}function li(n,t){if(!n)return n;const e=t.transformPosition(n.index),r=t.transformPosition(n.index+n.length);return{index:e,length:r-e}}class Ko extends Tt{constructor(t,e){super(t,e),t.root.addEventListener("drop",r=>{var l;r.preventDefault();let s=null;if(document.caretRangeFromPoint)s=document.caretRangeFromPoint(r.clientX,r.clientY);else if(document.caretPositionFromPoint){const a=document.caretPositionFromPoint(r.clientX,r.clientY);s=document.createRange(),s.setStart(a.offsetNode,a.offset),s.setEnd(a.offsetNode,a.offset)}const i=s&&t.selection.normalizeNative(s);if(i){const a=t.selection.normalizedToRange(i);(l=r.dataTransfer)!=null&&l.files&&this.upload(a,r.dataTransfer.files)}})}upload(t,e){const r=[];Array.from(e).forEach(s=>{var i;s&&((i=this.options.mimetypes)!=null&&i.includes(s.type))&&r.push(s)}),r.length>0&&this.options.handler.call(this,t,r)}}Ko.DEFAULTS={mimetypes:["image/png","image/jpeg"],handler(n,t){if(!this.quill.scroll.query("image"))return;const e=t.map(r=>new Promise(s=>{const i=new FileReader;i.onload=()=>{s(i.result)},i.readAsDataURL(r)}));Promise.all(e).then(r=>{const s=r.reduce((i,l)=>i.insert({image:l}),new O().retain(n.index).delete(n.length));this.quill.updateContents(s,S.sources.USER),this.quill.setSelection(n.index+r.length,S.sources.SILENT)})}};const ng=["insertText","insertReplacementText"];class rg extends Tt{constructor(t,e){super(t,e),t.root.addEventListener("beforeinput",r=>{this.handleBeforeInput(r)}),/Android/i.test(navigator.userAgent)||t.on(N.events.COMPOSITION_BEFORE_START,()=>{this.handleCompositionStart()})}deleteRange(t){Mi({range:t,quill:this.quill})}replaceText(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";if(t.length===0)return!1;if(e){const r=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents(new O().retain(t.index).insert(e,r),N.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,N.sources.SILENT),!0}handleBeforeInput(t){if(this.quill.composition.isComposing||t.defaultPrevented||!ng.includes(t.inputType))return;const e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(!e||e.collapsed===!0)return;const r=sg(t);if(r==null)return;const s=this.quill.selection.normalizeNative(e),i=s?this.quill.selection.normalizedToRange(s):null;i&&this.replaceText(i,r)&&t.preventDefault()}handleCompositionStart(){const t=this.quill.getSelection();t&&this.replaceText(t)}}function sg(n){var t;return typeof n.data=="string"?n.data:(t=n.dataTransfer)!=null&&t.types.includes("text/plain")?n.dataTransfer.getData("text/plain"):null}const ig=/Mac/i.test(navigator.platform),lg=100,og=n=>!!(n.key==="ArrowLeft"||n.key==="ArrowRight"||n.key==="ArrowUp"||n.key==="ArrowDown"||n.key==="Home"||ig&&n.key==="a"&&n.ctrlKey===!0);class ag extends Tt{constructor(e,r){super(e,r);x(this,"isListening",!1);x(this,"selectionChangeDeadline",0);this.handleArrowKeys(),this.handleNavigationShortcuts()}handleArrowKeys(){this.quill.keyboard.addBinding({key:["ArrowLeft","ArrowRight"],offset:0,shiftKey:null,handler(e,r){let{line:s,event:i}=r;if(!(s instanceof Et)||!s.uiNode)return!0;const l=getComputedStyle(s.domNode).direction==="rtl";return l&&i.key!=="ArrowRight"||!l&&i.key!=="ArrowLeft"?!0:(this.quill.setSelection(e.index-1,e.length+(i.shiftKey?1:0),N.sources.USER),!1)}})}handleNavigationShortcuts(){this.quill.root.addEventListener("keydown",e=>{!e.defaultPrevented&&og(e)&&this.ensureListeningToSelectionChange()})}ensureListeningToSelectionChange(){if(this.selectionChangeDeadline=Date.now()+lg,this.isListening)return;this.isListening=!0;const e=()=>{this.isListening=!1,Date.now()<=this.selectionChangeDeadline&&this.handleSelectionChange()};document.addEventListener("selectionchange",e,{once:!0})}handleSelectionChange(){const e=document.getSelection();if(!e)return;const r=e.getRangeAt(0);if(r.collapsed!==!0||r.startOffset!==0)return;const s=this.quill.scroll.find(r.startContainer);if(!(s instanceof Et)||!s.uiNode)return;const i=document.createRange();i.setStartAfter(s.uiNode),i.setEndAfter(s.uiNode),e.removeAllRanges(),e.addRange(i)}}N.register({"blots/block":W,"blots/block/embed":ht,"blots/break":wt,"blots/container":ye,"blots/cursor":He,"blots/embed":qi,"blots/inline":Rt,"blots/scroll":$e,"blots/text":At,"modules/clipboard":Ho,"modules/history":Go,"modules/keyboard":Rr,"modules/uploader":Ko,"modules/input":rg,"modules/uiNode":ag});class cg extends Nt{add(t,e){let r=0;if(e==="+1"||e==="-1"){const s=this.value(t)||0;r=e==="+1"?s+1:s-1}else typeof e=="number"&&(r=e);return r===0?(this.remove(t),!0):super.add(t,r.toString())}canAdd(t,e){return super.canAdd(t,e)||super.canAdd(t,parseInt(e,10))}value(t){return parseInt(super.value(t),10)||void 0}}const ug=new cg("indent","ql-indent",{scope:q.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});class oi extends W{}x(oi,"blotName","blockquote"),x(oi,"tagName","blockquote");class ai extends W{static formats(t){return this.tagName.indexOf(t.tagName)+1}}x(ai,"blotName","header"),x(ai,"tagName",["H1","H2","H3","H4","H5","H6"]);class In extends ye{}In.blotName="list-container";In.tagName="OL";class Rn extends W{static create(t){const e=super.create();return e.setAttribute("data-list",t),e}static formats(t){return t.getAttribute("data-list")||void 0}static register(){N.register(In)}constructor(t,e){super(t,e);const r=e.ownerDocument.createElement("span"),s=i=>{if(!t.isEnabled())return;const l=this.statics.formats(e,t);l==="checked"?(this.format("list","unchecked"),i.preventDefault()):l==="unchecked"&&(this.format("list","checked"),i.preventDefault())};r.addEventListener("mousedown",s),r.addEventListener("touchstart",s),this.attachUI(r)}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):super.format(t,e)}}Rn.blotName="list";Rn.tagName="LI";In.allowedChildren=[Rn];Rn.requiredContainer=In;class Ln extends Rt{static create(){return super.create()}static formats(){return!0}optimize(t){super.optimize(t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}x(Ln,"blotName","bold"),x(Ln,"tagName",["STRONG","B"]);class ci extends Ln{}x(ci,"blotName","italic"),x(ci,"tagName",["EM","I"]);class Qt extends Rt{static create(t){const e=super.create(t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}static formats(t){return t.getAttribute("href")}static sanitize(t){return Vo(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}format(t,e){t!==this.statics.blotName||!e?super.format(t,e):this.domNode.setAttribute("href",this.constructor.sanitize(e))}}x(Qt,"blotName","link"),x(Qt,"tagName","A"),x(Qt,"SANITIZED_URL","about:blank"),x(Qt,"PROTOCOL_WHITELIST",["http","https","mailto","tel","sms"]);function Vo(n,t){const e=document.createElement("a");e.href=n;const r=e.href.slice(0,e.href.indexOf(":"));return t.indexOf(r)>-1}class ui extends Rt{static create(t){return t==="super"?document.createElement("sup"):t==="sub"?document.createElement("sub"):super.create(t)}static formats(t){if(t.tagName==="SUB")return"sub";if(t.tagName==="SUP")return"super"}}x(ui,"blotName","script"),x(ui,"tagName",["SUB","SUP"]);class hi extends Ln{}x(hi,"blotName","strike"),x(hi,"tagName",["S","STRIKE"]);class fi extends Rt{}x(fi,"blotName","underline"),x(fi,"tagName","U");class mr extends qi{static create(t){if(window.katex==null)throw new Error("Formula module requires KaTeX.");const e=super.create(t);return typeof t=="string"&&(window.katex.render(t,e,{throwOnError:!1,errorColor:"#f00"}),e.setAttribute("data-value",t)),e}static value(t){return t.getAttribute("data-value")}html(){const{formula:t}=this.value();return`<span>${t}</span>`}}x(mr,"blotName","formula"),x(mr,"className","ql-formula"),x(mr,"tagName","SPAN");const Bl=["alt","height","width"];class di extends lt{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Bl.reduce((e,r)=>(t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e),{})}static match(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}static sanitize(t){return Vo(t,["http","https","data"])?t:"//:0"}static value(t){return t.getAttribute("src")}format(t,e){Bl.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}}x(di,"blotName","image"),x(di,"tagName","IMG");const Ml=["height","width"];class br extends ht{static create(t){const e=super.create(t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen","true"),e.setAttribute("src",this.sanitize(t)),e}static formats(t){return Ml.reduce((e,r)=>(t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e),{})}static sanitize(t){return Qt.sanitize(t)}static value(t){return t.getAttribute("src")}format(t,e){Ml.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):super.format(t,e)}html(){const{video:t}=this.value();return`<a href="${t}">${t}</a>`}}x(br,"blotName","video"),x(br,"className","ql-video"),x(br,"tagName","IFRAME");const pn=new Nt("code-token","hljs",{scope:q.INLINE});class Ft extends Rt{static formats(t,e){for(;t!=null&&t!==e.domNode;){if(t.classList&&t.classList.contains(J.className))return super.formats(t,e);t=t.parentNode}}constructor(t,e,r){super(t,e,r),pn.add(this.domNode,r)}format(t,e){t!==Ft.blotName?super.format(t,e):e?pn.add(this.domNode,e):(pn.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}optimize(){super.optimize(...arguments),pn.value(this.domNode)||this.unwrap()}}Ft.blotName="code-token";Ft.className="ql-token";class ut extends J{static create(t){const e=super.create(t);return typeof t=="string"&&e.setAttribute("data-language",t),e}static formats(t){return t.getAttribute("data-language")||"plain"}static register(){}format(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):super.format(t,e)}replaceWith(t,e){return this.formatAt(0,this.length(),Ft.blotName,!1),super.replaceWith(t,e)}}class bn extends ve{attach(){super.attach(),this.forceNext=!1,this.scroll.emitMount(this)}format(t,e){t===ut.blotName&&(this.forceNext=!0,this.children.forEach(r=>{r.format(t,e)}))}formatAt(t,e,r,s){r===ut.blotName&&(this.forceNext=!0),super.formatAt(t,e,r,s)}highlight(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.children.head==null)return;const s=`${Array.from(this.domNode.childNodes).filter(l=>l!==this.uiNode).map(l=>l.textContent).join(`
`)}
`,i=ut.formats(this.children.head.domNode);if(e||this.forceNext||this.cachedText!==s){if(s.trim().length>0||this.cachedText==null){const l=this.children.reduce((c,h)=>c.concat(Oo(h,!1)),new O),a=t(s,i);l.diff(a).reduce((c,h)=>{let{retain:d,attributes:m}=h;return d?(m&&Object.keys(m).forEach(g=>{[ut.blotName,Ft.blotName].includes(g)&&this.formatAt(c,d,g,m[g])}),c+d):c},0)}this.cachedText=s,this.forceNext=!1}}html(t,e){const[r]=this.children.find(t);return`<pre data-language="${r?ut.formats(r.domNode):"plain"}">
${Ir(this.code(t,e))}
</pre>`}optimize(t){if(super.optimize(t),this.parent!=null&&this.children.head!=null&&this.uiNode!=null){const e=ut.formats(this.children.head.domNode);e!==this.uiNode.value&&(this.uiNode.value=e)}}}bn.allowedChildren=[ut];ut.requiredContainer=bn;ut.allowedChildren=[Ft,He,At,wt];const hg=(n,t,e)=>{if(typeof n.versionString=="string"){const r=n.versionString.split(".")[0];if(parseInt(r,10)>=11)return n.highlight(e,{language:t}).value}return n.highlight(t,e).value};class Wo extends Tt{static register(){N.register(Ft,!0),N.register(ut,!0),N.register(bn,!0)}constructor(t,e){if(super(t,e),this.options.hljs==null)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");this.languages=this.options.languages.reduce((r,s)=>{let{key:i}=s;return r[i]=!0,r},{}),this.highlightBlot=this.highlightBlot.bind(this),this.initListener(),this.initTimer()}initListener(){this.quill.on(N.events.SCROLL_BLOT_MOUNT,t=>{if(!(t instanceof bn))return;const e=this.quill.root.ownerDocument.createElement("select");this.options.languages.forEach(r=>{let{key:s,label:i}=r;const l=e.ownerDocument.createElement("option");l.textContent=i,l.setAttribute("value",s),e.appendChild(l)}),e.addEventListener("change",()=>{t.format(ut.blotName,e.value),this.quill.root.focus(),this.highlight(t,!0)}),t.uiNode==null&&(t.attachUI(e),t.children.head&&(e.value=ut.formats(t.children.head.domNode)))})}initTimer(){let t=null;this.quill.on(N.events.SCROLL_OPTIMIZE,()=>{t&&clearTimeout(t),t=setTimeout(()=>{this.highlight(),t=null},this.options.interval)})}highlight(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:null,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(this.quill.selection.composing)return;this.quill.update(N.sources.USER);const r=this.quill.getSelection();(t==null?this.quill.scroll.descendants(bn):[t]).forEach(i=>{i.highlight(this.highlightBlot,e)}),this.quill.update(N.sources.SILENT),r!=null&&this.quill.setSelection(r,N.sources.SILENT)}highlightBlot(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"plain";if(e=this.languages[e]?e:"plain",e==="plain")return Ir(t).split(`
`).reduce((s,i,l)=>(l!==0&&s.insert(`
`,{[J.blotName]:e}),s.insert(i)),new O);const r=this.quill.root.ownerDocument.createElement("div");return r.classList.add(J.className),r.innerHTML=hg(this.options.hljs,e,t),$i(this.quill.scroll,r,[(s,i)=>{const l=pn.value(s);return l?i.compose(new O().retain(i.length(),{[Ft.blotName]:l})):i}],[(s,i)=>s.data.split(`
`).reduce((l,a,c)=>(c!==0&&l.insert(`
`,{[J.blotName]:e}),l.insert(a)),i)],new WeakMap)}}Wo.DEFAULTS={hljs:window.hljs,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"JavaScript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]};const vn=class vn extends W{static create(t){const e=super.create();return t?e.setAttribute("data-row",t):e.setAttribute("data-row",Di()),e}static formats(t){if(t.hasAttribute("data-row"))return t.getAttribute("data-row")}cellOffset(){return this.parent?this.parent.children.indexOf(this):-1}format(t,e){t===vn.blotName&&e?this.domNode.setAttribute("data-row",e):super.format(t,e)}row(){return this.parent}rowOffset(){return this.row()?this.row().rowOffset():-1}table(){return this.row()&&this.row().table()}};x(vn,"blotName","table"),x(vn,"tagName","TD");let vt=vn;class Ht extends ye{checkMerge(){if(super.checkMerge()&&this.next.children.head!=null){const t=this.children.head.formats(),e=this.children.tail.formats(),r=this.next.children.head.formats(),s=this.next.children.tail.formats();return t.table===e.table&&t.table===r.table&&t.table===s.table}return!1}optimize(t){super.optimize(t),this.children.forEach(e=>{if(e.next==null)return;const r=e.formats(),s=e.next.formats();if(r.table!==s.table){const i=this.splitAfter(e);i&&i.optimize(),this.prev&&this.prev.optimize()}})}rowOffset(){return this.parent?this.parent.children.indexOf(this):-1}table(){return this.parent&&this.parent.parent}}x(Ht,"blotName","table-row"),x(Ht,"tagName","TR");class Ct extends ye{}x(Ct,"blotName","table-body"),x(Ct,"tagName","TBODY");class Ge extends ye{balanceCells(){const t=this.descendants(Ht),e=t.reduce((r,s)=>Math.max(s.children.length,r),0);t.forEach(r=>{new Array(e-r.children.length).fill(0).forEach(()=>{let s;r.children.head!=null&&(s=vt.formats(r.children.head.domNode));const i=this.scroll.create(vt.blotName,s);r.appendChild(i),i.optimize()})})}cells(t){return this.rows().map(e=>e.children.at(t))}deleteColumn(t){const[e]=this.descendant(Ct);e==null||e.children.head==null||e.children.forEach(r=>{const s=r.children.at(t);s!=null&&s.remove()})}insertColumn(t){const[e]=this.descendant(Ct);e==null||e.children.head==null||e.children.forEach(r=>{const s=r.children.at(t),i=vt.formats(r.children.head.domNode),l=this.scroll.create(vt.blotName,i);r.insertBefore(l,s)})}insertRow(t){const[e]=this.descendant(Ct);if(e==null||e.children.head==null)return;const r=Di(),s=this.scroll.create(Ht.blotName);e.children.head.children.forEach(()=>{const l=this.scroll.create(vt.blotName,r);s.appendChild(l)});const i=e.children.at(t);e.insertBefore(s,i)}rows(){const t=this.children.head;return t==null?[]:t.children.map(e=>e)}}x(Ge,"blotName","table-container"),x(Ge,"tagName","TABLE");Ge.allowedChildren=[Ct];Ct.requiredContainer=Ge;Ct.allowedChildren=[Ht];Ht.requiredContainer=Ct;Ht.allowedChildren=[vt];vt.requiredContainer=Ht;function Di(){return`row-${Math.random().toString(36).slice(2,6)}`}class fg extends Tt{static register(){N.register(vt),N.register(Ht),N.register(Ct),N.register(Ge)}constructor(){super(...arguments),this.listenBalanceCells()}balanceTables(){this.quill.scroll.descendants(Ge).forEach(t=>{t.balanceCells()})}deleteColumn(){const[t,,e]=this.getTable();e!=null&&(t.deleteColumn(e.cellOffset()),this.quill.update(N.sources.USER))}deleteRow(){const[,t]=this.getTable();t!=null&&(t.remove(),this.quill.update(N.sources.USER))}deleteTable(){const[t]=this.getTable();if(t==null)return;const e=t.offset();t.remove(),this.quill.update(N.sources.USER),this.quill.setSelection(e,N.sources.SILENT)}getTable(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.quill.getSelection();if(t==null)return[null,null,null,-1];const[e,r]=this.quill.getLine(t.index);if(e==null||e.statics.blotName!==vt.blotName)return[null,null,null,-1];const s=e.parent;return[s.parent.parent,s,e,r]}insertColumn(t){const e=this.quill.getSelection();if(!e)return;const[r,s,i]=this.getTable(e);if(i==null)return;const l=i.cellOffset();r.insertColumn(l+t),this.quill.update(N.sources.USER);let a=s.rowOffset();t===0&&(a+=1),this.quill.setSelection(e.index+a,e.length,N.sources.SILENT)}insertColumnLeft(){this.insertColumn(0)}insertColumnRight(){this.insertColumn(1)}insertRow(t){const e=this.quill.getSelection();if(!e)return;const[r,s,i]=this.getTable(e);if(i==null)return;const l=s.rowOffset();r.insertRow(l+t),this.quill.update(N.sources.USER),t>0?this.quill.setSelection(e,N.sources.SILENT):this.quill.setSelection(e.index+s.children.length,e.length,N.sources.SILENT)}insertRowAbove(){this.insertRow(0)}insertRowBelow(){this.insertRow(1)}insertTable(t,e){const r=this.quill.getSelection();if(r==null)return;const s=new Array(t).fill(0).reduce(i=>{const l=new Array(e).fill(`
`).join("");return i.insert(l,{table:Di()})},new O().retain(r.index));this.quill.updateContents(s,N.sources.USER),this.quill.setSelection(r.index,N.sources.SILENT),this.balanceTables()}listenBalanceCells(){this.quill.on(N.events.SCROLL_OPTIMIZE,t=>{t.some(e=>["TD","TR","TBODY","TABLE"].includes(e.target.tagName)?(this.quill.once(N.events.TEXT_CHANGE,(r,s,i)=>{i===N.sources.USER&&this.balanceTables()}),!0):!1)})}}const $l=Gt("quill:toolbar");class ji extends Tt{constructor(t,e){var r,s;if(super(t,e),Array.isArray(this.options.container)){const i=document.createElement("div");i.setAttribute("role","toolbar"),dg(i,this.options.container),(s=(r=t.container)==null?void 0:r.parentNode)==null||s.insertBefore(i,t.container),this.container=i}else typeof this.options.container=="string"?this.container=document.querySelector(this.options.container):this.container=this.options.container;if(!(this.container instanceof HTMLElement)){$l.error("Container required for toolbar",this.options);return}this.container.classList.add("ql-toolbar"),this.controls=[],this.handlers={},this.options.handlers&&Object.keys(this.options.handlers).forEach(i=>{var a;const l=(a=this.options.handlers)==null?void 0:a[i];l&&this.addHandler(i,l)}),Array.from(this.container.querySelectorAll("button, select")).forEach(i=>{this.attach(i)}),this.quill.on(N.events.EDITOR_CHANGE,()=>{const[i]=this.quill.selection.getRange();this.update(i)})}addHandler(t,e){this.handlers[t]=e}attach(t){let e=Array.from(t.classList).find(s=>s.indexOf("ql-")===0);if(!e)return;if(e=e.slice(3),t.tagName==="BUTTON"&&t.setAttribute("type","button"),this.handlers[e]==null&&this.quill.scroll.query(e)==null){$l.warn("ignoring attaching to nonexistent format",e,t);return}const r=t.tagName==="SELECT"?"change":"click";t.addEventListener(r,s=>{let i;if(t.tagName==="SELECT"){if(t.selectedIndex<0)return;const a=t.options[t.selectedIndex];a.hasAttribute("selected")?i=!1:i=a.value||!1}else t.classList.contains("ql-active")?i=!1:i=t.value||!t.hasAttribute("value"),s.preventDefault();this.quill.focus();const[l]=this.quill.selection.getRange();if(this.handlers[e]!=null)this.handlers[e].call(this,i);else if(this.quill.scroll.query(e).prototype instanceof lt){if(i=prompt(`Enter ${e}`),!i)return;this.quill.updateContents(new O().retain(l.index).delete(l.length).insert({[e]:i}),N.sources.USER)}else this.quill.format(e,i,N.sources.USER);this.update(l)}),this.controls.push([e,t])}update(t){const e=t==null?{}:this.quill.getFormat(t);this.controls.forEach(r=>{const[s,i]=r;if(i.tagName==="SELECT"){let l=null;if(t==null)l=null;else if(e[s]==null)l=i.querySelector("option[selected]");else if(!Array.isArray(e[s])){let a=e[s];typeof a=="string"&&(a=a.replace(/"/g,'\\"')),l=i.querySelector(`option[value="${a}"]`)}l==null?(i.value="",i.selectedIndex=-1):l.selected=!0}else if(t==null)i.classList.remove("ql-active"),i.setAttribute("aria-pressed","false");else if(i.hasAttribute("value")){const l=e[s],a=l===i.getAttribute("value")||l!=null&&l.toString()===i.getAttribute("value")||l==null&&!i.getAttribute("value");i.classList.toggle("ql-active",a),i.setAttribute("aria-pressed",a.toString())}else{const l=e[s]!=null;i.classList.toggle("ql-active",l),i.setAttribute("aria-pressed",l.toString())}})}}ji.DEFAULTS={};function Dl(n,t,e){const r=document.createElement("button");r.setAttribute("type","button"),r.classList.add(`ql-${t}`),r.setAttribute("aria-pressed","false"),e!=null?(r.value=e,r.setAttribute("aria-label",`${t}: ${e}`)):r.setAttribute("aria-label",t),n.appendChild(r)}function dg(n,t){Array.isArray(t[0])||(t=[t]),t.forEach(e=>{const r=document.createElement("span");r.classList.add("ql-formats"),e.forEach(s=>{if(typeof s=="string")Dl(r,s);else{const i=Object.keys(s)[0],l=s[i];Array.isArray(l)?gg(r,i,l):Dl(r,i,l)}}),n.appendChild(r)})}function gg(n,t,e){const r=document.createElement("select");r.classList.add(`ql-${t}`),e.forEach(s=>{const i=document.createElement("option");s!==!1?i.setAttribute("value",String(s)):i.setAttribute("selected","selected"),r.appendChild(i)}),n.appendChild(r)}ji.DEFAULTS={container:null,handlers:{clean(){const n=this.quill.getSelection();if(n!=null)if(n.length===0){const t=this.quill.getFormat();Object.keys(t).forEach(e=>{this.quill.scroll.query(e,q.INLINE)!=null&&this.quill.format(e,!1,N.sources.USER)})}else this.quill.removeFormat(n.index,n.length,N.sources.USER)},direction(n){const{align:t}=this.quill.getFormat();n==="rtl"&&t==null?this.quill.format("align","right",N.sources.USER):!n&&t==="right"&&this.quill.format("align",!1,N.sources.USER),this.quill.format("direction",n,N.sources.USER)},indent(n){const t=this.quill.getSelection(),e=this.quill.getFormat(t),r=parseInt(e.indent||0,10);if(n==="+1"||n==="-1"){let s=n==="+1"?1:-1;e.direction==="rtl"&&(s*=-1),this.quill.format("indent",r+s,N.sources.USER)}},link(n){n===!0&&(n=prompt("Enter link URL:")),this.quill.format("link",n,N.sources.USER)},list(n){const t=this.quill.getSelection(),e=this.quill.getFormat(t);n==="check"?e.list==="checked"||e.list==="unchecked"?this.quill.format("list",!1,N.sources.USER):this.quill.format("list","unchecked",N.sources.USER):this.quill.format("list",n,N.sources.USER)}}};const pg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="13" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="9" y1="4" y2="4"/></svg>',mg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="14" x2="4" y1="14" y2="14"/><line class="ql-stroke" x1="12" x2="6" y1="4" y2="4"/></svg>',bg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="5" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="9" y1="4" y2="4"/></svg>',yg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="15" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="15" x2="3" y1="14" y2="14"/><line class="ql-stroke" x1="15" x2="3" y1="4" y2="4"/></svg>',vg='<svg viewbox="0 0 18 18"><g class="ql-fill ql-color-label"><polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"/><rect height="1" width="1" x="4" y="4"/><polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"/><rect height="1" width="1" x="2" y="6"/><rect height="1" width="1" x="3" y="5"/><rect height="1" width="1" x="4" y="7"/><polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"/><rect height="1" width="1" x="2" y="12"/><rect height="1" width="1" x="2" y="9"/><rect height="1" width="1" x="2" y="15"/><polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"/><rect height="1" width="1" x="3" y="8"/><path d="M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z"/><path d="M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z"/><path d="M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z"/><rect height="1" width="1" x="12" y="2"/><rect height="1" width="1" x="11" y="3"/><path d="M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z"/><rect height="1" width="1" x="2" y="3"/><rect height="1" width="1" x="6" y="2"/><rect height="1" width="1" x="3" y="2"/><rect height="1" width="1" x="5" y="3"/><rect height="1" width="1" x="9" y="2"/><rect height="1" width="1" x="15" y="14"/><polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"/><rect height="1" width="1" x="13" y="7"/><rect height="1" width="1" x="15" y="5"/><rect height="1" width="1" x="14" y="6"/><rect height="1" width="1" x="15" y="8"/><rect height="1" width="1" x="14" y="9"/><path d="M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z"/><rect height="1" width="1" x="14" y="3"/><polygon points="12 6.868 12 6 11.62 6 12 6.868"/><rect height="1" width="1" x="15" y="2"/><rect height="1" width="1" x="12" y="5"/><rect height="1" width="1" x="13" y="4"/><polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"/><rect height="1" width="1" x="9" y="14"/><rect height="1" width="1" x="8" y="15"/><path d="M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z"/><rect height="1" width="1" x="5" y="15"/><path d="M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z"/><rect height="1" width="1" x="11" y="15"/><path d="M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z"/><rect height="1" width="1" x="14" y="15"/><rect height="1" width="1" x="15" y="11"/></g><polyline class="ql-stroke" points="5.5 13 9 5 12.5 13"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="11" y2="11"/></svg>',Eg='<svg viewbox="0 0 18 18"><rect class="ql-fill ql-stroke" height="3" width="3" x="4" y="5"/><rect class="ql-fill ql-stroke" height="3" width="3" x="11" y="5"/><path class="ql-even ql-fill ql-stroke" d="M7,8c0,4.031-3,5-3,5"/><path class="ql-even ql-fill ql-stroke" d="M14,8c0,4.031-3,5-3,5"/></svg>',Ag='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z"/><path class="ql-stroke" d="M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z"/></svg>',Ng='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="5" x2="13" y1="3" y2="3"/><line class="ql-stroke" x1="6" x2="9.35" y1="12" y2="3"/><line class="ql-stroke" x1="11" x2="15" y1="11" y2="15"/><line class="ql-stroke" x1="15" x2="11" y1="11" y2="15"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="7" x="2" y="14"/></svg>',jl='<svg viewbox="0 0 18 18"><polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"/><polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"/><line class="ql-stroke" x1="10" x2="8" y1="5" y2="13"/></svg>',wg='<svg viewbox="0 0 18 18"><line class="ql-color-label ql-stroke ql-transparent" x1="3" x2="15" y1="15" y2="15"/><polyline class="ql-stroke" points="5.5 11 9 3 12.5 11"/><line class="ql-stroke" x1="11.63" x2="6.38" y1="9" y2="9"/></svg>',Tg='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"/><line class="ql-stroke ql-fill" x1="15" x2="11" y1="4" y2="4"/><path class="ql-fill" d="M11,3a3,3,0,0,0,0,6h1V3H11Z"/><rect class="ql-fill" height="11" width="1" x="11" y="4"/><rect class="ql-fill" height="11" width="1" x="13" y="4"/></svg>',xg='<svg viewbox="0 0 18 18"><polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"/><line class="ql-stroke ql-fill" x1="9" x2="5" y1="4" y2="4"/><path class="ql-fill" d="M5,3A3,3,0,0,0,5,9H6V3H5Z"/><rect class="ql-fill" height="11" width="1" x="5" y="4"/><rect class="ql-fill" height="11" width="1" x="7" y="4"/></svg>',Lg='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z"/><rect class="ql-fill" height="1.6" rx="0.8" ry="0.8" width="5" x="5.15" y="6.2"/><path class="ql-fill" d="M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z"/></svg>',Sg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z"/></svg>',_g='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Og='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.65186,12.30664a2.6742,2.6742,0,0,1-2.915,2.68457,3.96592,3.96592,0,0,1-2.25537-.6709.56007.56007,0,0,1-.13232-.83594L11.64648,13c.209-.34082.48389-.36328.82471-.1543a2.32654,2.32654,0,0,0,1.12256.33008c.71484,0,1.12207-.35156,1.12207-.78125,0-.61523-.61621-.86816-1.46338-.86816H13.2085a.65159.65159,0,0,1-.68213-.41895l-.05518-.10937a.67114.67114,0,0,1,.14307-.78125l.71533-.86914a8.55289,8.55289,0,0,1,.68213-.7373V8.58887a3.93913,3.93913,0,0,1-.748.05469H11.9873a.54085.54085,0,0,1-.605-.60547V7.59863a.54085.54085,0,0,1,.605-.60547h3.75146a.53773.53773,0,0,1,.60547.59375v.17676a1.03723,1.03723,0,0,1-.27539.748L14.74854,10.0293A2.31132,2.31132,0,0,1,16.65186,12.30664ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',qg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm7.05371,7.96582v.38477c0,.39648-.165.60547-.46191.60547h-.47314v1.29785a.54085.54085,0,0,1-.605.60547h-.69336a.54085.54085,0,0,1-.605-.60547V12.95605H11.333a.5412.5412,0,0,1-.60547-.60547v-.15332a1.199,1.199,0,0,1,.22021-.748l2.56348-4.05957a.7819.7819,0,0,1,.72607-.39648h1.27637a.54085.54085,0,0,1,.605.60547v3.7627h.33008A.54055.54055,0,0,1,17.05371,11.96582ZM14.28125,8.7207h-.022a4.18969,4.18969,0,0,1-.38525.81348l-1.188,1.80469v.02246h1.5293V9.60059A7.04058,7.04058,0,0,1,14.28125,8.7207Z"/></svg>',Cg='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M16.74023,12.18555a2.75131,2.75131,0,0,1-2.91553,2.80566,3.908,3.908,0,0,1-2.25537-.68164.54809.54809,0,0,1-.13184-.8252L11.73438,13c.209-.34082.48389-.36328.8252-.1543a2.23757,2.23757,0,0,0,1.1001.33008,1.01827,1.01827,0,0,0,1.1001-.96777c0-.61621-.53906-.97949-1.25439-.97949a2.15554,2.15554,0,0,0-.64893.09961,1.15209,1.15209,0,0,1-.814.01074l-.12109-.04395a.64116.64116,0,0,1-.45117-.71484l.231-3.00391a.56666.56666,0,0,1,.62744-.583H15.541a.54085.54085,0,0,1,.605.60547v.43945a.54085.54085,0,0,1-.605.60547H13.41748l-.04395.72559a1.29306,1.29306,0,0,1-.04395.30859h.022a2.39776,2.39776,0,0,1,.57227-.07715A2.53266,2.53266,0,0,1,16.74023,12.18555ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z"/></svg>',Ig='<svg viewBox="0 0 18 18"><path class="ql-fill" d="M14.51758,9.64453a1.85627,1.85627,0,0,0-1.24316.38477H13.252a1.73532,1.73532,0,0,1,1.72754-1.4082,2.66491,2.66491,0,0,1,.5498.06641c.35254.05469.57227.01074.70508-.40723l.16406-.5166a.53393.53393,0,0,0-.373-.75977,4.83723,4.83723,0,0,0-1.17773-.14258c-2.43164,0-3.7627,2.17773-3.7627,4.43359,0,2.47559,1.60645,3.69629,3.19043,3.69629A2.70585,2.70585,0,0,0,16.96,12.19727,2.43861,2.43861,0,0,0,14.51758,9.64453Zm-.23047,3.58691c-.67187,0-1.22168-.81445-1.22168-1.45215,0-.47363.30762-.583.72559-.583.96875,0,1.27734.59375,1.27734,1.12207A.82182.82182,0,0,1,14.28711,13.23145ZM10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Z"/></svg>',Rg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="13" y1="4" y2="4"/><line class="ql-stroke" x1="5" x2="11" y1="14" y2="14"/><line class="ql-stroke" x1="8" x2="10" y1="14" y2="4"/></svg>',kg='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="10" width="12" x="3" y="4"/><circle class="ql-fill" cx="6" cy="7" r="1"/><polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"/></svg>',Bg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"/></svg>',Mg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="3" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="5 7 5 11 3 9 5 7"/></svg>',$g='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="11" y1="7" y2="11"/><path class="ql-even ql-stroke" d="M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z"/><path class="ql-even ql-stroke" d="M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z"/></svg>',Dg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="6" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="6" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="6" x2="15" y1="14" y2="14"/><line class="ql-stroke" x1="3" x2="3" y1="4" y2="4"/><line class="ql-stroke" x1="3" x2="3" y1="9" y2="9"/><line class="ql-stroke" x1="3" x2="3" y1="14" y2="14"/></svg>',jg='<svg class="" viewbox="0 0 18 18"><line class="ql-stroke" x1="9" x2="15" y1="4" y2="4"/><polyline class="ql-stroke" points="3 4 4 5 6 3"/><line class="ql-stroke" x1="9" x2="15" y1="14" y2="14"/><polyline class="ql-stroke" points="3 14 4 15 6 13"/><line class="ql-stroke" x1="9" x2="15" y1="9" y2="9"/><polyline class="ql-stroke" points="3 9 4 10 6 8"/></svg>',Pg='<svg viewbox="0 0 18 18"><line class="ql-stroke" x1="7" x2="15" y1="4" y2="4"/><line class="ql-stroke" x1="7" x2="15" y1="9" y2="9"/><line class="ql-stroke" x1="7" x2="15" y1="14" y2="14"/><line class="ql-stroke ql-thin" x1="2.5" x2="4.5" y1="5.5" y2="5.5"/><path class="ql-fill" d="M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z"/><path class="ql-stroke ql-thin" d="M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156"/><path class="ql-stroke ql-thin" d="M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109"/></svg>',Ug='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z"/><path class="ql-fill" d="M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z"/></svg>',Fg='<svg viewbox="0 0 18 18"><path class="ql-fill" d="M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z"/><path class="ql-fill" d="M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z"/></svg>',Hg='<svg viewbox="0 0 18 18"><line class="ql-stroke ql-thin" x1="15.5" x2="2.5" y1="8.5" y2="9.5"/><path class="ql-fill" d="M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z"/><path class="ql-fill" d="M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z"/></svg>',zg='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="2" width="3" x="5" y="5"/><rect class="ql-fill" height="2" width="4" x="9" y="5"/><g class="ql-fill ql-transparent"><rect height="2" width="3" x="5" y="8"/><rect height="2" width="4" x="9" y="8"/><rect height="2" width="3" x="5" y="11"/><rect height="2" width="4" x="9" y="11"/></g></svg>',Gg='<svg viewbox="0 0 18 18"><path class="ql-stroke" d="M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3"/><rect class="ql-fill" height="1" rx="0.5" ry="0.5" width="12" x="3" y="15"/></svg>',Kg='<svg viewbox="0 0 18 18"><rect class="ql-stroke" height="12" width="12" x="3" y="3"/><rect class="ql-fill" height="12" width="1" x="5" y="3"/><rect class="ql-fill" height="12" width="1" x="12" y="3"/><rect class="ql-fill" height="2" width="8" x="5" y="8"/><rect class="ql-fill" height="1" width="3" x="3" y="5"/><rect class="ql-fill" height="1" width="3" x="3" y="7"/><rect class="ql-fill" height="1" width="3" x="3" y="10"/><rect class="ql-fill" height="1" width="3" x="3" y="12"/><rect class="ql-fill" height="1" width="3" x="12" y="5"/><rect class="ql-fill" height="1" width="3" x="12" y="7"/><rect class="ql-fill" height="1" width="3" x="12" y="10"/><rect class="ql-fill" height="1" width="3" x="12" y="12"/></svg>',Sn={align:{"":pg,center:mg,right:bg,justify:yg},background:vg,blockquote:Eg,bold:Ag,clean:Ng,code:jl,"code-block":jl,color:wg,direction:{"":Tg,rtl:xg},formula:Lg,header:{1:Sg,2:_g,3:Og,4:qg,5:Cg,6:Ig},italic:Rg,image:kg,indent:{"+1":Bg,"-1":Mg},link:$g,list:{bullet:Dg,check:jg,ordered:Pg},script:{sub:Ug,super:Fg},strike:Hg,table:zg,underline:Gg,video:Kg},Vg='<svg viewbox="0 0 18 18"><polygon class="ql-stroke" points="7 11 9 13 11 11 7 11"/><polygon class="ql-stroke" points="7 7 9 5 11 7 7 7"/></svg>';let Pl=0;function Ul(n,t){n.setAttribute(t,`${n.getAttribute(t)!=="true"}`)}class kr{constructor(t){this.select=t,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",()=>{this.togglePicker()}),this.label.addEventListener("keydown",e=>{switch(e.key){case"Enter":this.togglePicker();break;case"Escape":this.escape(),e.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}togglePicker(){this.container.classList.toggle("ql-expanded"),Ul(this.label,"aria-expanded"),Ul(this.options,"aria-hidden")}buildItem(t){const e=document.createElement("span");e.tabIndex="0",e.setAttribute("role","button"),e.classList.add("ql-picker-item");const r=t.getAttribute("value");return r&&e.setAttribute("data-value",r),t.textContent&&e.setAttribute("data-label",t.textContent),e.addEventListener("click",()=>{this.selectItem(e,!0)}),e.addEventListener("keydown",s=>{switch(s.key){case"Enter":this.selectItem(e,!0),s.preventDefault();break;case"Escape":this.escape(),s.preventDefault();break}}),e}buildLabel(){const t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=Vg,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}buildOptions(){const t=document.createElement("span");t.classList.add("ql-picker-options"),t.setAttribute("aria-hidden","true"),t.tabIndex="-1",t.id=`ql-picker-options-${Pl}`,Pl+=1,this.label.setAttribute("aria-controls",t.id),this.options=t,Array.from(this.select.options).forEach(e=>{const r=this.buildItem(e);t.appendChild(r),e.selected===!0&&this.selectItem(r)}),this.container.appendChild(t)}buildPicker(){Array.from(this.select.attributes).forEach(t=>{this.container.setAttribute(t.name,t.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}escape(){this.close(),setTimeout(()=>this.label.focus(),1)}close(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}selectItem(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;const r=this.container.querySelector(".ql-selected");t!==r&&(r!=null&&r.classList.remove("ql-selected"),t!=null&&(t.classList.add("ql-selected"),this.select.selectedIndex=Array.from(t.parentNode.children).indexOf(t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e&&(this.select.dispatchEvent(new Event("change")),this.close())))}update(){let t;if(this.select.selectedIndex>-1){const r=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(r)}else this.selectItem(null);const e=t!=null&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",e)}}class Zo extends kr{constructor(t,e){super(t),this.label.innerHTML=e,this.container.classList.add("ql-color-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).slice(0,7).forEach(r=>{r.classList.add("ql-primary")})}buildItem(t){const e=super.buildItem(t);return e.style.backgroundColor=t.getAttribute("value")||"",e}selectItem(t,e){super.selectItem(t,e);const r=this.label.querySelector(".ql-color-label"),s=t&&t.getAttribute("data-value")||"";r&&(r.tagName==="line"?r.style.stroke=s:r.style.fill=s)}}class Xo extends kr{constructor(t,e){super(t),this.container.classList.add("ql-icon-picker"),Array.from(this.container.querySelectorAll(".ql-picker-item")).forEach(r=>{r.innerHTML=e[r.getAttribute("data-value")||""]}),this.defaultItem=this.container.querySelector(".ql-selected"),this.selectItem(this.defaultItem)}selectItem(t,e){super.selectItem(t,e);const r=t||this.defaultItem;if(r!=null){if(this.label.innerHTML===r.innerHTML)return;this.label.innerHTML=r.innerHTML}}}const Wg=n=>{const{overflowY:t}=getComputedStyle(n,null);return t!=="visible"&&t!=="clip"};class Yo{constructor(t,e){this.quill=t,this.boundsContainer=e||document.body,this.root=t.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,Wg(this.quill.root)&&this.quill.root.addEventListener("scroll",()=>{this.root.style.marginTop=`${-1*this.quill.root.scrollTop}px`}),this.hide()}hide(){this.root.classList.add("ql-hidden")}position(t){const e=t.left+t.width/2-this.root.offsetWidth/2,r=t.bottom+this.quill.root.scrollTop;this.root.style.left=`${e}px`,this.root.style.top=`${r}px`,this.root.classList.remove("ql-flip");const s=this.boundsContainer.getBoundingClientRect(),i=this.root.getBoundingClientRect();let l=0;if(i.right>s.right&&(l=s.right-i.right,this.root.style.left=`${e+l}px`),i.left<s.left&&(l=s.left-i.left,this.root.style.left=`${e+l}px`),i.bottom>s.bottom){const a=i.bottom-i.top,c=t.bottom-t.top+a;this.root.style.top=`${r-c}px`,this.root.classList.add("ql-flip")}return l}show(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}const Zg=[!1,"center","right","justify"],Xg=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],Yg=[!1,"serif","monospace"],Qg=["1","2","3",!1],Jg=["small",!1,"large","huge"];class kn extends ze{constructor(t,e){super(t,e);const r=s=>{if(!document.body.contains(t.root)){document.body.removeEventListener("click",r);return}this.tooltip!=null&&!this.tooltip.root.contains(s.target)&&document.activeElement!==this.tooltip.textbox&&!this.quill.hasFocus()&&this.tooltip.hide(),this.pickers!=null&&this.pickers.forEach(i=>{i.container.contains(s.target)||i.close()})};t.emitter.listenDOM("click",document.body,r)}addModule(t){const e=super.addModule(t);return t==="toolbar"&&this.extendToolbar(e),e}buildButtons(t,e){Array.from(t).forEach(r=>{(r.getAttribute("class")||"").split(/\s+/).forEach(i=>{if(i.startsWith("ql-")&&(i=i.slice(3),e[i]!=null))if(i==="direction")r.innerHTML=e[i][""]+e[i].rtl;else if(typeof e[i]=="string")r.innerHTML=e[i];else{const l=r.value||"";l!=null&&e[i][l]&&(r.innerHTML=e[i][l])}})})}buildPickers(t,e){this.pickers=Array.from(t).map(s=>{if(s.classList.contains("ql-align")&&(s.querySelector("option")==null&&gn(s,Zg),typeof e.align=="object"))return new Xo(s,e.align);if(s.classList.contains("ql-background")||s.classList.contains("ql-color")){const i=s.classList.contains("ql-background")?"background":"color";return s.querySelector("option")==null&&gn(s,Xg,i==="background"?"#ffffff":"#000000"),new Zo(s,e[i])}return s.querySelector("option")==null&&(s.classList.contains("ql-font")?gn(s,Yg):s.classList.contains("ql-header")?gn(s,Qg):s.classList.contains("ql-size")&&gn(s,Jg)),new kr(s)});const r=()=>{this.pickers.forEach(s=>{s.update()})};this.quill.on(S.events.EDITOR_CHANGE,r)}}kn.DEFAULTS=Jt({},ze.DEFAULTS,{modules:{toolbar:{handlers:{formula(){this.quill.theme.tooltip.edit("formula")},image(){let n=this.container.querySelector("input.ql-image[type=file]");n==null&&(n=document.createElement("input"),n.setAttribute("type","file"),n.setAttribute("accept",this.quill.uploader.options.mimetypes.join(", ")),n.classList.add("ql-image"),n.addEventListener("change",()=>{const t=this.quill.getSelection(!0);this.quill.uploader.upload(t,n.files),n.value=""}),this.container.appendChild(n)),n.click()},video(){this.quill.theme.tooltip.edit("video")}}}}});class Qo extends Yo{constructor(t,e){super(t,e),this.textbox=this.root.querySelector('input[type="text"]'),this.listen()}listen(){this.textbox.addEventListener("keydown",t=>{t.key==="Enter"?(this.save(),t.preventDefault()):t.key==="Escape"&&(this.cancel(),t.preventDefault())})}cancel(){this.hide(),this.restoreFocus()}edit(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),this.textbox==null)return;e!=null?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value="");const r=this.quill.getBounds(this.quill.selection.savedRange);r!=null&&this.position(r),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute(`data-${t}`)||""),this.root.setAttribute("data-mode",t)}restoreFocus(){this.quill.focus({preventScroll:!0})}save(){let{value:t}=this.textbox;switch(this.root.getAttribute("data-mode")){case"link":{const{scrollTop:e}=this.quill.root;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,S.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,S.sources.USER)),this.quill.root.scrollTop=e;break}case"video":t=tp(t);case"formula":{if(!t)break;const e=this.quill.getSelection(!0);if(e!=null){const r=e.index+e.length;this.quill.insertEmbed(r,this.root.getAttribute("data-mode"),t,S.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(r+1," ",S.sources.USER),this.quill.setSelection(r+2,S.sources.USER)}break}}this.textbox.value="",this.hide()}}function tp(n){let t=n.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||n.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return t?`${t[1]||"https"}://www.youtube.com/embed/${t[2]}?showinfo=0`:(t=n.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?`${t[1]||"https"}://player.vimeo.com/video/${t[2]}/`:n}function gn(n,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;t.forEach(r=>{const s=document.createElement("option");r===e?s.setAttribute("selected","selected"):s.setAttribute("value",String(r)),n.appendChild(s)})}const ep=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]];class Jo extends Qo{constructor(t,e){super(t,e),this.quill.on(S.events.EDITOR_CHANGE,(r,s,i,l)=>{if(r===S.events.SELECTION_CHANGE)if(s!=null&&s.length>0&&l===S.sources.USER){this.show(),this.root.style.left="0px",this.root.style.width="",this.root.style.width=`${this.root.offsetWidth}px`;const a=this.quill.getLines(s.index,s.length);if(a.length===1){const c=this.quill.getBounds(s);c!=null&&this.position(c)}else{const c=a[a.length-1],h=this.quill.getIndex(c),d=Math.min(c.length()-1,s.index+s.length-h),m=this.quill.getBounds(new ge(h,d));m!=null&&this.position(m)}}else document.activeElement!==this.textbox&&this.quill.hasFocus()&&this.hide()})}listen(){super.listen(),this.root.querySelector(".ql-close").addEventListener("click",()=>{this.root.classList.remove("ql-editing")}),this.quill.on(S.events.SCROLL_OPTIMIZE,()=>{setTimeout(()=>{if(this.root.classList.contains("ql-hidden"))return;const t=this.quill.getSelection();if(t!=null){const e=this.quill.getBounds(t);e!=null&&this.position(e)}},1)})}cancel(){this.show()}position(t){const e=super.position(t),r=this.root.querySelector(".ql-tooltip-arrow");return r.style.marginLeft="",e!==0&&(r.style.marginLeft=`${-1*e-r.offsetWidth/2}px`),e}}x(Jo,"TEMPLATE",['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""));class ta extends kn{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=ep),super(t,e),this.quill.container.classList.add("ql-bubble")}extendToolbar(t){this.tooltip=new Jo(this.quill,this.options.bounds),t.container!=null&&(this.tooltip.root.appendChild(t.container),this.buildButtons(t.container.querySelectorAll("button"),Sn),this.buildPickers(t.container.querySelectorAll("select"),Sn))}}ta.DEFAULTS=Jt({},kn.DEFAULTS,{modules:{toolbar:{handlers:{link(n){n?this.quill.theme.tooltip.edit():this.quill.format("link",!1,N.sources.USER)}}}}});const np=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]];class ea extends Qo{constructor(){super(...arguments);x(this,"preview",this.root.querySelector("a.ql-preview"))}listen(){super.listen(),this.root.querySelector("a.ql-action").addEventListener("click",e=>{this.root.classList.contains("ql-editing")?this.save():this.edit("link",this.preview.textContent),e.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",e=>{if(this.linkRange!=null){const r=this.linkRange;this.restoreFocus(),this.quill.formatText(r,"link",!1,S.sources.USER),delete this.linkRange}e.preventDefault(),this.hide()}),this.quill.on(S.events.SELECTION_CHANGE,(e,r,s)=>{if(e!=null){if(e.length===0&&s===S.sources.USER){const[i,l]=this.quill.scroll.descendant(Qt,e.index);if(i!=null){this.linkRange=new ge(e.index-l,i.length());const a=Qt.formats(i.domNode);this.preview.textContent=a,this.preview.setAttribute("href",a),this.show();const c=this.quill.getBounds(this.linkRange);c!=null&&this.position(c);return}}else delete this.linkRange;this.hide()}})}show(){super.show(),this.root.removeAttribute("data-mode")}}x(ea,"TEMPLATE",['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""));class na extends kn{constructor(t,e){e.modules.toolbar!=null&&e.modules.toolbar.container==null&&(e.modules.toolbar.container=np),super(t,e),this.quill.container.classList.add("ql-snow")}extendToolbar(t){t.container!=null&&(t.container.classList.add("ql-snow"),this.buildButtons(t.container.querySelectorAll("button"),Sn),this.buildPickers(t.container.querySelectorAll("select"),Sn),this.tooltip=new ea(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"k",shortKey:!0},(e,r)=>{t.handlers.link.call(t,!r.format.link)}))}}na.DEFAULTS=Jt({},kn.DEFAULTS,{modules:{toolbar:{handlers:{link(n){if(n){const t=this.quill.getSelection();if(t==null||t.length===0)return;let e=this.quill.getText(t);/^\S+@\S+\.\S+$/.test(e)&&e.indexOf("mailto:")!==0&&(e=`mailto:${e}`);const{tooltip:r}=this.quill.theme;r.edit("link",e)}else this.quill.format("link",!1,N.sources.USER)}}}}});N.register({"attributors/attribute/direction":Bo,"attributors/class/align":Io,"attributors/class/background":Ed,"attributors/class/color":vd,"attributors/class/direction":Mo,"attributors/class/font":jo,"attributors/class/size":Uo,"attributors/style/align":Ro,"attributors/style/background":Ri,"attributors/style/color":Ii,"attributors/style/direction":$o,"attributors/style/font":Po,"attributors/style/size":Fo},!0);N.register({"formats/align":Io,"formats/direction":Mo,"formats/indent":ug,"formats/background":Ri,"formats/color":Ii,"formats/font":jo,"formats/size":Uo,"formats/blockquote":oi,"formats/code-block":J,"formats/header":ai,"formats/list":Rn,"formats/bold":Ln,"formats/code":ki,"formats/italic":ci,"formats/link":Qt,"formats/script":ui,"formats/strike":hi,"formats/underline":fi,"formats/formula":mr,"formats/image":di,"formats/video":br,"modules/syntax":Wo,"modules/table":fg,"modules/toolbar":ji,"themes/bubble":ta,"themes/snow":na,"ui/icons":Sn,"ui/picker":kr,"ui/icon-picker":Xo,"ui/color-picker":Zo,"ui/tooltip":Yo},!0);const up=nt.AttributeMap,hp=nt.Op,fp=nt.OpIterator;export{up as AttributeMap,O as Delta,Tt as Module,hp as Op,fp as OpIterator,Uf as Parchment,ge as Range,N as default};
