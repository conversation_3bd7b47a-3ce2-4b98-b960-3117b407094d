import axios from 'axios';
import { toRaw } from 'vue';
import { removeEmptyParams } from '@/_common/api/helpers/Parameters';
import { parseFilters as parseStandardFilters } from '@/_common/api/helpers/StandardFiltersBuilder';
import { parseSorters as parseStandardSorters } from '@/_common/api/helpers/StandardSortersBuilder';

async function summary(params, year) {
    // Add year to params if it's provided
    const filterParams = { year: year, ...params };

    const filter = JSON.stringify(filterParams);
    const summary = (await axios.get(`/apps/accordo2/synthesis?filter=${filter}`))?.data;

    if (summary?.data) {
        summary.data[summary.data.length - 1].pop();
        return summary.data;
    }
    return null;
}

async function detail(queryParams, year) {
    const filters = toRaw(queryParams?.filters) || {};
    const sorters = toRaw(queryParams?.sorters);

    // Add year to filters if provided
    if (year) {
        filters.year = { operation: 'eq', value: year };
    }

    const params = removeEmptyParams({
        page: queryParams?.page || null,
        count: queryParams?.pageSize || null,
        filter: parseStandardFilters(filters) || null,
        sorting: parseStandardSorters(sorters) || null,
    });

    const agencies = (await axios.get('/apps/accordo2/details?filter=', { params }))?.data;
    if (agencies?.data) {
        return agencies.data;
    }
    return [];
}

async function updateAgreement(agreementId, data) {
  const response = await axios.put(`/apps/accordo2/agreement/${agreementId}/update`, data);
  return response.data;
}

async function getAgreement(agencyId, year) {
    const agreement = (await axios.get(`/apps/accordo2/agreement/${agencyId}/${year}`))?.data;

    if (agreement?.data) {
        return agreement.data;
    }
    return null;
}

async function revisions(agreementId) {
    const revisions = (await axios.get(`/apps/accordo2/revisions/${agreementId}`))?.data;

    if (revisions?.data) {
        return revisions.data;
    }
    return [];
}

async function setupObj(year) {
    const setupObj = (await axios.get(`/apps/accordo2/setupObj/${year}`))?.data;

    if (setupObj?.data) {
        return setupObj.data;
    }
    return null;
}

async function saveSetupObj(year, data) {
    const response = (await axios.put(`/apps/accordo2/setupObj/${year}`, data))?.data;
    return response.data;
}

async function vitaSlots(fascia, year) {
    const vitaSlots = (await axios.get(`/apps/accordo2/obj/life/${fascia}/${year}`))?.data;

    if (vitaSlots?.data) {
        return vitaSlots.data;
    }
    return [];
}

async function ramiPrefSlots(fascia, year) {
    const ramiPrefSlots = (await axios.get(`/apps/accordo2/obj/rp/${fascia}/${year}`))?.data;

    if (ramiPrefSlots?.data) {
        return ramiPrefSlots.data;
    }
    return [];
}

async function slots(year) {
    const slots = (await axios.get(`/apps/accordo2/slots/${year}`))?.data;

    if (slots?.data) {
        return slots.data;
    }
    return [];
}

async function getAgencyInfo(agencyId) {
    const agencyInfo = (await axios.get(`/apps/accordo2/agency/${agencyId}`))?.data;

    if (agencyInfo?.data) {
        return agencyInfo.data;
    }
    return null;
}

async function updateRevision(revisionId, data) {
  const response = await axios.put(`/apps/accordo2/revision/${revisionId}/create`, data);
  return response.data;
}

export const accordo2Api = {
    summary,
    detail,
    revisions,
    updateAgreement,
    updateRevision,
    setupObj,
    saveSetupObj,
    vitaSlots,
    ramiPrefSlots,
    getAgreement,
    slots,
    getAgencyInfo
}
