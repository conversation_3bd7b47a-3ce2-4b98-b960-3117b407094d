<script setup>
import {reactive, ref} from "vue";
import {Table} from "@/libs/table.js";
import {myPageAgencies} from "@/apps/my-page/api/myPageAgencies.js";
import Sorter from "@/_common/components/Table/Sorter.vue";
import Pagination from "@/_common/components/Table/Pagination.vue";
import {PhCheck, PhUserCheck, PhUserGear, PhUserMinus, PhWrench, PhX, PhDownload, PhArrowRight} from "@phosphor-icons/vue";

const columns = [
    {
        key: 'groupama_id',
        label: 'Cod. GA',
    },
    {
        key: 'agenzia_id',
        label: 'Cod. AGE',
    },
    {
        key: 'nome',
        label: 'Nome Agenzia',
    },
    {
        key: 'localita',
        label: 'Località',
    },
    {
        key: 'agents',
        label: 'Agente',
    },
    {
        key: 'status',
        label: 'Stato',
    },
    {
        key: 'hasAgencyPhoto',
        label: 'Immagine',
    },
    {
        key: 'detail',
        label: 'Completamento',
    },
    {
        key: 'privacy',
        label: 'Team',
    },
];
const filtered = {
    groupama_id: 'text',
    agenzia_id: 'text',
    nome: 'text',
    localita: 'text',
    agents: 'text',
};
const table = reactive(new Table(myPageAgencies.getAll, { sorters: { agenzia_id: 'ASC' }, pageSize: 50 }));
await table.fetchData();

function formatAgentsList(string) {
    return string?.replaceAll(', ', '<br>');
}

async function exportUrl() {
    window.location.href = myPageAgencies.getExportUrl({filters: table.filters, sorters: table.sorters});
}

let statuses = [
    { params: {standard: 0, approved: 0}, label: 'Da approvare' },
    { params: {standard: 1, approved: 1}, label: 'Standard' },
    { params: {standard: 0, approved: 1}, label: 'Approvate' }
];

let photoStatuses = [
    { value: 'SI', label: 'Inserita' },
    { value: 'NO', label: 'Mancante' }
];

let detailStatuses = [
    { value: 'FULL', label: 'Totale' },
    { value: 'PARTIAL', label: 'Parziale' },
    { value: 'EMPTY', label: 'Vuoto' }
];

let statusFilter = ref(null);
let photoFilter = ref(null);
let detailFilter = ref(null);

</script>

<template>
    <div class="container relative">
        <div class="card">
            <div class="card-header">
                <div class="title">
                    Elenco Agenzie
                </div>
                <div class="toolbar">
                    <button type="button" @click="() => exportUrl()" class="btn primary">
                        <PhDownload class="size-5" />
                        Scarica Excel
                    </button>
                </div>
            </div>
            <div class="card-body body-fit">
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                        <tr>
                            <th v-for="column in columns" :key="column.key">
                                <div class="th-label cursor-pointer" @click="() => table.toggleSort(column.key)">
                                    {{ column.label }}
                                    <Sorter :ordering="table.sorters[column.key]"/>
                                </div>
                                <div>
                                    <input v-if="filtered?.[column.key]"
                                           :type="filtered[column.key]"
                                           :value="table.filters[column.key]?.value"
                                           @input="e => table.applyFilters({
                                               [column.key]: {
                                                   operation: 'LIKE',
                                                   value: e.target.value,
                                               }
                                           })" />
                                    <select v-if="column.key === 'status'"
                                            :value="table.filters[column.key]?.value"
                                            v-model="statusFilter"
                                            @change="table.applyFilters({
                                                ['standard']: {
                                                    operation: 'EQ',
                                                    value: statusFilter.standard,
                                                },
                                                ['approved']: {
                                                    operation: 'EQ',
                                                    value: statusFilter.approved,
                                                }
                                            })">
                                        <option value="">Tutte</option>
                                        <option v-for="status in statuses" :value="status.params">{{ status.label }}</option>
                                    </select>
                                    <select v-if="column.key === 'hasAgencyPhoto'"
                                            :value="table.filters[column.key]?.value"
                                            v-model="photoFilter"
                                            @change="table.applyFilters({
                                                [column.key]: {
                                                    operation: 'EQ',
                                                    value: photoFilter,
                                                }
                                            })">
                                        <option value="">Tutte</option>
                                        <option v-for="status in photoStatuses" :value="status.value">{{ status.label }}</option>
                                    </select>
                                    <select v-if="column.key === 'detail'"
                                            :value="table.filters[column.key]?.value"
                                            v-model="detailFilter"
                                            @change="table.applyFilters({
                                                [column.key]: {
                                                    operation: 'EQ',
                                                    value: detailFilter,
                                                }
                                            })">
                                        <option value="">Tutte</option>
                                        <option v-for="status in detailStatuses" :value="status.value">{{ status.label }}</option>
                                    </select>
                                </div>
                            </th>
                            <th>Azioni</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="row in table.rows" :key="row.id">
                            <td v-for="column in columns" :key="column.key">
                                <div class="px-2">
                                    <div v-if="column.key === 'agents'">
                                        <span v-html="formatAgentsList(row[column.key])"></span>
                                    </div>
                                    <div class="flex justify-center" v-else-if="column.key === 'status'">
                                        <PhUserCheck class="text-success" v-if="row.approved && ! row.standard" :size="26" weight="bold" v-tooltip="'Approvata'" />
                                        <PhUserGear class="text-warning" v-if="!row.approved" :size="26" weight="bold" v-tooltip="'Da approvare'" />
                                        <PhUserMinus v-if="row.approved && row.standard" :size="26" weight="bold" v-tooltip="'Standard'" />
                                    </div>
                                    <div class="flex justify-center" v-else-if="column.key === 'hasAgencyPhoto'">
                                        <PhCheck class="text-success" v-if="row.hasAgencyPhoto === 'SI'" :size="22" weight="bold" v-tooltip="'Immagine Agenzia inserita'" />
                                        <PhX class="text-danger" v-if="row.hasAgencyPhoto === 'NO'" :size="22" weight="bold" v-tooltip="'Immagine Agenzia mancante'" />
                                    </div>
                                    <div class="flex justify-center" v-else-if="column.key === 'detail'">
                                        <PhCheck class="text-success" v-if="row.detail === 'FULL'" :size="22" weight="bold" v-tooltip="'Completo'" />
                                        <PhX class="text-danger" v-if="row.detail === 'EMPTY'" :size="22" weight="bold" v-tooltip="'Vuoto'" />
                                        <PhWrench v-if="row.detail === 'PARTIAL'" :size="22" weight="bold" v-tooltip="'Parziale'" />
                                    </div>
                                    <div v-else-if="column.key === 'privacy'">
                                        {{row.privacyAccepted}}/{{row.teamTotal}}
                                    </div>
                                    <div v-else>{{ row[column.key] }}</div>
                                </div>
                            </td>
                            <td>
                                <RouterLink class="btn secondary" :to="{ name: 'my-page-detail', params: { id: row.agenzia_id }}"><PhArrowRight size="22" />Visualizza</RouterLink>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="flex justify-center items-center gap-x-2 py-5">
                    <Pagination :table="table" />
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
thead tr th:nth-child(-n+2) {
    max-width: 120px;
}
</style>
