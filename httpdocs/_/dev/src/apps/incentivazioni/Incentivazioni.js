import router from "@/_common/router";
import Incentivazioni from "@/apps/incentivazioni/Incentivazioni.vue";
import RaccoltaVincente from "@/apps/incentivazioni/2025/raccolta-vincente-2025/RaccoltaVincente.js";
import Focus2025 from "@/apps/incentivazioni/2025/focus-2025/Focus2025.js";
import Compass2025 from "@/apps/incentivazioni/2025/compass-2025/Compass2025.js";
import NewProtection2025Routing from "@/apps/incentivazioni/2025/new-protection-2025/NewProtection2025Routing.js";
import GaraWelfare2025Routing from "@/apps/incentivazioni/2025/gara-welfare-2025/GaraWelfare2025Routing.js";

export default [
    {
        path: '/incentive',
        name: 'incentive',
        redirect: { name: 'dashboard' },
        meta: {
            breadcrumb: 'Incentivazioni'
        },
        children: [
            {
                path: ':year',
                name: 'incentive-year',
                component: Incentivazioni,
                props: (route) => ({ key: route.params.year }),
                beforeEnter: (to, _) => {
                    if (isNaN(to.params.year) || to.params.year < 1900 || to.params.year > 2200) {
                        router.push({ path: '/' });
                    }
                },
            },
            ...Focus2025,
            ...Compass2025,
            ...Focus2025,
            ...RaccoltaVincente,
            ...NewProtection2025Routing,
            ...GaraWelfare2025Routing
        ]
    }
];

