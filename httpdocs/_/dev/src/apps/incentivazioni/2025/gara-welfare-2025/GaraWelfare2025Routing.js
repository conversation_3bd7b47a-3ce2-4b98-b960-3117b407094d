import {useUserStore} from "@/_common/stores/user.js";
import FrontendGaraWelfare2025 from "@/apps/incentivazioni/2025/gara-welfare-2025/FrontendGaraWelfare2025.vue";

export default [
    {
        path: 'gara-welfare-2025',
        name: 'gara-welfare-2025',
        redirect: to => {
            const userStore = useUserStore();

            if (userStore.UTYPE === 'AGENTE') {
                return { name: 'gara-welfare-2025-frontend' };
            } else if (['AMMINISTRATORE', 'KA', 'AREAMGR', 'DISTRICTMGR'].includes(userStore.UTYPE)) {
                window.location.href = '../'
            } else {
                window.location.href = '../';
            }
        },
        children: [
            {
                path: 'frontend',
                name: 'gara-welfare-2025-frontend',
                component: FrontendGaraWelfare2025,
                beforeEnter: (to, from, next) => {
                    const userStore = useUserStore();

                    if (!['AGENTE'].includes(userStore.UTYPE)) {
                        window.location.href = '../'
                    } else {
                        next();
                    }
                },
            }
        ]
    }
];

