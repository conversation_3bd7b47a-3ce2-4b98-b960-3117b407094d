import axios from 'axios';

async function getStatus() {
    const res = (await axios.get(`/apps/gara-welfare-2025/ranking`))?.data
    if (!res.data) {
        return null
    }
    return res.data
}

async function getIncentiveStatus() {
    const res = (await axios.get(`/apps/gara-welfare-2025/last-update`))?.data
    if (!res.data) {
        return null
    }
    return res.data
}

export const garaWelfare2025Api = {
    getStatus,
    getIncentiveStatus
};
