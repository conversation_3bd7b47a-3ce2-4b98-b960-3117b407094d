<script setup>
import {ref, computed, onMounted} from "vue";
import {garaWelfare2025Api} from "@/apps/incentivazioni/2025/gara-welfare-2025/garaWelfare2025Api.js";
import {formatNumber, formatCurrency, formatDate} from "@/libs/formatter.js";
import {useUserStore} from "@/_common/stores/user.js";

const authData = useUserStore()
const incentiveStatus = ref(null)
const isLoading = ref(true)

const agencyStatus = ref({
    pezzi: 0,
    premiTot: 0
})

const ranking = ref([])

const slots = [
    {0: 1500, 1: 2500, 2: 4000},
    {0: 1000, 1: 1500, 2: 2500},
    {0: 750, 1: 1200, 2: 1500},
    {0: 500, 1: 800, 2: 1000},
    {0: 300, 1: 450, 2: 600},
]

async function refreshData() {
    isLoading.value = true
    try {
        const data = await garaWelfare2025Api.getStatus()
        agencyStatus.value = data['status']
        ranking.value = data['ranking']
        incentiveStatus.value = await garaWelfare2025Api.getIncentiveStatus()
    } finally {
        isLoading.value = false
    }
}

const showMessage = computed(() => {
    // Dati non ancora caricati
    if (!incentiveStatus.value || !agencyStatus.value) {
        return ""
    }

    // Incentivazione aperta - Dati in elaborazione
    if (incentiveStatus.value.status === 'processing') {
        return "/themes/incentivazioni/media/gara-welfare-2025/CASO_D.GIF"
    }
    // Incentivazione chiusa
    else if (incentiveStatus.value.status === 'closed' || incentiveStatus.value.status === 'liquidated') {
        return "/themes/incentivazioni/media/gara-welfare-2025/CASO_E.GIF"
    }
    // Incentivazione aperta - Obiettivi minimi raggiunti
    else if (incentiveStatus.value.status === 'active' && agencyStatus.value.premiTot >= 10000 && agencyStatus.value.pezzi >= 2) {
        // Fascia massima raggiunta
        if (agencyStatus.value.premiTot >= 25000) {
            return "/themes/incentivazioni/media/gara-welfare-2025/CASO_C.GIF"
        }
        return "/themes/incentivazioni/media/gara-welfare-2025/CASO_A.GIF"
    }
    // Incentivazione aperta - Obiettivi minimi non raggiunti
    return "/themes/incentivazioni/media/gara-welfare-2025/CASO_B.GIF"
})

// Load data when component is mounted
onMounted(async () => {
    await refreshData()
})

function checkQualyfing(row) {
    return row.premiTot >= 10000 && row.pezzi >= 2
}

</script>

<template>
    <div class="bg-[#143e8e] text-[#143e8e] full-height py-5" style="margin: -25px 0">

        <div class="container">

            <div class="flex justify-between items-center mb-3">
                <div>
                    <img class="w-[135px]" :src="`https://${window.staticHost}/themes/incentivazioni/media/gara-welfare-2025/logo.svg`" alt="Logo Gara Welfare 2025" />
                </div>
                <div class="relative hidden lg:block">
                    <div v-if="isLoading" class="flex items-center justify-center h-full">
                        <div class="flex flex-col items-center gap-4">
                            <div class="animate-spin rounded-full h-16 w-16 border-4 border-[#e2de00] border-t-transparent"></div>
                        </div>
                    </div>
                    <img v-else class="mx-auto" :src="`https://${window.staticHost}${showMessage}`" />
                </div>
                <div>
                    <a href="/api/apps/gara-welfare-2025/policies-detail" class="text-white">Dettaglio</a>
                    |
                    <a href="/api/apps/gara-welfare-2025/pdf" class="text-white">Regolamento</a>
                </div>
            </div>

            <div class="p-5 text-white">Dati aggiornati al: {{incentiveStatus?.lastUpdate ? formatDate(incentiveStatus.lastUpdate) : 'Caricamento...'}}</div>

            <div v-if="isLoading" class="flex items-center justify-center min-h-[450px]">
                <div class="flex flex-col items-center gap-4">
                    <div class="animate-spin rounded-full h-16 w-16 border-4 border-[#e2de00] border-t-transparent"></div>
                    <p class="text-white font-medium text-lg">Caricamento dati in corso...</p>
                </div>
            </div>

            <div v-else class="grid grid-cols-10 gap-5">
                <div class="col-span-10 lg:col-span-4">
                    <div class="grid grid-cols-2 gap-5">
                        <div class="col-span-2 sm:col-span-1 lg:col-span-2">
                            <div class="box">
                                <div class="box-title mb-5">Obiettivi minimi</div>
                                <div class="flex justify-between mb-2.5">
                                    <div class="text-lg font-bold uppercase">nuovi contratti</div>
                                    <div class="text-xl font-bold uppercase"><span class="font-black">{{agencyStatus.pezzi}}</span> / 2</div>
                                </div>
                                <div class="flex gap-5 mb-5">
                                    <div class="border-2 border-[#143e8e] w-full h-8" :class="{ 'bg-[#e2de00]': agencyStatus.pezzi >= 1 }"></div>
                                    <div class="border-2 border-[#143e8e] w-full h-8" :class="{ 'bg-[#e2de00]': agencyStatus.pezzi >= 2 }"></div>
                                </div>
                                <div class="mb-5 h-2 border-l-2 border-r-2 border-[#143e8e] flex items-center">
                                    <hr style="border: 0; border-bottom: 2px dashed #143e8e; margin: 0; width: 100%;">
                                </div>
                                <div class="flex justify-between mb-2.5">
                                    <div class="text-lg font-bold uppercase">Premi raccolti</div>
                                    <div class="text-xl font-bold uppercase"><span class="font-black">{{formatNumber(agencyStatus.premiTot)}}</span> / 10.000 €</div>
                                </div>
                                <div class="progressbar-container relative">
                                    <!-- Notches Top -->
                                    <div class="absolute left-0 top-0 w-full flex justify-evenly pointer-events-none">
                                        <template v-for="i in 9" :key="'top-notch-' + i">
                                            <div class="w-[2px] h-[10px] bg-[#143e8e]"></div>
                                        </template>
                                    </div>
                                    <!-- Notches Bottom -->
                                    <div class="absolute left-0 bottom-0 w-full flex justify-evenly pointer-events-none">
                                        <template v-for="i in 9" :key="'bottom-notch-' + i">
                                            <div class="w-[2px] h-[10px] bg-[#143e8e]"></div>
                                        </template>
                                    </div>
                                    <div
                                        class="bg-[#e2de00] h-full"
                                        :style="{ width: `${Math.min((agencyStatus.premiTot / 10000) * 100, 100)}%` }"
                                    ></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-span-2 sm:col-span-1 lg:col-span-2">
                            <div class="box">
                                <div class="box-title mb-2">Fascia premi</div>
                                <div class="overflow-x-auto">
                                    <table class="slots-table">
                                        <thead>
                                        <tr>
                                            <th></th>
                                            <th>< 15.000 €</th>
                                            <th class="custom-border">15-25.000 €</th>
                                            <th>> 25.000 €</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(row, index) in slots" :key="index">
                                                <td>{{index+1}}</td>
                                                <td class="text-right" :class="{'highlight-even': index % 2 === 0 && agencyStatus.premiTot < 15000, 'highlight-odd': index % 2 === 1 && agencyStatus.premiTot < 15000}">{{formatCurrency(row[0], false)}}</td>
                                                <td class="text-right custom-border" :class="{'highlight-even': index % 2 === 0 && (agencyStatus.premiTot >= 15000 && agencyStatus.premiTot < 25000 ), 'highlight-odd': index % 2 === 1 && (agencyStatus.premiTot >= 15000 && agencyStatus.premiTot < 25000 )}">{{formatCurrency(row[1], false)}}</td>
                                                <td class="text-right" :class="{'highlight-even': index % 2 === 0 && agencyStatus.premiTot >= 25000, 'highlight-odd': index % 2 === 1 && agencyStatus.premiTot >= 25000}">{{formatCurrency(row[2], false)}}</td>
                                            </tr>
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-span-10 lg:col-span-6">
                    <div class="box h-full">
                        <div class="box-title mb-2">Classifica provvisoria</div>
                        <div class="overflow-x-auto">
                            <table class="ranking-table">
                                <thead>
                                <tr>
                                    <th>Pos.</th>
                                    <th>Agenzia</th>
                                    <th>Premi</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr v-for="(row, index) in ranking" :key="index" :class="{'highlight': row.agenzia_id === authData.AGENZIA, 'first-5' : index < 5 && checkQualyfing(row) }">
                                    <td>
                                        <template v-if="row.premiTot >= 10000 && row.pezzi >= 2">{{index+1}}°</template>
                                        <template v-else>-</template>
                                    </td>
                                    <td>{{row.agenzia_id}} - {{row.localita}}</td>
                                    <td>{{formatCurrency(row.premiTot)}}</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>
</template>

<style scoped>
.full-height {
    min-height: 100vh; /* This will make it at least as tall as the viewport */
    height: 100%; /* This will make it fill its parent container */
    flex-grow: 1; /* This will make it expand to fill available space if in a flex container */
}

.box {
    @apply bg-white rounded-xl p-5;
}

.box-title {
    letter-spacing: 0.5em;
    @apply font-bold text-xl uppercase text-center text-[#e2de00] bg-[#143e8e] py-2.5;
}

.progressbar-container {
    @apply h-14 overflow-hidden border-2 border-[#143e8e];
}

.slots-table th {
    @apply bg-[#143e8e] text-[#e2de00] text-right font-bold p-3;
}

.slots-table tr:nth-child(odd) {
    @apply bg-[#f6f5b8];
}

.slots-table td {
    @apply p-3 font-bold border-0;
}

.highlight-odd {
    @apply bg-[#f3f197];
}

.highlight-even {
    @apply bg-[#edeb6c];
}

.custom-border {
    @apply border-l-2 border-r-2 border-white;
}

.ranking-table tr.highlight > td:first-child {
    border-left: 4px solid #143e8e; /* Blue left border */
    border-top: 4px solid #143e8e;  /* Blue top border */
    border-bottom: 4px solid #143e8e; /* Blue bottom border */
}

.ranking-table tr.highlight > td:last-child {
    border-right: 4px solid #143e8e; /* Blue right border */
    border-top: 4px solid #143e8e;
    border-bottom: 4px solid #143e8e;
}

.ranking-table tr.highlight > td:not(:first-child):not(:last-child) {
    border-top: 4px solid #143e8e;
    border-bottom: 4px solid #143e8e;
}

/* Optional: Remove double borders between highlighted and non-highlighted rows */
.ranking-table td {
    border: none;
    border-bottom: 2px solid #143e8e;
    @apply font-bold;
}

.first-5 {
    @apply !bg-[#e2de00];
}

.ranking-table tr:nth-child(odd) {
    @apply bg-[#f6f5b8];
}
</style>