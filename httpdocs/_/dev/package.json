{"name": "agendo3", "version": "0.15.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "test": "vitest --ui", "ttest": "vitest"}, "dependencies": {"@chenfengyuan/vue-qrcode": "^2.0.0", "@headlessui/tailwindcss": "^0.2.1", "@headlessui/vue": "^1.7.22", "@heroicons/vue": "^2.1.3", "@phosphor-icons/vue": "^2.2.1", "@vee-validate/i18n": "^4.14.4", "@vee-validate/rules": "^4.14.4", "@vueup/vue-quill": "^1.2.0", "axios": "^1.6.8", "chart.js": "^4.4.8", "floating-vue": "^5.2.2", "lodash": "^4.17.21", "pinia": "^2.1.7", "playwright": "^1.44.0", "primevue": "^4.2.5", "qrcode": "^1.5.4", "quill": "^2.0.3", "rollup-plugin-copy": "^3.5.0", "tailwindcss-primeui": "^0.5.1", "vee-validate": "^4.12.8", "vue": "^3.5.13", "vue-router": "^4.3.0", "vue-upload-component": "^3.1.15", "vue3-toastify": "^0.2.1", "yup": "^1.4.0"}, "devDependencies": {"@playwright/test": "^1.43.1", "@types/node": "^20.12.7", "@vitejs/plugin-vue": "^5.0.4", "@vitest/ui": "^1.5.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "vite": "^5.1.6", "vite-plugin-vue-devtools": "^7.0.18", "vitest": "^1.5.0"}, "jshintConfig": {"esversion": 9}}