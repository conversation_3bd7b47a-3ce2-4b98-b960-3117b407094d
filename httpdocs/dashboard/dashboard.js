(function(ng, module) {

	module.config(['$stateProvider', 'xng.$dataProvider', function($stateProvider, $dataProvider) {
		$stateProvider
			.state('dashboard',		{ url: '/', templateUrl: 'dashboard/dashboard.html',	controller: 'app.dashboard.Ctrl',		navbar: 'dashboard' })
		;

	}]);


	// initialization
	// ------------------------------------------

	var UI_ADMIN, UI_BANNERS, UI_NEWS_FILTER, UI_POPUP_CONTACT, UI_NEO, UI_RETICOMP, UI_RETICOMP_AGT, UI_FAM_COMM, UI_IMPRENDO, UI_MYPAGE, UI_GOTIT, UI_WELFARE_AZIENDALE, UI_SIMVITA;
	module.run(['$rootScope', function($rootScope) {
		$rootScope.$on('xng.auth:init', function(ev, authData) {
			if(!authData) return;
			UI_ADMIN = null;
			UI_BANNERS = null;
			if(/^(KA|AMMINISTRATORE|AREAMGR|DISTRICTMGR|FOR|ASV)$/.test(authData.UTYPE)) {
				UI_BANNERS = 'DIREZIONE';
				UI_NEWS_FILTER = 'showDIREZ,EQ,1';
			}
			if(/^AGENTE$/.test(authData.UTYPE)) {
				UI_BANNERS = 'AGENTE';
				UI_NEWS_FILTER = 'showAGENTI,EQ,1';
				UI_POPUP_CONTACT = true;
			}
			if(/^INTERMEDIARIO$/.test(authData.UTYPE)) {
				UI_BANNERS = 'INTERMEDIARIO';
				UI_NEWS_FILTER = 'showINTERMED,EQ,1';
				UI_NEO = authData.UROLE == 'NEO';
			}
			if(authData.ACL.admin) UI_ADMIN = authData.ACL.admin;
			if(authData.ACL.reticomp) UI_RETICOMP = true;
			if(authData.ACL.reticomp_agt) UI_RETICOMP_AGT = true;
			if(authData.ACL.fam_comm) UI_FAM_COMM = true;
			if(authData.ACL.imprendo) UI_IMPRENDO = true;
			if(authData.ACL.gotit) UI_GOTIT = true;
			if(authData.ACL.mypage) UI_MYPAGE = true;
			if(authData.ACL.wel_azi) UI_WELFARE_AZIENDALE = true;
			if(authData.ACL.simvita) UI_SIMVITA = true;
		});
	}]);


	// controllers
	// ------------------------------------------

	module.controller('app.dashboard.Ctrl', ['$scope', '$http', 'xng.$data', 'xng.ui.$growl', 'xng.ui.$modal', '$window', '$sce', '$dashboardLocalStorage', function($scope, $http, $data, $growl, $modal, $window, $sce, $dashboardLocalStorage) {
		var NewsRepository = $data.repository('News');
		$scope.today = new Date().toJSON().slice(0,10);
		$scope.UI_ADMIN = UI_ADMIN;
		$scope.UI_BANNERS = UI_BANNERS;
		$scope.UI_NEO = UI_NEO;
		$scope.UI_RETICOMP = UI_RETICOMP;
		$scope.UI_RETICOMP_AGT = UI_RETICOMP_AGT;
		$scope.UI_FAM_COMM = UI_FAM_COMM;
		$scope.UI_IMPRENDO = UI_IMPRENDO;
		$scope.UI_GOTIT = UI_GOTIT;
		$scope.UI_MYPAGE = UI_MYPAGE;
		$scope.UI_WELFARE_AZIENDALE = UI_WELFARE_AZIENDALE;
		$scope.UI_SIMVITA = UI_SIMVITA;
		$scope.grid = null;
		$scope.user = $scope.$authData;

		$scope.updateDashboardCache = function () {
			$dashboardLocalStorage.setObject('dashboard', $scope.dashboardCache)
		}
		$scope.dashboardCache = $dashboardLocalStorage.getObject('dashboard')
		if ( ! $scope.dashboardCache || ! $scope.dashboardCache?.automaticModal ) {
			$dashboardLocalStorage.setObject('dashboard', {automaticModal: false})
		}

		NewsRepository.fetchAll(1, 3, 'date.DESC', UI_NEWS_FILTER).then(function(data){
			$scope.newsArray = data;
		});
		// Incentivazioni
		$scope.incentivazioni = [];
		$http({ method: 'GET', url: '/api/incentivazioni/dashboard' }).then(function (response) {
			$scope.incentivazioni = response.data.data;
		});

		// POPUP BIG JUMP
		$scope.popupOpen = function() {
			$modal.open({ scope: $scope, templateUrl: '/incentivazioni/bigjump-modal.html' });
		};

		// Check Agenti MyPage
		if ($scope.$authData.UTYPE === 'AGENTE') {
			$http({ method: 'GET', url: '/api/apps/cms-agenzie/access' }).then(function (response) {
				$scope.mypageAgente = response.data.access;
			});
		}

		// Check Agenti MyPage
		if ($scope.$authData.UTYPE === 'INTERMEDIARIO') {
			$http({ method: 'GET', url: '/api/apps/uniqum/check-enrolled' }).then(function (response) {
				$scope.intermediarioUniqum = response.data.enrolled;
			});
		}


		// POPUP cancellazione dati Contact
		if(UI_POPUP_CONTACT) $http({ method: 'GET', url: '/api/apps/contact/stop' })
			.then(function (response) {
				if(response.data.data != null) return;
				var popupContactScope = $scope.$new();
				var popupContact = $modal.open({ scope: popupContactScope, templateUrl: '/dashboard/contact-modal.html' });
				UI_POPUP_CONTACT = false;
				popupContactScope.submit = function() {
					$http({ method: 'POST', url: '/api/apps/contact/stop' }).then(
						function() {
							$growl.success('Conferma registrata', { icon: 'icon-checkmark', ttl: 4000 });
							popupContact.close();
						}, function () {
							$growl.error('Errore imprevisto', { icon: 'icon-warning', ttl: 3000 });
						}
					);
				};
			});

		$scope.privacyModal = null;

		if ( $scope.user.UTYPE === 'INTERMEDIARIO') {
			// Check privacy sito Groupama
			$http({ method: 'GET', url: '/api/apps/cms-agenzie/privacy-request/status' }).then(function (response) {
				if (! response.data.request) {
					return;
				}
				$scope.privacyRequestWaiting = true;
				$scope.privacyModal = $modal.open({ scope: $scope, templateUrl: '/dashboard/privacy-modal.html' });
			});
		}

		$scope.getIframeSrc = function () {
			return $sce.trustAsResourceUrl('//' + staticHost + '/assets/dashboard/Groupama_Uniqum_1080.mp4');
		};

		$scope.changeStep = function (step, event) {
			event.stopPropagation();
			$scope.step = step;
		};

		$scope.checkUserCredits = function () {
			$http({ method: 'GET', url: '/api/apps/formazione/dashboard/credits-summary' }).then(function (response) {

				if (! response.data.success) {
					return;
				}

				var credits = response.data.data.credits;
				$scope.missingCredits = (30 - credits).toFixed(2);
				$scope.step = 0;

				if ($scope.missingCredits > 0) {
					$modal.open({ scope: $scope, templateUrl: '/dashboard/30-ore-modal.html' });
				}

			});
		};

		// Disabilito modal per esortare al completamento della formazione annuale
		if ( $scope.user.UTYPE === 'AGENTE' ) {
			//$scope.checkUserCredits();
		}

		function addMinutesToTimestamp(minutes) {
			var date = new Date();
			// Set expire time by adding n minutes to current time
			date.setMinutes( date.getMinutes() + minutes );
			return date;
		}

		// Metodo necessario per sovrascrivere la cache di sessione di formazione
		$scope.goToFormazione = function (page) {

			// Oggetto che raccoglie stato e url delle 2 rotte
			var states = {
				'est' : ['external-credits.external-courses', 'corsi-frequentati'],
				'aut' : ['external-credits.self-certifications', 'autocertificazioni'],
				'das' : ['dashboard', null]
			};

			// Oggetto che raccoglie i dati di sessione
			var obj = {
				type: $scope.user.UTYPE,
				userId: $scope.user.UID,
				state: 'root.frontend.' + states[page][0],
				expireTime: JSON.stringify(addMinutesToTimestamp(5).toString()),
				stateParams: null
			};
			// Sovrascrive la sessione registrata nel local storage
			$window.localStorage['formazioneCachedSession'] = JSON.stringify(obj);
			// Link diretto
			if (states[page][1]) {
				$window.location.href = '/formazione/#!/frontend/ore-formative-esterne/' + states[page][1];
				return;
			}
			$window.location.href = '/formazione/#!/frontend/dashboard';

		};

		$scope.showFormazionePath = function () {
			return new Date('1/1/2025') >= Date.now();
		};

		//$scope.simvitaModal = null;
		$scope.uniqumModal = null;
		$scope.setUniqumVideoAsWatched = function() {
			$scope.dashboardCache.automaticModal = true;
			$scope.updateDashboardCache();
		}

		if ( ['AGENTE'].includes($scope.UI_BANNERS) && ! $scope.dashboardCache.automaticModal ) {
			$scope.uniqumModal = $modal.open({ scope: $scope, templateUrl: '/dashboard/uniqum-approfondimenti-modal.html' });
		}

		$scope.closeUniqumModal = function () {
			$scope.setUniqumVideoAsWatched();
			$scope.uniqumModal.close();
		}

		/*if ( $scope.user.UTYPE === 'AGENTE' ) {
			$http({
					method: 'GET',
					url: '/api/apps/formazione/survey/check',
				}).then(function (response) {

					$scope.completedSurvey = response.data.completion;
					/!*if ( !response.data.completion && $scope.today < $scope.modalDay ) {
						$modal.open({ scope: $scope, templateUrl: '/dashboard/survey-modal.html', backdrop: 'static' });
					}*!/

			});
		}

		$scope.goToFormazioneSurvey = function () {

			// Oggetto che raccoglie i dati di sessione
			var obj = {
				type: $scope.user.UTYPE,
				userId: $scope.user.UID,
				state: 'root.frontend.survey',
				expireTime: JSON.stringify(addMinutesToTimestamp(5).toString()),
				stateParams: null
			};
			// Sovrascrive la sessione registrata nel local storage
			$window.localStorage['formazioneCachedSession'] = JSON.stringify(obj);
			$window.location.href = '/formazione/#!/frontend/offerta-formativa';

		};*/

		$scope.isPlayerShown = [];
		$scope.onAirPlaylist = [
			{
				id: 4,
				title: 'Episodio 4',
				length: '16:24',
				isPlaying: true,
			},
			{
				id: 3,
				title: 'Episodio 3',
				length: '20:59',
			},
			{
				id: 2,
				title: 'Episodio 2',
				length: '14:54',
			},
			{
				id: 1,
				title: 'Episodio 1',
				length: '18:27'
			}
		]
		let podcastFiles = {
			ep1: {
				img: 'bgr-onair-ep1.jpg',
				audio: 'Groupama ONAIR.mp3'
			},
			ep2: {
				img: 'bgr-onair-ep2.jpg',
				audio: 'Groupama ONAIR-2.mp3',
			},
			ep3: {
				img: 'bgr-onair-ep3.jpg',
				audio: 'Groupama ONAIR-3.mp3',
			},
			ep4: {
				img: 'bgr-onair-ep4.jpg',
				audio: 'Groupama ONAIR-4.mp3',
			},
			news: {
				img: 'bgr-podcast-vita.jpg',
				audio: 'news-vita-e-investimenti.mp3',
			}
		}
		$scope.showAudioPlayer = function (index, episode = null) {

			let element = document.querySelector('#draggable-player-' + index);
			element.onmousedown = event => {
				position.x = event.clientX
				position.y = event.clientY

				document.onmousemove = documentEvent => {
					const x = position.x - documentEvent.clientX
					const y = position.y - documentEvent.clientY

					position.x = documentEvent.clientX
					position.y = documentEvent.clientY

					element.style.top = element.offsetTop - y + 'px'
					element.style.left = element.offsetLeft - x + 'px'

				}

				document.onmouseup = () => {
					document.onmouseup = null
					document.onmousemove = null
				}
			}

			let l2 = index === 1 ? 'banner-onair' : 'banner-news';
			$http({
				method: 'POST',
				data: {
					l1: 'podcast',
					l2: l2,
					l3: l2 === 'banner-onair' ? `ep${Number(episode) + 1}` : '',
				},
				url: '/api/stats',
			});
			$scope.isPlayerShown[index] = true;
			element.style.top = '120px';
			element.style.left = '10px';
		};
		$scope.hideAudioPlayer = function (index) {
			$scope.isPlayerShown[index] = false;
			document.getElementById("audio-" + index).pause();
		};

		const position = {
			x: 0,
			y: 0
		}

		$scope.getPodcastAudio = function (episode) {
			return $sce.trustAsResourceUrl('//' + staticHost + '/assets/dashboard/' + podcastFiles[episode].audio);
		};

		$scope.getPodcastCover = function (episode) {
			return $sce.trustAsResourceUrl('//' + staticHost + '/assets/dashboard/' + podcastFiles[episode].img);
		};

		$scope.changeEpisode = function (episode, index = null) {
			if (index !== null) {
				$scope.onAirPlaylist.forEach((item, i) => {
					$scope.onAirPlaylist[i].isPlaying = false;
				})
				$scope.onAirPlaylist[index].isPlaying = true;
			}
			let sourceEl = document.getElementById("src-1");
			let coverEl = document.getElementById("img-1");
			sourceEl.src = $scope.getPodcastAudio(episode);
			coverEl.src = $scope.getPodcastCover(episode);
			document.getElementById("audio-1").load()
			$http({
				method: 'POST',
				data: {
					l1: 'podcast',
					l2: 'banner-onair',
					l3: episode,
				},
				url: '/api/stats',
			});
		}

		$scope.processExtUrl = function (url, event) {
			$http({
				method: 'POST',
				data: {
					l1: event,
					l2: '',
					l3: '',
				},
				url: '/api/stats',
			});
			window.open(url, '_blank');
		}

	}]);

	// Directives
	// ------------------------------------------

	module.directive('gridCell', function(){
		return {
			template: function() {
				return "<div ng-include='customTemplate()'></div>";
			},
			link: function(scope, element, attrs) {
				scope.customTemplate = function() {
					if (scope.cell.dataType == 'IMG') {
						return 'dashboard/cell-templates/img.html';
					}
				}
			},
			scope: {
				cell: '=cell',
				staticHost: '@host'
			}
		}
	});

	// Filters
	// ------------------------------------------

	module.filter('propsFilter', function() {
		return function(items, props) {
			var out = [];
			if (angular.isArray(items)) {
				items.forEach(function(item) {
					var itemMatches = false;
					var keys = Object.keys(props);
					for (var i = 0; i < keys.length; i++) {
						var prop = keys[i];
						var text = props[prop].toLowerCase();
						if (item[prop].toString().toLowerCase().indexOf(text) !== -1) {
							itemMatches = true;
							break;
						}
					}
					if (itemMatches) {
						out.push(item);
					}
				});
			} else {
				// Let the output be the input untouched
				out = items;
			}
			return out;
		};
	});

})(angular, angular.module('app.dashboard', []));
