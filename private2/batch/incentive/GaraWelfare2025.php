<?php

namespace batch\incentive;

use batch\Incentive;
use metadigit\core\cli\Request,
    metadigit\core\cli\Response;

class GaraWelfare2025 extends Incentive
{

    /**
     * @batch(description="Incentive: Gara Welfare 2025 import")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function dataAction(Request $Req, Response $Res)
    {

        $this->pdoStExecute("delete from inc_gara_welfare_2025_data");
        $selectedRows = 0;
        $rowsRegistered = 0;
        $rowsError = 0;
        $errors = [];

        $statement = $this->pdoPrepare("INSERT INTO inc_gara_welfare_2025_data (
            agenzia_id,
            contraente,
            polizza,
            polizzaMadre,
            codiceProdotto,
            premio
        ) VALUES (
            :agenzia_id,
            :contraente,
            :polizza,
            :polizzaMadre,
            :codiceProdotto,
            :premio
        )");

        // Query con i requisiti dell'incentivazione
        // Viene usato REGEXP invece di LIKE per assicurarmi che la parola cercata sia 'intera'
        $query = "
            SELECT
                agenzia_id,
                contraente,
                polizza,
                polizzaMadre,
                codiceProdotto,
                premioNetto
            FROM pc_data
            WHERE codiceProdotto IN('000520')
            AND dataContabileEmissione BETWEEN '2025-07-01' AND '2025-12-31'
            AND (
                contraente REGEXP '(^|[^a-zA-Z0-9])CASSA([^a-zA-Z0-9]|$)'
                   OR contraente REGEXP '(^|[^a-zA-Z0-9])CASSE([^a-zA-Z0-9]|$)'
                   OR contraente REGEXP '(^|[^a-zA-Z0-9])FONDO([^a-zA-Z0-9]|$)'
                   OR contraente REGEXP '(^|[^a-zA-Z0-9])FONDI([^a-zA-Z0-9]|$)'
                   OR contraente REGEXP '(^|[^a-zA-Z0-9])MUTUO([^a-zA-Z0-9]|$)'
                   OR contraente REGEXP '(^|[^a-zA-Z0-9])MUTUA([^a-zA-Z0-9]|$)'
            )
        ";

        $selectedRows = $this->pdoQuery($query)->rowCount();

        $data = $this->pdoQuery($query)->fetchAll(\PDO::FETCH_ASSOC);

        // Log information about the policies being processed
        /*$polizzeList = array_column($data, 'polizza');
        $this->log("Polizze elaborate: " . count($polizzeList));
        if (count($polizzeList) > 0) {
            $this->log("Lista polizze elaborate: " . implode(', ', $polizzeList));
        }*/

        foreach ($data as $row) {
            try {
                $statement->execute(
                    [
                        'agenzia_id' => $row['agenzia_id'],
                        'contraente' => $row['contraente'],
                        'polizza' => $row['polizza'],
                        'polizzaMadre' => $row['polizzaMadre'],
                        'codiceProdotto' => $row['codiceProdotto'],
                        'premio' => $row['premioNetto'],
                    ]
                );
                $rowsRegistered++;
            } catch (\Exception $ex) {
                $rowsError++;
                $this->trace(LOG_ERR, TRACE_ERROR, __FUNCTION__, "ERROR", print_r($row, true));
                $errors[] = [
                    'error' => $ex->getMessage(),
                    'row' => $row
                ];
            }
        }

        $this->log("Righe totali: $selectedRows");
        $this->log("Righe registrate: $rowsRegistered");
        $this->log("Righe con errori: $rowsError");
        if ($errors) {
            $this->log("Errori:");
            foreach ($errors as $index => $error) {
                $this->log("Error " . ($index + 1) . ": " . $error['error']);
                $this->log("Row data: " . print_r($error['row'], true));
                $this->log("-------------------");
            }
        }
        $this->log("================");

        $data = $this->pdoQuery('SELECT * FROM inc_gara_welfare_2025_data')->fetchAll(\PDO::FETCH_ASSOC);

        $deletedPolicies = [];
        foreach ($data as $row) {
            if ( ! $this->isNewProduction($row) ) {
                // Track the polizza before deletion
                $deletedPolicies[] = $row['polizza'];

                $this->pdoStExecute("DELETE FROM inc_gara_welfare_2025_data WHERE agenzia_id = :agenzia_id AND polizzaMadre = :polizzaMadre", [
                    ':agenzia_id' => $row['agenzia_id'],
                    ':polizzaMadre' => $row['polizzaMadre'],
                ]);
            }
        }

        // Log deleted policies information
        $deletedCount = count($deletedPolicies);
        $this->log("Polizze eliminate: $deletedCount");
        if ($deletedCount > 0) {
            $this->log("Lista polizze eliminate: " . implode(', ', $deletedPolicies));
        }

        $parsed = new \stdClass();
        $parsed->id = 339;
        $parsed->date = date('Y-m-d');
        $this->incentive->postInsert($parsed);

        // Timestamps.
        /*$this->pdoStExecute(
            "update incentive set lastUpdate = :lastUpdate, lastUpdateLabel = :lastUpdateLabel where id = :id",
            [
                ':lastUpdate' => date("Y-m-d H:i:s"),
                ':lastUpdateLabel' => $this->parsed->date,
                ':id' => $this->parsed->id,
            ]
        );*/

    }

    /**
     * @batch(description="Incentive: Gara Welfare 2025 static")
     * @param \metadigit\core\cli\Request $Req
     * @param \metadigit\core\cli\Response $Res
     * @throws \Exception
     */
    public function staticAction(Request $Req, Response $Res)
    {
        parent::staticAction($Req, $Res);
    }

    protected function isNewProduction($policy)
    {
        // Fetch all policies with the same 'polizzaMadre'
        $query = "SELECT polizza FROM inc_gara_welfare_2025_data WHERE polizzaMadre = :polizzaMadre";
        $statement = $this->pdoPrepare($query);
        $statement->execute([':polizzaMadre' => $policy['polizzaMadre']]);
        $policies = $statement->fetchAll(\PDO::FETCH_ASSOC);

        // Check if any of these policies has '1C' at the end of 'polizza'
        foreach ($policies as $relatedPolicy) {
            if (substr($relatedPolicy['polizza'], -2) === '1C') {
                return true;
            }
        }

        return false;
    }

}
