<?xml version="1.0" encoding="UTF-8"?>
<context namespace="batch">
	<includes>
		<include namespace="data"/>
		<include namespace="data.geo"/>
		<include namespace="data.apps.reportistica"/>
		<include namespace="data.apps.ivass"/>
		<include namespace="data.apps.accordo2"/>
		<include namespace="data.apps.cmsAgenzie"/>
		<include namespace="data.incentivazioni"/>
		<include namespace="api.apps.incentive.rinn2023"/>
		<include namespace="api.apps.incentive.supernova"/>
		<include namespace="api.apps.incentive.supernova2024"/>
		<include namespace="api.apps.incentive.supernovaRF2024"/>
		<include namespace="api.apps.incentive.focus2023"/>
		<include namespace="api.apps.incentive.focus2024"/>
		<include namespace="api.apps.incentive.focus2025"/>
		<include namespace="api.apps.incentive.focusFase22023"/>
		<include namespace="api.apps.incentive.focusFase22024"/>
		<include namespace="api.apps.incentive.fullprotection2023"/>
		<include namespace="api.apps.incentive.fullprotection2024"/>
		<include namespace="api.apps.incentive.fullbenessere2024"/>
		<include namespace="api.apps.incentive.arag2024"/>
		<include namespace="api.apps.incentive.compass2025"/>
		<include namespace="api.apps.incentive.raccoltaVincente2025"/>
		<include namespace="api.apps.incentive.newProtection2025"/>
		<include namespace="api.apps.incentive.garaWelfare2025"/>
		<include namespace="data.apps.eventmanager"/>
		<include namespace="batch.utils.anagrafica"/>
		<include namespace="service"/>
		<include namespace="system"/>
	</includes>
	<objects>
		<!-- Dispatcher -->
		<object id="batch.Dispatcher" class="metadigit\core\console\Dispatcher">
			<properties>
				<property name="mappings" type="array">
					<item key="abi">batch.Abi</item>
					<item key="accordo">batch.AccordoEconomico</item>
                    <item key="anagraficaCheck">batch.AnagraficaCheck</item>
                    <item key="anagrafica">batch.Anagrafica</item>
                    <item key="arag2024">batch.Arag2024</item>
					<item key="avanzamenti">batch.Avanzamenti</item>
					<item key="cmsAgenzie">batch.CmsAgenzie</item>
					<item key="compass2025">batch.Compass2025</item>
					<item key="contact">batch.ContAct</item>
					<item key="elearning">batch.ELearningFetchRemote</item>
					<item key="weekElearning">batch.WeekELearningFetchRemote</item>
					<item key="eventmanager">batch.EventManager</item>
					<item key="fix">batch.Fix</item>
					<item key="formazioneUpdate">batch.FormazioneUpdate</item>
					<item key="fullprotection2023">batch.Fullprotection2023</item>
					<item key="fullprotection2024">batch.Fullprotection2024</item>
					<item key="fullbenessere2024">batch.Fullbenessere2024</item>
					<item key="statusMail">batch.FormazioneStatusMail</item>
					<item key="gare">batch.Gare</item>
					<item key="geo">batch.GeoData</item>
					<item key="incentivazioni">batch.Incentivazioni</item>
					<item key="lifePolicies">batch.LifePolicies</item>
					<item key="lifeClients">batch.LifeClients</item>
					<item key="PCPolicies">batch.PCPolicies</item>
					<item key="ivass">batch.Ivass</item>
					<item key="neo">batch.Neo</item>
					<item key="notify">batch.PDNotificator</item>
					<item key="pianiagenzia2">batch.PianiAgenzia2</item>
					<item key="reportistica">batch.Reportistica</item>
					<item key="rinn2023">batch.Rinn2023</item>
					<item key="focus2023">batch.Focus2023</item>
					<item key="focus2024">batch.Focus2024</item>
					<item key="focus2025">batch.Focus2025</item>
					<item key="focusFase22023">batch.FocusFase22023</item>
					<item key="focusFase22024">batch.FocusFase22024</item>
					<item key="raccoltaVincente2025">batch.RaccoltaVincente2025</item>
					<item key="newProtection2025">batch.NewProtection2025</item>
					<item key="garaWelfare2025">batch.GaraWelfare2025</item>
					<item key="santino">batch.Santino</item>
					<item key="stats">batch.Stats</item>
					<item key="supernova2023">batch.Supernova2023</item>
					<item key="supernova2024">batch.Supernova2024</item>
					<item key="supernovaRF2024">batch.SupernovaRF2024</item>
					<item key="tassozero">batch.TassoZero</item>
					<item key="welcomeback">batch.WelcomeBack</item>
					<item key="webinar">batch.Webinar</item>
				</property>
			</properties>
		</object>
		<!-- controllers -->
		<object id="batch.Anagrafica" class="batch\Anagrafica">
			<properties>
				<property name="Abi" type="object">service.Abi</property>
				<property name="UsersAuthRepository" type="object">data.UsersAuthRepository</property>
				<property name="matrnx" type="object">batch.utils.anagrafica.Matrnx</property>
				<property name="nxca" type="object">batch.utils.anagrafica.Nxca</property>
				<property name="analyzer" type="object">batch.utils.anagrafica.Analyzer</property>
			</properties>
		</object>
		<object id="batch.ContAct" class="batch\ContAct"/>
		<object id="batch.Fix" class="batch\Fix"/>
		<object id="batch.Gare" class="batch\Gare"/>
		<object id="batch.GeoData" class="batch\GeoData"/>
		<object id="batch.Incentivazioni" class="batch\Incentivazioni">
			<properties>
				<property name="GroupamaPDNotificator" type="object">service.GroupamaPDNotificator</property>
				<property name="IncentivazioniRepository" type="object">data.incentivazioni.IncentivazioniRepository</property>
			</properties>
		</object>

		<object id="batch.LifePolicies" class="batch\LifePolicies">
			<properties>
				<property name="utils" type="object">service.GroupamaUtils</property>
			</properties>
		</object>

		<object id="batch.LifeClients" class="batch\LifeClients">
			<properties>
				<property name="utils" type="object">service.GroupamaUtils</property>
			</properties>
		</object>

		<object id="batch.PCPolicies" class="batch\PCPolicies">
			<properties>
				<property name="utils" type="object">service.GroupamaUtils</property>
			</properties>
		</object>

		<object id="batch.Reportistica" class="batch\Reportistica">
			<properties>
				<property name="AreeRepository" type="object">data.geo.AreeRepository</property>
				<property name="BatchesRepository" type="object">data.apps.reportistica.BatchesRepository</property>
				<property name="DatiCommercialiRepository" type="object">data.apps.reportistica.DatiCommercialiRepository</property>
				<property name="DatiIniziativeRepository" type="object">data.apps.reportistica.DatiIniziativeRepository</property>
				<property name="DistrictsRepository" type="object">data.geo.DistrictsRepository</property>
				<property name="GareAreaRepository" type="object">data.apps.reportistica.GareAreaRepository</property>
				<property name="Mailer" type="object">system.Mailer</property>
				<property name="UsersRepository" type="object">data.UsersRepository</property>
			</properties>
		</object>
		<object id="batch.Santino" class="batch\Santino"/>
		<object id="batch.TassoZero" class="batch\TassoZero"/>
		<object id="batch.WelcomeBack" class="batch\WelcomeBack"/>
		<object id="batch.Webinar" class="batch\Webinar" />
		<object id="batch.PianiAgenzia2" class="batch\PianiAgenzia2">
			<properties>
				<property name="AreeRepository" type="object">data.geo.AreeRepository</property>
				<property name="DatiCommercialiRepository" type="object">data.apps.reportistica.DatiCommercialiRepository</property>
			</properties>
		</object>
		<object id="batch.Avanzamenti" class="batch\Avanzamenti">
            <properties>
                <property name="GroupamaPDNotificator" type="object">service.GroupamaPDNotificator</property>
            </properties>
        </object>
		<object id="batch.Ivass" class="batch\Ivass">
			<properties>
				<property name="users" type="object">data.UsersRepository</property>
				<property name="table5" type="object">data.apps.ivass.Table5Repository</property>
				<property name="table7" type="object">data.apps.ivass.Table7Repository</property>
			</properties>
		</object>
		<object id="batch.Abi" class="batch\Abi">
			<properties>
				<property name="AbiService" type="object">service.AbiService</property>
				<property name="UsersRepository" type="object">data.UsersRepository</property>
			</properties>
		</object>
		<object id="batch.Neo" class="batch\Neo"></object>
		<object id="batch.PDNotificator" class="batch\PDNotificator">
			<properties>
				<property name="GroupamaPDNotificator" type="object">service.GroupamaPDNotificator</property>
			</properties>
		</object>
		<!--<object id="batch.Formazione" class="batch\Formazione">
			<properties>
				<property name="mailerManager" type="object">data.apps.formazione.Managers.MailerManager</property>
			</properties>
		</object>-->
		<object id="batch.FormazioneUpdate" class="batch\FormazioneUpdate">
			<properties>
				<property name="mailerManager" type="object">data.apps.formazione.Managers.MailerManager</property>
				<property name="pdfManager" type="object">data.apps.formazione.Managers.PDFManager</property>
			</properties>
		</object>
		<object id="batch.ELearningFetchRemote" class="batch\ELearningFetchRemote"></object>
		<object id="batch.WeekELearningFetchRemote" class="batch\WeekELearningFetchRemote"></object>
		<object id="batch.FormazioneStatusMail" class="batch\FormazioneStatusMail">
			<properties>
				<property name="mailerManager" type="object">data.apps.formazione.Managers.MailerManager</property>
			</properties>
		</object>

		<object id="batch.AccordoEconomico" class="batch\AccordoEconomico">
			<properties>
				<property name="accordi" type="object">data.apps.accordo2.Repositories.AccordoRepository</property>
				<property name="revisioni" type="object">data.apps.accordo2.Repositories.AccordoRevRepository</property>
			</properties>
		</object>

		<object id="batch.CmsAgenzie" class="batch\CmsAgenzie">
			<properties>
				<property name="utils" type="object">service.GroupamaUtils</property>
				<property name="agenzieLegacy" type="object">data.AgenzieRepository</property>
				<property name="agenzieCMS" type="object">data.apps.cmsAgenzie.AgenciesDataRepository</property>
                <property name="usersLegacy" type="object">data.UsersRepository</property>
                <property name="usersCMS" type="object">data.apps.cmsAgenzie.EmployeesRepository</property>
			</properties>
		</object>

		<object id="batch.Rinn2023" class="batch\incentive\Rinn2023">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.rinn2023.Rinn2023</property>
				<property name="dataHasHeader" type="integer">1</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">1</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">1</property>
				<property name="debug" type="integer">1</property>
			</properties>
		</object>

        <object id="batch.AnagraficaCheck" class="batch\AnagraficaCheck">
            <properties>
                <property name="matrnx" type="object">batch.utils.anagrafica.Matrnx</property>
                <property name="nxca" type="object">batch.utils.anagrafica.Nxca</property>
                <property name="analyzer" type="object">batch.utils.anagrafica.Analyzer</property>
                <property name="renderer" type="object">batch.utils.anagrafica.Renderer</property>
                <property name="mailer" type="object">system.Mailer</property>
                <property name="backup" type="integer">0</property>
            </properties>
        </object>

		<object id="batch.Supernova2023" class="batch\incentive\Supernova2023">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.supernova.Supernova</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.Supernova2024" class="batch\incentive\Supernova2024">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.supernova2024.Supernova2024</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.SupernovaRF2024" class="batch\incentive\SupernovaRF2024">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.supernovaRF2024.SupernovaRF2024</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.Focus2023" class="batch\incentive\Focus2023">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.focus2023.Focus2023</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.Focus2024" class="batch\incentive\Focus2024">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.focus2024.Focus2024</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.Focus2025" class="batch\incentive\Focus2025">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.focus2025.Focus2025</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.FocusFase22023" class="batch\incentive\FocusFase22023">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.focusFase22023.FocusFase22023</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.FocusFase22024" class="batch\incentive\FocusFase22024">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.focusFase22024.FocusFase22024</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.Fullprotection2023" class="batch\incentive\Fullprotection2023">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.fullprotection2023.Fullprotection2023</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.Fullprotection2024" class="batch\incentive\Fullprotection2024">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.fullprotection2024.Fullprotection2024</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.Fullbenessere2024" class="batch\incentive\Fullbenessere2024">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.fullbenessere2024.Fullbenessere2024</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.RaccoltaVincente2025" class="batch\incentive\RaccoltaVincente2025">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.raccoltaVincente2025.RaccoltaVincente2025</property>
			</properties>
		</object>

		<object id="batch.NewProtection2025" class="batch\incentive\NewProtection2025">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.newProtection2025.NewProtection2025</property>
			</properties>
		</object>

		<object id="batch.Arag2024" class="batch\incentive\Arag2024">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.arag2024.Arag2024</property>
				<property name="dataHasHeader" type="integer">0</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.EventManager" class="batch\EventManager" >
			<properties>
				<property name="mailer" type="object">system.Mailer</property>
			</properties>
		</object>

		<object id="batch.Compass2025" class="batch\incentive\Compass2025">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.compass2025.Compass2025</property>
				<property name="dataHasHeader" type="integer">1</property>
				<property name="dataSeparator" type="string">;</property>
				<property name="staticHasHeader" type="integer">0</property>
				<property name="staticSeparator" type="string">,</property>
				<property name="enableMemProfiler" type="integer">0</property>
				<property name="debug" type="integer">0</property>
			</properties>
		</object>

		<object id="batch.GaraWelfare2025" class="batch\incentive\GaraWelfare2025">
			<properties>
				<property name="incentive" type="object">api.apps.incentive.garaWelfare2025.GaraWelfare2025</property>
			</properties>
		</object>

		<object id="batch.Stats" class="batch\Stats"></object>

		<!-- Managers -->
		<object id="batch.BatchManager" class="metadigit\lib\batch\BatchManager"/>
	</objects>
</context>
