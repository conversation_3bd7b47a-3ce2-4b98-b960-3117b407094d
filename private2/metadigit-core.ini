[settings]
traceLevel	= 7
charset		= UTF-8
locale		= it_IT.UTF-8
timeZone	= Europe/Rome

[namespaces]
api						= api
apps					= apps
batch					= batch
data					= data
service					= service
util					= util
metadigit\lib			= "phar:///srv/portaleagendo.it/vendor/metadigit-lib-2.0.5.phar"
metadigit\dev			= "/srv/portaleagendo.it/vendor/metadigit-dev"
fpdf					= "/srv/portaleagendo.it/vendor/fpdf"
Dompdf					= "/srv/portaleagendo.it/vendor/dompdf/src"
Svg					    = "/srv/portaleagendo.it/vendor/php-svg-lib/src"
PHPTAL					= "phar:///srv/portaleagendo.it/vendor/phptal-1.2.2.phar"
PHPExcel				= "phar:///srv/portaleagendo.it/vendor/phpexcel-1.8.1.phar"
Swift					= "phar:///srv/portaleagendo.it/vendor/swift-mailer-5.4.3.phar/classes"
Flow					= "phar:///srv/portaleagendo.it/vendor/flow-php-server-0.2.1.phar"
metadigit\webconsole	= "phar:///srv/portaleagendo.it/vendor/metadigit-webconsole-2.0.5.phar"
WhichBrowser			= "phar:///srv/portaleagendo.it/vendor/whichbrowser-parser-2.1.7.phar/src"
Foothing\Kpi			= '/srv/portaleagendo.it/vendor/php-kpi/src'
MathParser				= '/srv/portaleagendo.it/vendor/mossadal/math-parser/src/MathParser'
TrainingDev			    = '/srv/portaleagendo.it/vendor/training-dev/src'

[apps-http]
CONSOLE		= "8081|/|metadigit.webconsole.app"
; common
API_REST	= "443|/api/rest-api/|api.rest"
WS_GASITE	= "443|/api/webservices/ga-site/|api.webservices.gasite"
WS_ABI		= "443|/api/webservices/abi/|api.webservices.abi"
WEBSERVICE	= "443|/api/webservices/|api.webservices"
AUTH		= "443|/api/auth/|api.auth"
; main sections
API_ADMIN	= "443|/api/admin/|api.admin"
API_DOCS	= "443|/api/docs/|api.docs"
API_DOWNLOAD= "443|/api/download/|api.download"
API_ANALYTICS= "443|/api/analytics/|api.analytics"
API_INCENT	= "443|/api/incentivazioni|api.incentivazioni"
API_GARE	= "443|/api/gare|api.gare"
API_UPLOAD	= "443|/api/upload/|api.upload"
API_USERS	= "443|/api/users|api.users"
API_STATS	= "443|/api/stats|api.stats"
; apps
API_CONTACT	= "443|/api/apps/contact/|api.apps.contact"
API_CONTRIB	= "443|/api/apps/contributi/|api.apps.contributi"
API_CRUSCOT	= "443|/api/apps/cruscotti/|api.apps.cruscotti"
API_IVASS	= "443|/api/apps/ivass/|api.apps.ivass"
API_REPORT	= "443|/api/apps/reportistica/|api.apps.reportistica"
API_SANTINO	= "443|/api/apps/santino/|api.apps.santino"
API_TASSO	= "443|/api/apps/tassozero/|api.apps.tassozero"
API_WLCBK	= "443|/api/apps/welcomeback/|api.apps.welcomeback"
API_PIANIAG2= "443|/api/apps/pianiagenzia2/|api.apps.pianiagenzia2"
API_AVANZAM = "443|/api/apps/avanzamenti/|api.apps.avanzamenti"
API_NEO		= "443|/api/apps/neo/|api.apps.neo"
API_FORMAZ	= "443|/api/apps/formaz/|api.apps.formaz"
API_FORMAZIONE = "443|/api/apps/formazione/|api.apps.formazione"
API_MYPLACE	= "443|/api/apps/myplace/|api.apps.myplace"
API_OCTO	= "443|/api/apps/octo/|api.apps.octo"
API_CREDITS	= "443|/api/apps/credits/|api.apps.credits"
API_RESTART	= "443|/api/apps/restart/|api.apps.restart"
API_BESMART	= "443|/api/apps/besmart/|api.apps.besmart"
API_RINNOVIAMOCI	= "443|/api/apps/rinnoviamoci/|api.apps.incentive.rinnoviamoci"
API_RINNOVIAMOCI_FASE2	= "443|/api/apps/rinnoviamoci-fase2/|api.apps.incentive.rinnoviamociFase2"
API_ACCORDO2	= "443|/api/apps/accordo2/|api.apps.accordo2"
API_FOCUS	= "443|/api/apps/focus/|api.apps.incentive.focus2022"
API_FIRSTCHANCE	= "443|/api/apps/firstchance/|api.apps.incentive.firstchance"
API_FIDITALIA	= "443|/api/apps/fiditalia/|api.apps.incentive.fiditalia"
API_COMPASS	= "443|/api/apps/compass/|api.apps.incentive.compass"
API_INCENTIVE	= "443|/api/apps/incentive/|api.apps.incentive"
API_ARAG	= "443|/api/apps/arag/|api.apps.incentive.arag"
API_FULLPROTECTION	= "443|/api/apps/fullprotection/|api.apps.incentive.fullprotection"
API_DOLCEVITA	= "443|/api/apps/dolce-vita/|api.apps.incentive.dolcevita"
API_RINNOVIAMOCI_2023	= "443|/api/apps/rinnoviamoci-2023/|api.apps.incentive.rinn2023"
API_CASA	    = "443|/api/apps/casa/|api.apps.incentive.casa"
API_DINAMICHE	= "443|/api/apps/dinamiche/|api.apps.incentive.dinamiche"
API_INFORTUNI	= "443|/api/apps/infortuni/|api.apps.incentive.infortuni"
API_FOCUS_2023	= "443|/api/apps/focus-2023/|api.apps.incentive.focus2023"
API_AGIT    	= "443|/api/apps/agit/|api.apps.agit"
API_CMS_AGENZIE	= "443|/api/apps/cms-agenzie/|api.apps.cmsAgenzie"
API_EVENT_MANAGER = "443|/api/apps/event-manager/|api.apps.eventmanager"
API_SUPERNOVA	= "443|/api/apps/supernova/|api.apps.incentive.supernova"
API_FOCUS_2023_FASE2 = "443|/api/apps/focus-2023-fase2/|api.apps.incentive.focusFase22023"
API_FULLPROTECTION_2023	= "443|/api/apps/fullprotection-2023/|api.apps.incentive.fullprotection2023"
API_PARTNERSHIP	= "443|/api/apps/partnership/|api.apps.partnership"
API_FOCUS_2024	= "443|/api/apps/focus-2024/|api.apps.incentive.focus2024"
API_SUPERNOVA_2024	= "443|/api/apps/supernova-2024/|api.apps.incentive.supernova2024"
API_PORTALEGAREAREA	= "443|/api/apps/portalegarearea/|api.apps.portalegarearea"
API_FULLPROTECTION_2024	= "443|/api/apps/fullprotection-2024/|api.apps.incentive.fullprotection2024"
API_FULLBENESSERE_2024	= "443|/api/apps/fullbenessere-2024/|api.apps.incentive.fullbenessere2024"
API_UNIQUM	= "443|/api/apps/uniqum/|api.apps.uniqum"
API_ARAG_2024	= "443|/api/apps/arag-2024/|api.apps.incentive.arag2024"
API_FOCUS_2024_FASE2 = "443|/api/apps/focus-2024-fase2/|api.apps.incentive.focusFase22024"
API_SUPERNOVA_RF_2024	= "443|/api/apps/supernova-rf-2024/|api.apps.incentive.supernovaRF2024"
API_FOCUS_2025	= "443|/api/apps/focus-2025/|api.apps.incentive.focus2025"
API_DOWNLOADP	= "443|/api/apps/download/|api.apps.download"
API_COMPASS_2025	= "443|/api/apps/compass-2025/|api.apps.incentive.compass2025"
API_RACCOLTA_VINCENTE_2025	= "443|/api/apps/raccolta-vincente-2025/|api.apps.incentive.raccoltaVincente2025"
API_NEW_PROTECTION_2025	= "443|/api/apps/new-protection-2025/|api.apps.incentive.newProtection2025"
API_EVENTI_AREA	= "443|/api/apps/eventi-area/|api.apps.areaEvents"
API_GARA_WELFARE_2025	= "443|/api/apps/gara-welfare-2025/|api.apps.incentive.garaWelfare2025"
; Groupama API
API_GA 		= "443|/api/ga/|api.ga"
; UI
UI			= "443|/|apps.ui"

[apps-cli]
batch		= batch
webconsole	= metadigit.webconsole.batch

[constants]
ASSETS_DIR	= "/storage/portaleagendo.it/data/assets/"
ASSETS_URL	= "/assets/"
SWIFT_DIR	= "phar:///srv/portaleagendo.it/vendor/swift-mailer-5.4.3.phar/"
UPLOAD_DIR	= "/storage/portaleagendo.it/data/upload/"
DOWNLOAD_DIR	= "/storage/portaleagendo.it/data/downloads/"
PCHART_DIR	= "phar:///srv/portaleagendo.it/vendor/pChart-2.1.3.phar/"

[databases]
system		= "sqlite:/srv/portaleagendo.it/data/system.sqlite|null|null"
master		= "mysql:unix_socket=/var/run/mysqld/mysqld.sock;dbname=portaleagendo|portaleagendo|n3D80S1K!sm3lA!imN"

[logs]
kernel	= "LOG_INFO|kernel|metadigit\core\log\writer\FileWriter|kernel.log|null"
system	= "LOG_ERR||metadigit\core\log\writer\SqliteWriter|system|log"
