<?php namespace util;

/**
 * Configuration class for Welfare-related constants and settings.
 * 
 * This class centralizes welfare agency configurations to avoid duplication
 * across multiple controllers and services.
 */
class WelfareConfig
{
    /**
     * List of agencies participating in welfare programs.
     * 
     * @var array
     */
    const PARTICIPATING_AGENCIES = [
        "G030", "G031", "G045", "G051", "G059", "G065", "G067", "G075", "G081", "G101", 
        "G114", "G116", "G117", "G119", "G122", "G126", "G143", "G146", "G159", "G162", 
        "G163", "G174", "G187", "G190", "G193", "G203", "G211", "G243", "G244", "G249", 
        "G254", "G256", "G260", "G269", "G277", "G295", "G296", "G306", "G371", "G375", 
        "G411", "G428", "G431", "G435", "G451", "G454", "G471", "G476", "G482", "G484", 
        "G486", "G542", "G766", "G816", "G837", "G848", "G850", "G857", "G864", "G875", 
        "G902", "G943", "G980", "G981", "G983", "G993", "GL23", "GL60", "GL63", "GL85", 
        "GM12", "GM15", "GM19", "GM21", "GM23", "GM28", "GM40", "GM43", "GM51", "GM53", 
        "GM63", "M18", "M35", "N033", "N036", "N048", "N050", "N079", "N086", "N144", 
        "N173", "N180", "N268", "N312", "N335", "N392", "N465", "N479", "N569", "N852", 
        "N863", "N959", "N984", "N992", "NB36", "NC60", "NC70", "ND16", "ND66", "ND70", 
        "NF22", "NF54"
    ];

    /**
     * Check if an agency is allowed for welfare programs.
     * 
     * @param string $agencyCode The agency code to check
     * @return bool True if the agency is allowed, false otherwise
     */
    public static function isAllowedWelfareAgency($agencyCode)
    {
        return in_array($agencyCode, self::PARTICIPATING_AGENCIES);
    }

    /**
     * Check if the current session user's agency is allowed for welfare programs.
     * Also checks for admin/KA user types which have full access.
     * 
     * @return bool True if allowed, false otherwise
     */
    public static function isCurrentUserAllowedWelfare()
    {
        $agencyCode = $_SESSION['AUTH']['AGENZIA'] ?? null;
        $uType = $_SESSION['AUTH']['UTYPE'] ?? null;
        
        // Admin and KA users have full access
        if ($uType !== 'AGENTE' && $uType !== 'INTERMEDIARIO') {
            return true;
        }
        
        // Check if agency is in the allowed list
        return $agencyCode && self::isAllowedWelfareAgency($agencyCode);
    }
}
