<?php

namespace data\apps\formazione\Managers;


use metadigit\core\CoreTrait;
use metadigit\webconsole\Kernel;
use TrainingDev\Managers\ClassRoomManager;
use data\apps\formazione\Managers\DashboardManager;
use TrainingDev\Repositories\Interfaces\AttendanceRepositoryInterface;
use TrainingDev\Repositories\Interfaces\ClassRoomRepositoryInterface;
use TrainingDev\Repositories\Interfaces\CourseRepositoryInterface;
use TrainingDev\Repositories\Interfaces\LocationRepositoryInterface;
use TrainingDev\Repositories\Interfaces\RecipientRepositoryInterface;
use TrainingDev\Repositories\Interfaces\UserRepositoryInterface;

class BookingManager
{
    use CoreTrait;

    /**
     * @var RecipientRepositoryInterface
     */
    protected $recipientRepository;
    /**
     * @var CourseRepositoryInterface
     */
    protected $courseRepository;
    /**
     * @var ClassRoomRepositoryInterface
     */
    protected $classroomRepository;

    /**
     * @var AttendanceRepositoryInterface
     */
    protected $attendanceRepository;

    /**
     * @var UserRepositoryInterface
     */
    protected $userRepository;

    /**
     * @var LocationRepositoryInterface
     */
    protected $locationRepository;

    /**
     * @var ClassRoomManager
     */
    protected $classroom;

    /**
     * Retrieve recipients by course
     *
     * @param $classroomId
     * @param $agencyCode
     * @param $authorId
     * @return array
     */
    public function getRecipients($classroomId, $agencyCode, $authorId)
    {
        if (! $class = $this->classroomRepository->find($classroomId)) {
            return null;
        }

        return $this->recipientRepository->getRecipientsByClassroom($class->course_id, $class->id, $agencyCode, $authorId);
    }

    /**
     * Retrieve the course and it classes that user can subscribe
     *
     * @param $courseId
     * @param $agencyCode
     * @param $userId
     * @return array
     * @throws \Exception
     */
    public function getAvailableClassrooms($courseId, $agencyCode, $userId)
    {
        $today = new \DateTime('now');
        $classrooms = $result = [];

        if (! $course = $this->courseRepository->find($courseId)) {
            return [];
        }

        $classes = $this->classroomRepository->findByCourse($courseId);

        foreach ($classes as $class) {
            $classrooms[] = $this->getClassRoomStatus($class);
        }

        $result = [
            "classrooms" => $classrooms
        ];

        return $result;
    }

    public function getClassRoomStatus($class)
    {
        $days = $class->getPropertyFromData('days', []);

        $facilityName = $address = null;
        if (!is_null($class->location_id) && $location = $this->locationRepository->find($class->location_id)) {
            $facilityName = $location->getPropertyFromData('facilityName', []);

            $address = $location->getPropertyFromData('address', []);
        }

        return [
            "id" => $class->id,
            "city" => $class->city,
            "location" => $location->name,
            "address" => ($address) ? $address : "",
            "startDate" => $this->getStartDate($days),
            "startHour" => $this->getStartHour($days),
            "endDate" => $this->getEndDate($days),
            "signupStartDate" => (new \DateTime($class->signupStart))->format("d/m/Y"),
            "signupEndDate" => (new \DateTime($class->signupEnd))->format("d/m/Y"),
            "vacantSeats" => $this->classroom->getVacancies($class),
            "queuedUsers" => $this->getQueuedUsers($class),
            "totalSeats" => $class->seats,
            'days' => $this->getLocationDates(json_decode($class->data)),
        ];
    }

    /**
     * Return number of queued users presents in given classroom.
     *
     * @param $class
     * @return mixed
     */
    public function getQueuedUsers($class)
    {
        return $this->attendanceRepository->countQueuedUsers($class);
    }


    /**
     * Get Start Date from dates array
     *
     * @param $days
     * @return string
     * @throws \Exception
     */
    private function getStartDate($days)
    {
        if (! is_array($days) || count($days) <= 0) {
            return "";
        }

        if (! isset($days[0]["date"])) {
            return "";
        }

        return (new \DateTime($days[0]["date"]))->format("d/m/Y");
    }

    /**
     * Get Start Hour from dates array
     *
     * @param $days
     * @return string
     */
    private function getStartHour($days)
    {
        if (! is_array($days) || count($days) <= 0) {
            return "";
        }

        if (! isset($days[0]["startTime"])) {
            return "";
        }

        return $days[0]["startTime"];
    }

    /**
     * Get End Date from dates array
     *
     * @param $days
     * @return string
     * @throws \Exception
     */
    private function getEndDate($days)
    {
        $count = count($days);

        if (! is_array($days) || $count <= 0) {
            return "";
        }

        if (! isset($days[$count - 1]["date"])) {
            return "";
        }

        return (new \DateTime($days[$count - 1]["date"]))->format("d/m/Y");
    }

    /**
     * Booking
     *
     * @param $user
     * @param $classroom
     * @param $authorId
     * @param $action
     * @return mixed
     * @throws \TrainingDev\Exceptions\CannotAddUserToClassException
     */
    public function book($user, $classroom, $authorId, $action)
    {
        if (! $this->checkCourseLimits($user, $classroom)) {
            throw new \Exception("The limit has been reached", 112);
        }

        if (!$this->checkUserInRecipient($user->id, $classroom->course_id)) {
            throw new \Exception("User is not exists in recipients", 113);
        }

        if (!$this->checkBookDates($classroom)) {
            throw new \Exception("The signup dates is invalid", 114);
        }

        if ($action === 'booked' && $this->classroom->isClassroomFull($classroom)) {
            throw new \Exception("The classroom is full", 118);
        }

        if (! $booking = $this->classroom->addUser($classroom->id, $user->id, $action)) {
            throw new \Exception("User has not been added", 500);
        }

        return $this->recipientRepository->getRecipientsByClassroom($classroom->course_id, $classroom->id, $user->agenzia_id, $authorId);
    }

    /**
     * Check limits course
     *
     * @param $user
     * @param $classroom
     * @return bool
     * @throws \Exception
     */
    public function checkCourseLimits($user, $classroom)
    {
        if (! $course = $this->courseRepository->find($classroom->course_id)) {
            throw new \Exception("Course not found");
        }

        if (! $limit = $this->getCourseLimit($course, $user->type)) {
            $this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, "Course $course->id has no limit for {$user->type}");
            return true;
        }

        $count = $this->attendanceRepository->countAttendees($classroom->course_id, $user->agenzia_id);

        $this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, "Check limit: $count < $limit");

        return $count < $limit;
    }

    /**
     * Retrieve the limit course
     *
     * @param $course
     * @param $type
     * @return mixed
     */
    public function getCourseLimit($course, $type)
    {
        $limit = $course->getPropertyFromData('agencyLimit');

        $this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, "Employee limit: $limit");

        return $course->getPropertyFromData('agencyLimit');
    }

    /**
     * Check user is in recipients
     *
     * @param $userId
     * @param $courseId
     * @return bool
     */
    public function checkUserInRecipient($userId, $courseId)
    {
        if ($this->recipientRepository->findOne($userId, $courseId)) {
            return true;
        }

        return false;
    }

    /**
     * Check signup dates
     *
     * @param $classroom
     * @return bool
     * @throws \Exception
     */
    public function checkBookDates($classroom)
    {
        $today     = new \DateTime('today midnight');
        $startDate = new \DateTime($classroom->signupStart);
        $endDate   = new \DateTime($classroom->signupEnd);

        if ($today >= $startDate && $today <= $endDate) {
            return true;
        }

        return false;
    }

    /**
     * Retrieve classrooms of UIQUM courses in which logged user has bookings
     *
     * @param $agencyCode
     * @return array
     */
    public function getUniqumClassroomsWithBookings($agencyCode)
    {
        $courses = $this->courseRepository->findUniqumCoursesByAgency($agencyCode);

        foreach ($courses as $courseKey => $courseItem) {
            // Prendo solo le aule in cui l'agenzia ha "presenze", quindi le inserisco nel corso
            $courses[$courseKey]['classrooms'] = $this->classroomRepository->findByAgency($courseItem['id'], $agencyCode);
            foreach ($courses[$courseKey]['classrooms'] as $key => $item) {
                // Decodifico data (json) per ciclare i giorni nelle aule che hanno più di un giorno
                $courses[$courseKey]['classrooms'][$key]['data'] = json_decode($courses[$courseKey]['classrooms'][$key]['data']);
                // Prendo le "presenze" dell'Agenzia, quindi le inserisco nell'aula
                $courses[$courseKey]['classrooms'][$key]['bookings'] = $this->attendanceRepository->findByClassroom($item['id'], $agencyCode);
            }
        }

        return $courses;

    }

    protected function getLocationDates($data)
    {
        $days = [];
        foreach ($data->days as $day) {
            $date = new \DateTime($day->date);
            $days[] = $date->format('d/m/Y');
        }
        return $days;
    }

}
