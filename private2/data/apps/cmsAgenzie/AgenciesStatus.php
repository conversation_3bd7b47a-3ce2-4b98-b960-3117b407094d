<?php namespace data\apps\cmsAgenzie;

use api\utils\ConvertToJson,
    data\apps\formazione\Models\Traits\GetPropertyFromData;

/**
 * @orm(source="vw_cms_agenzie_status", target="cms_agenzie_status")
 */
class AgenciesStatus
{
    use \metadigit\core\db\orm\EntityTrait, ConvertToJson, GetPropertyFromData;

    /**
     * @orm(primarykey)
     */
    protected $agenzia_id;

    /**
     * @orm(string)
     */
    protected $groupama_id;

    /**
     * @orm(type="integer")
     */
    protected $approved;

    /**
     * @orm(type="string")
     */
    protected $ragioneSociale;

    /**
     * @orm(type="integer")
     */
    protected $standard;

    /**
     * @orm(type="integer")
     */
    protected $user_id;

    /**
     * @orm(type="datetime")
     */
    protected $updated_at;

    /**
     * @orm(type="string")
     */
    protected $link;

    /**
     * @orm(type="string")
     */
    protected $shortLink;

    /**
     * @orm(readonly)
     */
    protected $area;

    /**
     * @orm(readonly)
     */
    protected $distretto;

    /**
     * @orm(readonly)
     */
    protected $localita;

    /**
     * @orm(readonly)
     */
    protected $agents;

    /**
     * @orm(readonly)
     */
    protected $nome;

    /**
     * @orm(readonly)
     */
    protected $hasAgencyPhoto;

    /**
     * @orm(readonly)
     */
    protected $detail;

    /**
     * @orm(readonly)
     */
    protected $privacyAccepted;

    /**
     * @orm(readonly)
     */
    protected $teamTotal;


    /**
     * @return mixed
     */
    public function getAgenziaId()
    {
        return $this->agenzia_id;
    }

    /**
     * @param mixed $agenzia_id
     */
    public function setAgenziaId($agenzia_id)
    {
        $this->agenzia_id = $agenzia_id;
    }

    /**
     * @return mixed
     */
    public function getapproved()
    {
        return $this->approved;
    }

    /**
     * @param mixed $approved
     */
    public function setapproved($approved)
    {
        $this->approved = $approved;
    }

    /**
     * @return mixed
     */
    public function getArea()
    {
        return $this->area;
    }

    /**
     * @param mixed $area
     */
    public function setArea($area)
    {
        $this->area = $area;
    }

    /**
     * @return mixed
     */
    public function getDistretto()
    {
        return $this->distretto;
    }

    /**
     * @param mixed $distretto
     */
    public function setDistretto($distretto)
    {
        $this->distretto = $distretto;
    }

    /**
     * @return mixed
     */
    public function getLink()
    {
        return $this->link;
    }

    /**
     * @param mixed $link
     */
    public function setLink($link)
    {
        $this->link = $link;
    }

    /**
     * @return mixed
     */
    public function getShortLink()
    {
        return $this->shortLink;
    }

    /**
     * @param mixed $shortLink
     */
    public function setShortLink($shortLink)
    {
        $this->shortLink = $shortLink;
    }

}
