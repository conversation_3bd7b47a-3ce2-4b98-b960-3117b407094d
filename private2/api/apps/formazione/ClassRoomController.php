<?php

namespace api\apps\formazione;

use api\utils\MakePaginationData;
use data\apps\formazione\Repositories\AgencyRepository;
use data\apps\formazione\Repositories\AreaClassRoomRepository;
use data\apps\formazione\Repositories\AttendanceAreaMngRepository;
use data\apps\formazione\Repositories\AttendanceRepository;
use data\apps\formazione\Repositories\ClassRoomRepository;
use data\apps\formazione\Repositories\CourseRepository;
use data\apps\formazione\Repositories\FileRepository;
use data\apps\formazione\Repositories\UserRepository;
use data\apps\formazione\Utils\CharsetConversion;
use data\apps\formazione\Utils\Json;
use metadigit\core\db\orm\Repository;
use metadigit\core\http\Request;
use metadigit\core\http\Response;
use TrainingDev\Exceptions\UneligibleUserException;
use TrainingDev\Managers\ClassRoomManager;
use TrainingDev\Managers\ReminderManager;

class ClassRoomController extends BaseController
{
    use MakePaginationData;

    /**
     * @var ClassRoomManager
     */
    protected $manager;

    /**
     * @var ClassRoomRepository
     */
    protected $repository;

    /**
     * @var AttendanceRepository
     */
    protected $attendanceRepository;

    /**
     * @var CourseRepository
     */
    protected $courseRepository;

    /**
     * @var CharsetConversion
     */
    protected $charsetConversion;

    /**
     * @var AreaClassRoomRepository
     */
    protected $areaClassesRepository;

    /**
     * @var UserRepository
     */
    protected $userRepository;

    /**
     * @var ReminderManager
     */
    protected $reminderManager;

    /**
     * @var AttendanceAreaMngRepository
     */
    protected $attendanceAreaMngRepository;

    /**
     * @var AgencyRepository
     */
    protected $agencies;

    /**
     * @var FileRepository
     */
    protected $files;

    /**
     * Retrieve all Classes from a given course.
     *
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    function indexAction(Request $Req, Response $Res)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        $result['items'] = $this->attendanceAreaMngRepository->findClassrooms($data);

        if ($paginationData->pageSize && ($result['items'] <= $paginationData->pageSize)) {
            $result['total'] = count($result['items']);
        } else {
            $result['total'] = count($this->attendanceAreaMngRepository->findClassrooms($data, false));
        }

        // Decoding json in the results
        foreach ($result['items'] as $item) {
            if (property_exists($item, 'data')) {
                $item->data = json_decode($item->data);
            }
        }

        if (isset($data['excel']) && $data['excel']) {

            $result['items'] = $this->charsetConversion->convert($result['items'],"UTF-8", "Windows-1252");

            return $Res
                ->set('data', $result['items'])
                ->set('saveName', strtotime('now').'_classRoomList')
                ->setView('file-excel:/course');
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Retrieve ClassRoom with specific id
     *
     * @routing(method="GET", pattern="/view/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     * @throws \metadigit\core\db\orm\Exception
     */
    public function viewAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        if ($classroom = $this->repository->fetch($id)) {
            $success = true;
            $course = $this->courseRepository->fetch($classroom->course_id);
            if ( $course->groupamaType === 'area' ) {
                $memo = $this->files->fetchAll(null,null,null,"course_id,EQ,$classroom->course_id|class_id,EQ,$classroom->id|type,EQ,MEMO", Repository::FETCH_ARRAY);
                $participants = $this->files->fetchAll(null,null,null,"course_id,EQ,$classroom->course_id|class_id,EQ,$classroom->id|type,EQ,PARTICIPANTS", Repository::FETCH_ARRAY);
                $classroom->memo = $memo[0] ?? null;
                $classroom->participants = $participants[0] ?? null;
            }
            $Res->set('data', $classroom->json());
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Change Status to a Class
     *
     * @routing(method="PUT", pattern="/<id>/status")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $id
     */
    function statusAction(Request $Req, Response $Res, $id)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        try {
            // Change status
            $this->manager->changeStatus($id, $data);

            // Retrieve class
            $class = $this->repository->find($id);

            $success = true;
            $Res->set('data', $class->json());
        } catch(OrmException $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Retrieve booked Attendance
     *
     * @routing(method="GET", pattern="/<classId>/booked")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    public function bookedAction(Request $Req, Response $Res, $classId)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        if ($paginationData->criteriaExp) {
            $criteriaExp[] = $paginationData->criteriaExp;
        }
        $criteriaExp[] = 'class_id,EQ,'.$classId;
        $criteriaExp[] = 'state,IN,booked,signedup,unsigned';

        $attendances = $this->attendanceRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            implode('|', $criteriaExp)
        );

        $result['items'] = Json::jsonCollection($attendances);

        $result['total'] = $this->attendanceRepository->count(implode('|', $criteriaExp));

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Retrieve queued Attendance
     * @routing(method="GET", pattern="/<classId>/queued")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    public function queuedAction(Request $Req, Response $Res, $classId)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        if ($paginationData->criteriaExp) {
            $criteriaExp[] = $paginationData->criteriaExp;
        }
        $criteriaExp[] = 'class_id,EQ,'.$classId;
        $criteriaExp[] = 'state,EQ,queued';

        $queued = $this->attendanceRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            implode('|', $criteriaExp)
        );

        $result['items'] = Json::jsonCollection($queued);

        $result['total'] = $this->attendanceRepository->count(implode('|', $criteriaExp));

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Retrieve signed Attendance
     * @routing(method="GET", pattern="/<classId>/signed")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    public function signedAction(Request $Req, Response $Res, $classId)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        if ($paginationData->criteriaExp) {
            $criteriaExp[] = $paginationData->criteriaExp;
        }
        $criteriaExp[] = 'class_id,EQ,'.$classId;
        $criteriaExp[] = 'state,EQ,signedUp';

        $signed = $this->attendanceRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            implode('|', $criteriaExp)
        );

        $result['items'] = Json::jsonCollection($signed);

        $result['total'] = $this->attendanceRepository->count(implode('|', $criteriaExp));

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Force Add an user to this classroom.
     *
     * @routing(method="POST", pattern="/<classId>/add")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    function addUserAction(Request $Req, Response $Res, $classId)
    {
        if (! $class = $this->repository->find($classId)) {
            return $Res
                ->set('success', false)
                ->set('message', 'Class not exists')
                ->setView('json:');
        }

        if (! $course = $this->courseRepository->find($class->course_id)) {
            return $Res
                ->set('success', false)
                ->set('message', 'Course not exists')
                ->setView('json:');
        }

        if (! $user = $this->userRepository->find($Req->get('user')->id)) {
            return $Res
                ->set('success', false)
                ->set('message', 'User does not exists')
                ->setView('json:');
        }

        $area = null;
        if ($class->groupamaType == 'area') {
            if (isset($_SESSION['AUTH']) && ($_SESSION['AUTH']['UTYPE'] == 'AREAMGR' || $_SESSION['AUTH']['UTYPE'] == 'AREAMGR_COLLAB')) {
                $area = $_SESSION['AUTH']['AREA'];
            }
        }
        //$result = $this->manager->forceAddUser($class, $user, 'signedup');

        try {
            // Area is the same of areamng or areamng_collab
            if (($area) && ($user->area != $area)) {
                throw new UneligibleUserException("The user have an area different from areamng", 108);
            }

            $_SESSION['AUTH']['UTYPE'] === 'DISTRICTMGR' ? $attendanceStatus = "booked" : $attendanceStatus = "signedup";

            $result = $this->manager->forceAddUser($class, $user, $attendanceStatus);

            return $Res
                ->set('success', true)
                ->set('data', $result->json())
                ->setView('json:');
        }

        catch (UneligibleUserException $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getCode())
                ->set('classroom', $ex->getClass())
                ->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * Signup an user to this classroom.
     *
     * @routing(method="PUT", pattern="/<classId>/signup")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    function signupAction(Request $Req, Response $Res, $classId)
    {
        try {
            $result = $this->manager->signup($classId, $Req->get('attendance')->user_id);

            return $Res->set('success', true)->set('data', $result->json())->setView('json:');
        }

        catch (UneligibleUserException $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getCode())
                ->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * Reset an user to this classroom.
     *
     * @routing(method="PUT", pattern="/<classId>/reset")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    function resetAction(Request $Req, Response $Res, $classId)
    {
        try {
            $result = $this->manager->resetSignup($classId, $Req->get('attendance')->user_id);

            return $Res->set('success', true)->set('data', $result->json())->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', 0)
                ->setView('json:');
        }
    }

    /**
     * Sign up all Attendance
     *
     * @routing(method="PUT", pattern="/<classId>/signupall")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     */
    function signupAllAction(Request $Req, Response $Res, $classId)
    {
        $success = false;

        try {
            $this->attendanceRepository->signupAllBooked($classId);

            $success = true;
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Promote every Attendance
     *
     * @routing(method="PUT", pattern="/<classId>/promoteall")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     */
    function promoteAllAction(Request $Req, Response $Res, $classId)
    {
        $success = false;

        if (! $class = $this->repository->find($classId)) {
            return $Res
                ->set('success', false)
                ->set('message', 'Class not exists')
                ->setView('json:');
        }

        try {
            $this->manager->promoteAll($class);

            $success = true;
        } catch(\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Create new ClassRoom
     *
     * @routing(method="POST", pattern="/<courseId>/create")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $courseId
     */
    public function createAction(Request $Req, Response $Res, $courseId)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        $data['area'] = (isset($data['area_id']) && $data['area_id']) ? $data['area_id'] : null;
        $data['status'] = 'active';
        $data['sendMemo'] = '0';

        try {
            if (! $course = $this->courseRepository->find($courseId)) {
                throw new \Exception("Course not found");
            }

            if ($course->groupamaType == 'area') {
                if (in_array($_SESSION['AUTH']['UTYPE'], [ 'AREAMGR', 'AREAMGR_COLLAB' ])) {
                    $data['area'] = ($_SESSION['AUTH']['AREA']) ? $_SESSION['AUTH']['AREA'] : null;
                }
            }

            if ($course->groupamaType == 'direz') {
                if (! $data['seats']) {
                    throw new \Exception("The seats field is required");
                }
            }

            if (count($data['data']['days']) == 0) {
                throw new \Exception("There must be at least a date");
            }

            $data['firstDay'] = $data['data']['days'][0]['date'];

            $result = $this->manager->create($data, $courseId);

            $success = true;
            $Res->set('data', $result->json());
        } catch (\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Retrieve deleted Attendance
     *
     * @routing(method="GET", pattern="/<classId>/deleted")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    public function deletedAction(Request $Req, Response $Res, $classId)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        if ($paginationData->criteriaExp) {
            $criteriaExp[] = $paginationData->criteriaExp;
        }
        $criteriaExp[] = 'class_id,EQ,'.$classId;
        $criteriaExp[] = 'state,IN,deleted';

        $result['items'] = $this->attendanceRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            implode('|', $criteriaExp)
        );

        $result['items'] = Json::jsonCollection($result['items']);

        $result['total'] = $this->attendanceRepository->count(implode('|', $criteriaExp));

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Retrieve all Classes from a given course.
     *
     * @routing(method="GET", pattern="/area")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    function areaListAction(Request $Req, Response $Res)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        $result['items'] = $this->areaClassesRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            $paginationData->criteriaExp
        );

        $result['items'] = Json::jsonCollection($result['items']);

        $result['total'] = $this->areaClassesRepository->count($paginationData->criteriaExp);

        if (isset($data['excel']) && $data['excel']) {

            $result['items'] = $this->charsetConversion->convert($result['items'],"UTF-8", "Windows-1252");

            return $Res
                ->set('data', $result['items'])
                ->set('saveName', strtotime('now').'_classRoomList')
                ->setView('file-excel:/course');
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }



    /**
     * Create new ClassRoom
     *
     * @routing(method="POST", pattern="/<classId>/update")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     */
    public function updateAction(Request $Req, Response $Res, $classId)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        $data['area'] = ($data['area_id']) ? $data['area_id'] : null;

        try {
            if (! $classroom = $this->repository->find($classId)) {
                throw new \Exception("Class not found");
            }

            if (! $course = $this->courseRepository->find($classroom->course_id)) {
                throw new \Exception("Course associated to this class not found");
            }

            if ($course->groupamaType == 'area') {
                if (in_array($_SESSION['AUTH']['UTYPE'], [ 'AREAMGR', 'AREAMGR_COLLAB' ])) {
                    $data['area'] = ($_SESSION['AUTH']['AREA']) ? $_SESSION['AUTH']['AREA'] : null;
                }
            }

            if ($course->groupamaType == 'direz') {
                if (! $data['seats']) {
                    throw new \Exception("The seats field is required");
                }
            }

            if (count($data['data']['days']) == 0) {
                throw new \Exception("There must be at least a date");
            }

            $data['firstDay'] = $data['data']['days'][0]['date'];

            $result = $this->repository->update($classroom->id, $data);

            $success = true;
            $Res->set('data', null);
        } catch (\Exception $Ex) {
            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Reminder
     *
     * @routing(method="POST", pattern="/<classId>/reminder")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     */
    public function reminderAction(Request $Req, Response $Res, $classId)
    {
        $success = false;

        $data = json_decode($Req->getRawData(), true);

        $regionIds = ($data['regionIds']) ? $data['regionIds'] : null;

        try {
            if (! $regionIds || count($regionIds) == 0) {
                throw new \Exception("The regionIds are required", 400);
            }

            $this->reminderManager->subscriptionRequest($classId, $regionIds);

            $success = true;
        } catch(\Exception $Ex) {

            switch($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->repository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(),true));
                    break;
                case 400:
                    $Res->set('success', $success)
                        ->set('error', $Ex->getMessage())
                        ->setView('json:');
                    break;
            }
        }

        $Res->set('success', $success)->setView('json:');
    }

    /**
     * Retrieve all attendances.
     *
     * @routing(method="GET", pattern="/<classId>/attendees")
     * @param Request $Req
     * @param Response $Res
     * @param integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    function excelAction(Request $Req, Response $Res, $classId)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        if ($paginationData->criteriaExp) {
            $criteriaExp[] = $paginationData->criteriaExp;
        }
        $criteriaExp[] = 'class_id,EQ,'.$classId;

        if (isset($data['excel']) && $data['excel']) {
            $criteriaExp[] = 'state,IN,signedup';
        }

        $attendances = $this->attendanceRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            implode('|', $criteriaExp)
        );

        $result['items'] = Json::jsonCollection($attendances);

        $result['total'] = $this->attendanceRepository->count(implode('|', $criteriaExp));

        if (isset($data['excel']) && $data['excel']) {

            $attendances = $this->charsetConversion->convert($attendances,"UTF-8", "Windows-1252");

            foreach ($attendances as $attendance) {
                $attendance->translatePropertyForExcel();
            }

            return $Res
                ->set('data', $attendances)
                ->set('saveName', strtotime('now').'_classRoomAttendeesList')
                ->setView('file-excel:/attendee');
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Delete area classroom.
     *
     * @routing(method="DELETE", pattern="/<classId>/area")
     * @param Request $Req
     * @param Response $Res
     * @param int $classId
     */
    public function areaDeleteAction(Request $Req, Response $Res, $classId)
    {
        if (!$this->repository->removeAreaClassroom($classId)) {
            $Res->set('success', false)
                ->set('error', "An error occurred during the classroom area deleting")
                ->setView('json:');
        }

        $Res->set('success', true)->setView('json:');
    }

    /**
     * Get all deleted users.
     *
     * @routing(method="GET", pattern="/<classId>/excel-deleted")
     * @param Request $Req
     * @param Response $Res
     * @param integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    function excelDeletedAction(Request $Req, Response $Res, $classId)
    {

        $attendances = $this->attendanceRepository->fetchAll(null, null, null, "class_id,EQ,$classId|state,EQ,deleted");
        $attendances = $this->charsetConversion->convert($attendances, "UTF-8", "Windows-1252");
        foreach ($attendances as $attendance) {
            $attendance->translatePropertyForExcel();

        }

        return $Res
            ->set('data', $attendances)
            ->set('saveName', strtotime('now') . '_classRoomAttendeesList')
            ->setView('file-excel:/attendee');
    }

    /**
     * Find all active users in given agency, then sign them up.
     *
     * @routing(method="POST", pattern="/<classId>/add-agency/<agencyId>")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @param Integer $agencyId
     * @throws \metadigit\core\db\orm\Exception
     */
    function addAgencyAction(Request $Req, Response $Res, $classId, $agencyId)
    {
        if (! $class = $this->repository->find($classId)) {
            return $Res
                ->set('success', false)
                ->set('message', 'Class not exists')
                ->setView('json:');
        }

        try {

            $users = $this->userRepository->fetchAll(null,null,null,"agencyId,EQ,$agencyId|active,EQ,1", Repository::FETCH_ARRAY);

            foreach ($users as $user) {

                // Skippo inserimento utente se già presente nell'aula
                if ( $this->attendanceRepository->findByUser($class->id, $user['id']) ) {
                    continue;
                }

                $user = $this->manager->forceAddUser($class, (object)$user, "signedup");
                $result[] = (array) $user;

            }

        }
        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Retrieve all attendance besides type ok
     *
     * @routing(method="GET", pattern="/<classId>/enrolled")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    public function enrolledAction(Request $Req, Response $Res, $classId)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        if ($paginationData->criteriaExp) {
            $criteriaExp[] = $paginationData->criteriaExp;
        }
        $criteriaExp[] = 'class_id,EQ,'.$classId;
        $criteriaExp[] = 'state,IN,booked,unsigned,queued,deleted';
        //$criteriaExp[] = 'result,EQ,none';

        $attendances = $this->attendanceRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            implode('|', $criteriaExp)
        );

        $result['items'] = Json::jsonCollection($attendances);

        $result['total'] = $this->attendanceRepository->count(implode('|', $criteriaExp));

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

    /**
     * Retrieve all attendance besides type ok
     *
     * @routing(method="GET", pattern="/<classId>/registry")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $classId
     * @throws \metadigit\core\db\orm\Exception
     */
    public function registryAction(Request $Req, Response $Res, $classId)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        if ($paginationData->criteriaExp) {
            $criteriaExp[] = $paginationData->criteriaExp;
        }
        $criteriaExp[] = 'class_id,EQ,'.$classId;
        $criteriaExp[] = 'state,EQ,signedup';

        $attendances = $this->attendanceRepository->fetchAll(
            $paginationData->page,
            $paginationData->pageSize,
            $paginationData->orderExp,
            implode('|', $criteriaExp)
        );

        $result['items'] = Json::jsonCollection($attendances);

        $result['total'] = $this->attendanceRepository->count(implode('|', $criteriaExp));

        return $Res
            ->set('success', true)
            ->set('data', $result)
            ->setView('json:');
    }

}
