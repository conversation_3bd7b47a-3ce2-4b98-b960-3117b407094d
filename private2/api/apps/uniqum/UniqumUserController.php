<?php

namespace api\apps\uniqum;

use api\utils\MakePaginationData;
use data\apps\formazione\Utils\CharsetConversion;
use data\apps\formazione\Utils\Json;
use data\apps\uniqum\TeamMemberRepository;
use data\apps\uniqum\UniqumUserRepository;
use metadigit\core\db\orm\Repository;
use metadigit\core\http\Request;
use metadigit\core\http\Response;

class UniqumUserController extends \metadigit\core\web\controller\ActionController
{

    use MakePaginationData;

    /**
     * @var UniqumUserRepository
     */
    protected $users;

    /**
     * @var TeamMemberRepository
     */
    protected $members;

    /**
     * @var CharsetConversion
     */
    protected $charsetConversion;

    /**
     * Get users not yet enrolled
     *
     * @routing(method="GET", pattern="/enrollable")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    function enrollableUsersAction(Request $Req, Response $Res)
    {

        if ( $_SESSION['AUTH']['UTYPE'] !== 'AGENTE' ) {
            http_response_code(401);
            return $Res
                ->set('success', false)
                ->set('message', 'Forbidden access')
                ->setView('json:');
        }

        //$agency = $_SESSION['AUTH']['AGENZIA'];
        /*echo '<pre>';
        print_r($users);
        echo '</pre>';
        return;*/

        try {

            $users = $this->users->fetchAll(null,null,null,"enrolled,EQ,0", Repository::FETCH_ARRAY);

            return $Res
                ->set('success', true)
                ->set('data',$users)
                ->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * Get enrolled users
     *
     * @routing(method="GET", pattern="/enrolled")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    function enrolledUsersAction(Request $Req, Response $Res)
    {

        if ( $_SESSION['AUTH']['UTYPE'] !== 'AGENTE' ) {
            http_response_code(401);
            return $Res
                ->set('success', false)
                ->set('message', 'Forbidden access')
                ->setView('json:');
        }

        try {

            $users = $this->members->fetchAll(null,null,null,null, Repository::FETCH_ARRAY);

            return $Res
                ->set('success', true)
                ->set('data',$users)
                ->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * Enroll user
     *
     * @routing(method="POST", pattern="/enroll")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    function enrollUserAction(Request $Req, Response $Res)
    {
        http_response_code(400);
        return $Res
            ->set('success', false)
            ->set('message', 'Locked')
            ->setView('json:');

        if ( $_SESSION['AUTH']['UTYPE'] !== 'AGENTE' ) {
            http_response_code(401);
            return $Res
                ->set('success', false)
                ->set('message', 'Forbidden access')
                ->setView('json:');
        }

        $data = (array) json_decode($Req->getRawData());
        $data['createdBy'] = $_SESSION['AUTH']['UID'];
        $data['agenzia_id'] = $_SESSION['AUTH']['AGENZIA'];
        //TRACE and $this->trace(LOG_DEBUG, TRACE_DEFAULT, __FUNCTION__, 'DATA', print_r($data,true));
        try {

            $result = $this->members->insert(null,$data,true, Repository::FETCH_ARRAY);

            return $Res
                ->set('success', true)
                ->set('data',$result)
                ->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * Remove enrolled user
     *
     * @routing(method="DELETE", pattern="/remove/<userId>")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $userId
     * @throws \metadigit\core\db\orm\Exception
     */
    function removeUserAction(Request $Req, Response $Res, $userId)
    {

        http_response_code(400);
        return $Res
            ->set('success', false)
            ->set('message', 'Locked')
            ->setView('json:');

        if ( $_SESSION['AUTH']['UTYPE'] !== 'AGENTE' ) {
            http_response_code(401);
            return $Res
                ->set('success', false)
                ->set('message', 'Forbidden access')
                ->setView('json:');
        }

        try {

            $result = $this->members->delete([$userId, $_SESSION['AUTH']['AGENZIA']]);

            return $Res
                ->set('success', true)
                ->set('data',$result)
                ->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * Get enrolled users
     *
     * @routing(method="GET", pattern="/enrolled-list")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\db\orm\Exception
     */
    function enrolledUsersListAction(Request $Req, Response $Res)
    {

        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        $criteriaExp = ($paginationData && ! empty($paginationData->criteriaExp)) ? $paginationData->criteriaExp : "";

        try {

            $users = $this->members->fetchAll(
                $paginationData->page,
                $paginationData->pageSize,
                $paginationData->orderExp,
                $criteriaExp,
                Repository::FETCH_ARRAY
            );

            $result['items'] = $users;
            $result['total'] = $this->members->count($criteriaExp);

            return $Res
                ->set('success', true)
                ->set('data',$result)
                ->setView('json:');
        }

        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * @routing(method="GET", pattern="/enrolled-excel")
     *
     * @param Request $Req
     * @param Response $Res
     */
    public function verificationsExcelAction(Request $Req, Response $Res)
    {
        $data = $Req->getGetData();

        $paginationData = $this->makePaginationData($data);

        try {
            $teamMembers = $this->members->fetchAll(
                null,
                null,
                $paginationData->orderExp,
                $paginationData->criteriaExp
            );

            if (count($teamMembers) > 0) {
                $teamMembers = $this->charsetConversion->convert($teamMembers,"UTF-8", "Windows-1252");
            }

            return $Res
                ->set('data', $teamMembers)
                ->set('saveName', 'Lista membri Uniqum_'.strtotime('now'))
                ->setView('file-excel:/team-members');
        } catch (\Exception $ex) {
            print_r($ex->getMessage());
        }
    }

    /**
     * @routing(method="GET", pattern="/enrolled-total")
     *
     * @param Request $Req
     * @param Response $Res
     */
    public function enrolledTotalAction(Request $Req, Response $Res)
    {

        try {
            $teamMembersTotal = $this->members->count(null);

            return $Res
                ->set('success', true)
                ->set('total',$teamMembersTotal)
                ->setView('json:');
        } catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

    /**
     * Check if user is enrolled
     *
     * @routing(method="GET", pattern="/check-enrolled")
     * @param Request $Req
     * @param Response $Res
     * @param Integer $userId
     * @return Response
     */
    function checkUserEnrolledAction(Request $Req, Response $Res, $userId)
    {
        $exists = $this->members->fetch([$_SESSION['AUTH']['UID'], $_SESSION['AUTH']['AGENZIA']]);

        try {
            $exists = $this->members->fetch([$_SESSION['AUTH']['UID'], $_SESSION['AUTH']['AGENZIA']]);

            return $Res
                ->set('success', true)
                ->set('enrolled', !empty($exists))
                ->setView('json:');
        }
        catch (\Exception $ex) {
            return $Res
                ->set('success', false)
                ->set('errorCode', $ex->getMessage())
                ->setView('json:');
        }
    }

}
