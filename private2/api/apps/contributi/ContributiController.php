<?php

namespace api\apps\contributi;

use metadigit\core\Exception;
use metadigit\core\db\orm\Exception as OrmException;
use metadigit\core\db\orm\Repository;
use metadigit\core\http\Request;
use metadigit\core\http\Response;
use Flow\Config;
use Flow\File;

class ContributiController extends \metadigit\core\web\controller\ActionController
{
    const EDITABLE_YEARS = [ 2025, 2024 ];
    const NOTIFY_EMAIL = '<EMAIL>';
    const NOTIFY_EMAIL_DEBUG = '<EMAIL>';

    /** AgenzieRepository
     * @var \metadigit\core\db\orm\Repository */
    protected $AgenzieRepository;
    /** BudgetsManager
     * @var \data\apps\contributi\BudgetsManager */
    protected $BudgetsManager;
    /** ContributiRepository
     * @var \metadigit\core\db\orm\Repository */
    protected $ContributiRepository;
    /** Mailer
     * @var \metadigit\core\mail\Mailer */
    protected $Mailer;
    /** UsersRepository
     * @var \metadigit\core\db\orm\Repository */
    protected $UsersRepository;

    /**
     * @routing(method="POST", pattern="/contributi/<id>/approve")
     * @param Request $Req
     * @param Response $Res
     * @param integer $id Contributo ID
     * @param string $status
     */
    public function approveAction(Request $Req, Response $Res, $id, $status)
    {
        $success = false;
        $data = [];
        switch ($status) {
            case 'OK':
                $data['status'] = 'ACTIVE';
                $data['approvazione'] = 'OK';
                break;
            case 'KO':
                $data['status'] = 'DENIED';
                $data['approvazione'] = 'NO';
                break;
        }
        try {
            $data = $this->ContributiRepository->update($id, $data, true, Repository::FETCH_JSON);
            $success = true;
            $Res->set('data', $data);
        } catch (Exception $Ex) {
            switch ($Ex->getCode()) {
                case 300:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->ContributiRepository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, 'validation error', print_r($Ex->getData(), true));
                    break;
            }
        }
        $Res->set('success', $success)->setView('json:');
    }

    /**
     * @param Request $Req
     * @param Response $Res
     * @param integer $id Contributo ID
     * @param string $flowFilename
     */
    public function attachmentAction(Request $Req, Response $Res, $id, $flowFilename)
    {
        if (!is_dir(\metadigit\core\TMP_DIR.'flowjs-chunks')) {
            mkdir(\metadigit\core\TMP_DIR.'flowjs-chunks');
        }
        $config = new Config();
        $config->setTempDir(\metadigit\core\TMP_DIR.'flowjs-chunks');
        $File = new File($config);

        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            if ($File->checkChunk()) {
                $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs $File->checkChunk() OK');
                http_response_code(200);
            } else {
                $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs $File->checkChunk() NO');
                http_response_code(404);
                return;
            }
        } else {
            if ($File->validateChunk()) {
                $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs $File->saveChunk()');
                $File->saveChunk();
            } else {
                $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs INVALID CHUNCK, retry!');
                // error, invalid chunk upload request, retry
                http_response_code(400);
                return;
            }
        }
        if ($File->validateFile()) {
            // File upload was completed
            $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs: file upload was completed');
            $Contributo = $this->ContributiRepository->fetch($id);
            $dir = DOWNLOAD_DIR.'apps/contributi/'.$Contributo->anno;
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            $ext = strrchr($flowFilename, '.');
            if ($Contributo->allegato) {
                unlink($dir.'/'.$id.$Contributo->allegato);
            }
            $File->save($dir.'/'.$id.$ext);
            $this->ContributiRepository->update($Contributo, ['allegato' => $ext]);
        } else {
            $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'FlowJs: not a final chunk, continue to upload');
            // This is not a final chunk, continue to upload
        }
    }

    /**
     * @param Request $Req
     * @param Response $Res
     */
    function attachment2Action(Request $Req, Response $Res)
    {
        if (!is_dir(\metadigit\core\TMP_DIR.'custom-chunks')) {
            mkdir(\metadigit\core\TMP_DIR.'custom-chunks');
        }
        $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'contributiAttachment: data ', $Req->getRawData());
        $file = $_FILES;

        $data = $Req->getPostData();
        if (empty($data['id'])) {
            echo json_encode(
                [
                    'success' => false,
                    'message' => 'No id provided',
                    'data' => $data,
                ]
            );
            return;
        }
        if (empty($data['anno'])) {
            echo json_encode(
                [
                    'success' => false,
                    'message' => 'No anno provided',
                    'data' => $data,
                ]
            );
            return;
        }
        $id = $data['id'];
        $anno = $data['anno'];
        if (!is_dir(DOWNLOAD_DIR . 'contributi/')) {
            mkdir(DOWNLOAD_DIR . 'contributi/', 0755, true);
        }

        $res = $this->uploadChunking($file, $data, DOWNLOAD_DIR."apps/contributi/$anno/".$id);
        if(!empty($res['completed'])){
            $ext = strrchr($file['chunk']['name'], '.');
            $this->ContributiRepository->update($id, ['allegato' => $ext]);
        }
        $Res->setContentType('application/json');
        $Res->set($res);

        echo json_encode($res);
    }

    function validateChunkParameters($data)
    {
        //const chunkRequest = makeRequest(method, url, { ...data, fileName, chunk, totalChunks, maxChunkSize, currentChunkSize, chunkIndex: i, start, end });
        $validationErrors = [];
        if (empty($data['chunk'])) {
            return $validationErrors['chunk'] = 'required';
        }
        if (empty($data['totalChunks'])) {
            return $validationErrors['totalChunks'] = 'required';
        }
        if (empty($data['chunkIndex'])) {
            return $validationErrors['chunkIndex'] = 'required';
        }
        if (empty($data['fileName'])) {
            return $validationErrors['fileName'] = 'required';
        }
        return $validationErrors;
    }

    function areChunksUploaded($chunks, $totalChunks)
    {
        return count($chunks) == $totalChunks;
    }

    function mergeChunks($chunks, $tmpPath, $finalFilePath)
    {
        sort($chunks, SORT_NATURAL);
        $success = file_put_contents($finalFilePath, '');
        foreach ($chunks as $chunk) {
            $success = file_put_contents($finalFilePath, file_get_contents($chunk), FILE_APPEND);
            if(!$success){
                break;
            }
            unlink($chunk);
        }
        if(!$success){
            $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'Merge failed', $success);
        }

        return $success;
    }

    function uploadChunking($file, $data, $finalPath)
    {
        if (!isset($file['chunk'])) {
            return (
                [
                    'success' => false,
                    'message' => 'No file uploaded',
                    'data' => $data,
                ]
            );
        }
        if (!empty($chunkValidation)) {
            return (
                [
                    'success' => false,
                    'message' => 'Chunk validation failed',
                    'data' => $data,
                    'errors' => $chunkValidation
                ]
            );
        }
        $filename = $data['fileName'];
        $chunkIndex = $data['chunkIndex'];
        $tmpPath = \metadigit\core\TMP_DIR.'custom-chunks';
        $savePath = $tmpPath . $filename . '.part.' . $chunkIndex;

        if (!is_dir($tmpPath)) {
            mkdir($tmpPath, 0755, true);
        }
        $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'TmpPath: ' . $tmpPath . ' SavePath: ' . $savePath);
        $moved = move_uploaded_file($file['chunk']['tmp_name'], $savePath);

        if (!$moved) {
            $this->trace(LOG_DEBUG, 1, __FUNCTION__, 'Chunk upload failed[move_uploaded_file]: '. $file['chunk']['tmp_name'] . " to " . $savePath);
            return (
                [
                    'success' => false,
                    'message' => 'Chunk upload failed',
                    'data' => $data,
                    'errors' => ['file' => 'move_uploaded_file failed']
                ]
            );
        }

        $chunks = glob($tmpPath . $filename . '.part.*');

        if(!$this->areChunksUploaded($chunks, $data['totalChunks'])) {
            return (
                [
                    'success' => true,
                    'message' => 'Chunk uploaded',
                    'data' => $data,
                ]
            );
        }
        if(!$this->mergeChunks($chunks, $tmpPath, $finalPath)){
            return (
                [
                    'success' => false,
                    'message' => 'Chunk merge failed',
                    'data' => $data,
                    'errors' => ['file' => 'merge failed']
                ]
            );
        };
        return (
            [
                'completed' => true,
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => $data,
            ]
        );
    }

    /**
     * CREATE action (HTTP POST)
     * @routing(method="POST", pattern="/contributi")
     * @param Request $Req
     * @param Response $Res
     * @param string|null $subset
     */
    public function createAction(Request $Req, Response $Res, $subset = null)
    {
        $success = false;
        $data = (array) json_decode($Req->getRawData());
        $data['data'] = date('Y-m-d');
        try {
            $emailNotify = false;
            switch ($_SESSION['AUTH']['UTYPE']) {
                case 'KA':
                case 'AMMINISTRATORE':
                    $data['status'] = 'ACTIVE';
                    if ($data['budget'] == 'DIREZ') {
                        $data['approvazione'] = 'DIREZ';
                    }
                    break;
                case 'AREAMGR':
                case 'DISTRICTMGR':
                    if ($data['budget'] == 'DIREZ') {
                        $data['status'] = 'PENDING';
                        $data['approvazione'] = 'PENDING';
                        $emailNotify = true;
                    }
                    break;
            }
            $Contributo = $this->ContributiRepository->create($data);
            if (!$this->BudgetsManager->checkBudgetAvailability($Contributo)) {
                throw new OrmException(500, 'BUDGET AVAILABILITY CHECK', ['importo' => 'BUDGET_ESAURITO']);
            }
            $data = $this->ContributiRepository->insert(null, $data, true, Repository::FETCH_JSON, $subset);

            if ($emailNotify) { // mail notification
                $body = file_get_contents(__DIR__.'/tpl/notify-email.txt');
                $body = str_replace('[anno]', $data['anno'], $body);
                $body = str_replace('[agenzia_id]', $data['agenzia_id'], $body);
                $body = str_replace('[importo]', $data['importo'], $body);
                $body = str_replace('[titolo]', $data['titolo'], $body);
                $Message = $this->Mailer->newMessage();
                $Message->setFrom('<EMAIL>', 'PortaleAgendo')
                    ->setReplyTo('<EMAIL>')
                    ->setReturnPath('<EMAIL>')
                    ->setSubject('PortaleAgendo - richiesta contributo budget DIREZ')
                    ->setBody($body);
                switch (\metadigit\core\ENVIRONMENT) {
                    case 'PROD': $Message->setTo(self::NOTIFY_EMAIL);
                        break;
                    default: $Message->setTo(self::NOTIFY_EMAIL_DEBUG);
                }
                $this->Mailer->send($Message);
            }
            $success = true;
            $Res->set('data', $data);
        } catch (OrmException $Ex) {
            switch ($Ex->getCode()) {
                case 100:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->ContributiRepository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_ERR, TRACE_DEFAULT, __FUNCTION__, 'validation error', print_r($Ex->getData(), true));
                    break;
            }
        }
        $Res->set('success', $success)->setView('json:');
    }

    /**
     * DELETE action (HTTP DELETE)
     * @routing(method="DELETE", pattern="/contributi/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param integer $id Contributo id
     * @param string|null $subset
     */
    public function deleteAction(Request $Req, Response $Res, $id, $subset = null)
    {
        $success = false;
        $data['status'] = 'DELETED';
        try {
            $Contributo = $this->ContributiRepository->fetch($id);
            if ($Contributo->status == 'ACTIVE' && !in_array($_SESSION['AUTH']['UTYPE'], ['KA','AMMINISTRATORE'])) {
                throw new Exception(500, 'ACL: operation denied');
            }
            if ($data = $this->ContributiRepository->update($id, $data, false, Repository::FETCH_JSON, $subset)) {
                $success = true;
                $Res->set('data', $data);
            } else {
                $Res->set('errCode', 'UNKNOWN_ENTITY');
            }
        } catch (Exception $Ex) {
            http_response_code(500);
            $Ex->trace();
            trigger_error(get_class($this->ContributiRepository).' EXCEPTION', E_USER_ERROR);
        }
        $Res->set('success', $success)->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/contributi/<id>/download")
     * @param Request $Req
     * @param Response $Res
     * @param integer $id Contributo id
     */
    public function downloadAction(Request $Req, Response $Res, $id)
    {
        $Contributo = $this->ContributiRepository->fetch($id);
        $Res->set('saveName', 'Contributo-'.date('Y-m-d', strtotime($Contributo->data)).'-'.$Contributo->agenzia_id)
            ->setView('xsendfile:/apps/contributi/'.$Contributo->anno.'/'.$Contributo->id.$Contributo->allegato);
    }

    /**
     * @routing(method="GET", pattern="/contributi/export.csv")
     * @param Request  $Req
     * @param Response $Res
     * @param integer  $year
     */
    public function exportCsvAction(Request $Req, Response $Res, $year)
    {
        $Contributi = $this->ContributiRepository->fetchAll(1, null, 'data.ASC', 'anno,EQ,'.$year);
        $arrayContributi = $this->ContributiRepository->toArray($Contributi);

        $escapedData = array_map(function ($row) {
            return array_map(function ($field) {
                $field = (string) $field;
                return str_replace('"', '""', $field);
            }, $row);
        }, $arrayContributi);

        $Res->set('data', $escapedData)
            ->set('saveName', 'contributi-'.$year)
            ->setView('file-csv:/contributi');
    }

    /**
     * @routing(method="GET", pattern="/contributi/export.xls")
     * @param Request  $Req
     * @param Response $Res
     * @param integer  $year
     */
    public function exportXlsAction(Request $Req, Response $Res, $year)
    {
        $Contributi = $this->ContributiRepository->fetchAll(1, null, 'data.ASC', 'anno,EQ,'.$year);
        $Res->set('data', $this->ContributiRepository->toArray($Contributi))
            ->set('saveName', 'contributi-'.$year)
            ->setView('file-excel:/contributi');
    }

    /**
     * @routing(method="GET", pattern="/contributi/<id>/print")
     * @param Request $Req
     * @param Response $Res
     * @param integer $id Contributo ID
     * @param boolean $av logo AV
     */
    public function printAction(Request $Req, Response $Res, $id, $av = false)
    {
        try {
            $Contributo = $this->ContributiRepository->fetch($id);
            $Agenzia = $this->AgenzieRepository->fetch($Contributo->agenzia_id);
            // NOTE: orderExp 'id.DESC' per AREA 1 "NordOvest" con 2 AM attivi, il secondo è quello buono
            $AreaManager = $this->UsersRepository->fetchOne(null, 'id.DESC', 'type,EQ,AREAMGR|active,EQ,1|area,'.$Agenzia->area);
            $DistrictManager = $this->UsersRepository->fetchOne(null, 'id.DESC', 'type,EQ,DISTRICTMGR|active,EQ,1|district,' . $Agenzia->district);
            $Res->set('Contributo', $Contributo)
                ->set('Agenzia', $Agenzia)
                ->set('AreaManager', $AreaManager)
                ->set('DistrictManager', $DistrictManager)
                ->set('logoAV', $av)
                ->setView('php:/contributo.pdf');
            $Res->setContentType('application/pdf');
        } catch (Exception $Ex) {
            $Ex->trace();
            http_response_code(500);
            $Res->set('success', false)->setView('json:');
        }
    }

    /**
     * READ action (HTTP GET)
     * @routing(method="GET", pattern="/contributi")
     * @param Request $Req
     * @param Response $Res
     * @param string $criteriaExp
     * @param string $orderExp
     * @param integer $page
     * @param integer $pageSize
     * @param string|null $subset
     */
    public function readAllAction(Request $Req, Response $Res, $criteriaExp = null, $orderExp = null, $page, $pageSize, $subset = null)
    {
        $success = false;
        try {
            $data = $this->ContributiRepository->fetchAll($page, $pageSize, $orderExp, $criteriaExp, Repository::FETCH_JSON, $subset);
            $total = $this->ContributiRepository->count($criteriaExp);
            $success = true;
            $Res->set('total', $total)
                ->set('data', $data);
        } catch (Exception $Ex) {
            http_response_code(500);
            $Ex->trace();
            trigger_error(get_class($this->ContributiRepository).' EXCEPTION', E_USER_ERROR);
        }
        $Res->set('success', $success)->setView('json:');
    }

    /**
     * READ action (HTTP GET)
     * @routing(method="GET", pattern="/contributi/<id>")
     * @param Request  $Req
     * @param Response $Res
     * @param integer $id Contributo id
     * @param string|null $subset
     */
    public function readAction(Request $Req, Response $Res, $id, $subset = null)
    {
        $success = false;
        try {
            if ($data = $this->ContributiRepository->fetch($id, Repository::FETCH_JSON, $subset)) {
                $success = true;
                $Res->set('data', $data);
            }
        } catch (Exception $Ex) {
            http_response_code(500);
            $Ex->trace();
            trigger_error(get_class($this->ContributiRepository).' EXCEPTION', E_USER_ERROR);
        }
        $Res->set('success', $success)->setView('json:');
    }

    /**
     * UPDATE action (HTTP PUT)
     * @routing(method="PUT", pattern="/contributi/<id>")
     * @param Request $Req
     * @param Response $Res
     * @param integer $id Contributo id
     * @param string|null $subset
     */
    public function updateAction(Request $Req, Response $Res, $id, $subset = null)
    {
        $success = false;
        $data = (array) json_decode($Req->getRawData());
        try {
            /** @var \data\apps\contributi\Contributo $Contributo */
            $Contributo = $this->ContributiRepository->fetch($id);
            if (!in_array($Contributo->anno, self::EDITABLE_YEARS)) {
                $Res->set('growl-messages', [['type' => 'error', 'icon' => 'icon-warning', 'text' => 'Modifica annata '.$Contributo->anno.' disabilitata']]);
                throw new OrmException();
            }
            // update date on DRAFT => ACTIVE
            if ($Contributo->status == 'DRAFT' && $data['status'] == 'ACTIVE') {
                $data['data'] = date('Y-m-d');
            }
            $Contributo($data);
            if (!$this->BudgetsManager->checkBudgetAvailability($Contributo)) {
                throw new OrmException(500, 'BUDGET AVAILABILITY CHECK', ['importo' => 'BUDGET_ESAURITO']);
            }
            $data = $this->ContributiRepository->update($id, $data, true, Repository::FETCH_JSON, $subset);
            $success = true;
            $Res->set('data', $data);
        } catch (OrmException $Ex) {
            switch ($Ex->getCode()) {
                case 300:
                    http_response_code(500);
                    $Ex->trace();
                    trigger_error(get_class($this->ContributiRepository).' EXCEPTION', E_USER_ERROR);
                    break;
                case 500:
                    http_response_code(400);
                    $Res->set('errors', $Ex->getData());
                    TRACE and $this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, 'validation error', print_r($Ex->getData(), true));
                    break;
            }
        }
        $Res->set('success', $success)->setView('json:');
    }
}
