<?php

namespace api\apps\download;

use metadigit\core\CoreTrait;
use metadigit\core\http\Request;
use metadigit\core\http\Response;


class DownloadController extends  \metadigit\core\web\controller\ActionController
{

    use CoreTrait;

    function indexAction(Request $Req, Response $Res)
    {
        $Res->set('data', [])->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/list")
     * @param Request  $Req
     * @param Response $Res
     */
    function listAction(Request $Req, Response $Res)
    {
        $Res->set('data', $this->getList())->setView('json:');
        $Res->set('success', true);
    }

    protected function isAllowedWelfare()
    {
        return \util\WelfareConfig::isCurrentUserAllowedWelfare();
    }


    protected function getWelfareNodes($icoDir, $i = 0)
    {
        if (!$this->isAllowedWelfare()) {
            return [];
        }

        return [
            DownloadNode::stdDir(
                $i++,
                "Welfare Aziendale",
                "Materiale per le Agenzie",
                $icoDir . "welfareaziendale.png"
            )

                // 1) IL WELFARE AZIENDALE
                ->addEntity(
                    DownloadNode::stdDir($i++, "Il Welfare Aziendale")
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Il Welfare Aziendale",
                            "docs/welfare-aziendale/1.Il_welfare_aziendale/Il_Welfare_Aziendale.pptx"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Simulatore del Risparmio Fiscale (Definitivo) 2024",
                            "docs/welfare-aziendale/1.Il_welfare_aziendale/Simulatore_del_risparmio_fiscale_(definitivo)_2024.xlsx"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Raccolta Dati Dipendenti",
                            "docs/welfare-aziendale/1.Il_welfare_aziendale/Raccolta_dati_Dipendenti.xlsx"
                        ))
                )

                // 2) FORMAZIONE
                ->addEntity(
                    DownloadNode::stdDir($i++, "Formazione")
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "PDF Navigabile 2024",
                            "docs/welfare-aziendale/2.Formazione/Groupama_Welfare04.pdf"
                        ))
                )

                // 3) COMUNICAZIONE
                ->addEntity(
                    DownloadNode::stdDir($i++, "Comunicazione")
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Brochure Welfare - Clienti e Prospect",
                            "docs/welfare-aziendale/3.Comunicazione/Brochure-Welfare_CLIENTI-E-PROSPECT.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Video Welfare limiti di deducibilità",
                            "docs/welfare-aziendale/3.Comunicazione/Welfare_limiti_deducibilità.mp4" //WARN: Missing
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Post tematici benessere impresa",
                            "docs/welfare-aziendale/3.Comunicazione/Groupama_post_Welfare.pptx" //WARN: Missing
                        ))
                )

                // 4) LE SOLUZIONI
                ->addEntity(
                    DownloadNode::stdDir($i++, "Le Soluzioni")
                        // Benessere Impresa
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Benessere Impresa - Argomentario Benessere Impresa Definitivo",
                            "docs/welfare-aziendale/4.Le_soluzioni/Benessere_Impresa/Argomentario_Benessere_Impresa_definitivo.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Benessere Impresa - Circolare Benessere Impresa",
                            "docs/welfare-aziendale/4.Le_soluzioni/Benessere_Impresa/Circolare_Benessere_Impresa.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Benessere Impresa - Questionario Sanitario",
                            "docs/welfare-aziendale/4.Le_soluzioni/Benessere_Impresa/Questionario_sanitario_Benessere_Impresa.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Benessere Impresa - Set Informativo",
                            "docs/welfare-aziendale/4.Le_soluzioni/Benessere_Impresa/Set_Informativo_Benessere_Impresa.pdf"
                        ))

                        // Infortuni Collettività
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Infortuni Collettività - Questionario Sanitario",
                            "docs/welfare-aziendale/4.Le_soluzioni/Infortuni_Collettività/Questionario_Sanitario_Protezione_Infortuni_Collettivita.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Infortuni Collettività - Set Informativo",
                            "docs/welfare-aziendale/4.Le_soluzioni/Infortuni_Collettività/Set_Informativo_Infortuni_collettività.pdf"
                        ))

                        // Menteserena Collettività
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Menteserena Collettività - Aggiornamento Criteri Assuntivi",
                            "docs/welfare-aziendale/4.Le_soluzioni/Menteserena_Collettività/Aggiornamento_criteri_assuntivi_MenteSerena_Collettività.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Menteserena Collettività - Circolare",
                            "docs/welfare-aziendale/4.Le_soluzioni/Menteserena_Collettività/Circolare_Menteserena_Collettività.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Menteserena Collettività - Scheda di Adesione",
                            "docs/welfare-aziendale/4.Le_soluzioni/Menteserena_Collettività/Scheda_di_Adesione_Menteserena.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Menteserena Collettività - Set Informativo",
                            "docs/welfare-aziendale/4.Le_soluzioni/Menteserena_Collettività/Set_Informativo_Menteserena_Collettività.pdf"
                        ))

                        // Programma Open
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Programma Open - Manuale Operativo",
                            "docs/welfare-aziendale/4.Le_soluzioni/Programma_Open/Manuale_Operativo_Programma_Open_ed_202211.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Programma Open - Nota Informativa",
                            "docs/welfare-aziendale/4.Le_soluzioni/Programma_Open/Nota_Informativa_Programma_Open_ed._2024.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Programma Open - Circolare",
                            "docs/welfare-aziendale/4.Le_soluzioni/Programma_Open/Programma_OPEN_Circolare_2023_54_DIR_VITA_Restyling_e_modifiche_al_Regolamento.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Programma Open - Modulo Adesione",
                            "docs/welfare-aziendale/4.Le_soluzioni/Programma_Open/Programma_Open_-_Modulo_adesione.pdf"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Programma Open - Regolamento",
                            "docs/welfare-aziendale/4.Le_soluzioni/Programma_Open/Programma-Open-Regolamento.pdf"
                        ))
                )

                // 5) LA CASSA DI ASSISTENZA
                ->addEntity(
                    DownloadNode::stdDir($i++, "La Cassa di Assistenza")
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Bozza Regolamento",
                            "docs/welfare-aziendale/5.La_cassa_di_assistenza/Bozza_esemplificativa_Regolamento_BISALUS.doc"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Bozza delibera Assembleare",
                            "docs/welfare-aziendale/5.La_cassa_di_assistenza/Bozza_esemplificativa_verbale_e_regolamento_Amministratori_BISALUS.docx"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Quote Associative",
                            "docs/welfare-aziendale/5.La_cassa_di_assistenza/BiSalus_Quote_Associative.pdf"

                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Esempio Costo Piano",
                            "docs/welfare-aziendale/5.La_cassa_di_assistenza/Esempio_Costo_Piano.docx"
                        ))
                        ->addEntity(DownloadNode::stdFile(
                            $i++,
                            "Anagrafica Cassa",
                            "docs/welfare-aziendale/5.La_cassa_di_assistenza/Anagrafica_cassa.pptx"
                        ))
                )
        ];
    }

    /**
     *
     * @return array<DownloadFolder>
     */
    protected function getList()
    {
        $i = 1;
        $icoDir = "/assets/downloadIcons/";
        /** @var DownloadNode[] $welfareNodes */
        $welfareNodes = $this->getWelfareNodes($icoDir, $i);

        $list = [

            DownloadNode::stdDir(
                $i++,
                "Cofidis",
                "Materiali",
                $icoDir . "cofidis.png"
            )
                ->addEntity(DownloadNode::stdFile(
                    $i++,
                    "Circular commerciale partnership Cofidis v2.0",
                    "docs/cofidis/circolare-commerciale-partnership-cofidis-v2.0.pdf"
                ))
                ->addEntity(DownloadNode::stdFile(
                    $i++,
                    "Guida PagoCredit Groupama",
                    "docs/cofidis/guida-pagocredit-groupama.pdf"
                ))
                ->addEntity(DownloadNode::stdFile(
                    $i++,
                    "Come fare una transazione PagoCREDIT su Smartpos_Verticale",
                    "docs/cofidis/cofidis.mp4"
                ))

        ];

        $list = array_merge($list, $welfareNodes);

        $list = array_merge(
            $list,
            [
                DownloadNode::stdFile(
                    $i++,
                    "CRM by SalesForce",
                    "docs/guida-salesforce-agenzia.pdf",
                    "Guida utenti di agenzia: CRM - Pagina Cliente",
                    $icoDir . "salesforce.png"
                ),

                DownloadNode::stdDir(
                    $i++,
                    "Life Pro",
                    "Materiali",
                    $icoDir . "lifepro.png"
                )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Informativa su rilasci in produzione"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Rilascio 24 giugno",
                                "docs/life-pro/20210624-nuovo-rilascio-lifepro.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Rilascio 19 luglio",
                                "docs/life-pro/20210719-nuovo-rilascio-lifepro.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Rilascio 11 ottobre",
                                "docs/life-pro/20211011-nuovo-rilascio-lifepro.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Rilascio 22 febbraio",
                                "docs/life-pro/20220214_nuovo-rilascio-lifre-pro.pdf"
                            ))
                    )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Manuale Life Pro"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Manuale Febbraio 2022",
                                "docs/life-pro/manuale-life-pro-febbraio-2022.pdf"
                            ))
                    )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Materiali percorso formativo Life Pro"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Mercati Finanziari Ed. 09 2021",
                                "docs/life-pro/materiali-1-mercati-finanziari-ed-092021.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Groupama Progetto Attivo Ed. 10 2021",
                                "docs/life-pro/materiali-2-groupama-progetto-attivo-ed102021.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Piattaforma LifePro",
                                "docs/life-pro/materiali-3-piattaforma-lifepro.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Corso Commerciale GPA 1 modulo Ed. 04 2021",
                                "docs/life-pro/materiali-4-corso-commerciale-gpa-1modulo-ed042021.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Corso Commerciale GPA 2 modulo Ed. 04 2021",
                                "docs/life-pro/materiali-5-corso-commerciale-gpa-2modulo-ed042021.pdf"
                            ))
                    )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Supporti Commerciali Groupama Progetto Attivo"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "DEM Progetto Attivo",
                                "docs/life-pro/supporti-dem-progetto-attivo.png"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Depliant Groupama Progetto Attivo Ed. 04 2021",
                                "docs/life-pro/supporti-depliant-progetto-attivo-ed042021.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Guida Tecnica Groupama Progetto Attivo Ed. 07 2021",
                                "docs/life-pro/supporti-guida-tecnica-progetto-attivo-ed072021.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Video Groupama Progetto Attivo",
                                "docs/life-pro/video-groupama-progetto-attivo.mp4"
                            ))
                    ),

                DownloadNode::stdFile(
                    $i++,
                    "Nuovo Portale Formazione",
                    "docs/formazione/Manuale_App_Formazione_AGENTI.pdf",
                    "Manuale Operativo",
                    $icoDir . "nuovoportaleformazione.png"
                ),

                DownloadNode::stdFile(
                    $i++,
                    "Focus Cliente",
                    "docs/ManualeFocusCliente.pdf",
                    "Manuale",
                    $icoDir . "focus.png"
                ),

                DownloadNode::stdDir(
                    $i++,
                    "Compass",
                    "Materiali",
                    $icoDir . "compass.png"
                )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Materiale Pubblicitario"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Locandina",
                                "docs/compass/4710_50x70_Locandina_Groupama_DMp5127.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Flyer",
                                "docs/compass/4710_100x210_Flyer_Groupama_DMp5130.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Quartino",
                                "docs/compass/4710_100x210_Quartino_Groupama_DMp5129.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Espositore",
                                "docs/compass/4710_240x340_Espositore_Groupama_DMp5128.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Tasche espositore",
                                "docs/compass/4710_Tasche_Espositore_Groupama.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Slide fissa appuntamento per prestiti",
                                "docs/compass/slide_fissa_appuntamento_per_prestiti_personali_agenti.pdf"
                            ))
                    )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Modulistica"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Form per richiesta convenzione Compass",
                                "docs/compass/FORM_RICHIESTA_CONVENZIONE_COMPASS.xlsx"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Proforma di fattura Compass",
                                "docs/compass/PROFORMA_DI_FATTURA_COMPASS_SPA.docx"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Referenti filiali",
                                "docs/compass/RECAPITI_DIRETTI_FILIALI_COMPASS_8_LUG_25.xlsx"
                            ))
                    )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Prodotti"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Prodotti Compass",
                                "docs/compass/Prodotti_Compass_Groupama_maggio_24.pdf"
                            ))
                    )
                    ->addEntity(
                        DownloadNode::stdDir(
                            $i++,
                            "Archivio Prodotti"
                        )
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Prodotti Compass (luglio 2023)",
                                "docs/compass/Prodotti_Compass_Groupama_luglio_23.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Prodotti Compass (maggio 2023)",
                                "docs/compass/Prodotti_Compass_Groupama_maggio_23.pdf"
                            ))
                            ->addEntity(DownloadNode::stdFile(
                                $i++,
                                "Prodotti Compass (marzo 2023)",
                                "docs/compass/Prodotti_Compass_Groupama_marzo_23.pdf"
                            ))
                    ),

            ]
        );

        return $list;
    }
}
