<?php namespace api\apps\incentive\garaWelfare2025;

use api\apps\incentive\newProtection2025\NewProtection2025;
use api\apps\incentive\supernova2024\Exception;
use api\apps\incentive\supernova2024\json;
use metadigit\core\db\orm\Repository;
use metadigit\core\db\PdoTrait;
use metadigit\core\http\Request;
use metadigit\core\http\Response;

class FrontendController extends \metadigit\core\web\controller\ActionController {

    use PdoTrait;

    /**
     * @var Repository
     */
    protected $data;

    /**
     * @var Repository
     */
    protected $status;

    /**
     * @var Repository
     */
    protected $incentive;



    /**
     * Return global ranking.
     *
     * @routing(method="GET", pattern="/ranking")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function statusAction(Request $Req, Response $Res)
    {

        if (! \util\WelfareConfig::isCurrentUserAllowedWelfare()) {
            return http_response_code(401);
        }

        $ranking = $this->status->fetchAll(
            1,
            13,
            "premiTot.DESC|pezzi.DESC",
            "premiTot,GT,0",
            \PDO::FETCH_ASSOC
        );

        // Reorder ranking: agencies with at least 2 pezzi AND at least 10000 premiTot come first
        $qualifyingAgencies = [];
        $nonQualifyingAgencies = [];

        foreach ($ranking as $agency) {
            if ($agency['pezzi'] >= 2 && $agency['premiTot'] >= 10000) {
                $qualifyingAgencies[] = $agency;
            } else {
                $nonQualifyingAgencies[] = $agency;
            }
        }

        // Merge arrays with qualifying agencies first
        $data['ranking'] = array_merge($qualifyingAgencies, $nonQualifyingAgencies);
        $data['agencyRanking'] = $this->findAgencyPlacement();
        $data['status'] = $this->status->fetchOne(null, null, "agenzia_id,EQ," . $_SESSION['AUTH']['AGENZIA'], \PDO::FETCH_ASSOC);

        return $Res->set('data', $data)->setView('json:');
    }

    /**
     * Return incentive last update.
     *
     * @routing(method="GET", pattern="/last-update")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function lastUpdateAction(Request $Req, Response $Res)
    {

        $data = $this->incentive->fetch(
            339,
            \PDO::FETCH_ASSOC
        );

        return $Res->set('data', $data)->setView('json:');
    }

    /**
     * @routing(method="GET", pattern="/pdf")
     * @param Request $Req
     * @param Response $Res
     *
     * @return json
     */
    public function pdfAction(Request $Req, Response $Res)
    {
        if ($_SESSION['AUTH']['UTYPE'] == 'INTERMEDIARIO') {
            return $Res->set('success', false)->setView('json:');
        }

        $Res->setView('xsendfile:/gara-welfare-2025/regolamento.pdf');
    }

    /**
     * Get Agencies status list
     * @routing(pattern="/excel-detail")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\context\ContextException
     */
    public function agenciesDetailAction(Request $Req, Response $Res)
    {

        if (! in_array($_SESSION['AUTH']['UTYPE'], ['KA', 'AMMINISTRATORE', 'AGENTE', 'AREAMGR', 'DISTRICTMGR'])) {
            return http_response_code(401);
        }

        try {

            $data = $this->status->fetchAll(null, null, null, null, Repository::FETCH_ARRAY);

            return $Res
                ->set('data', $data)
                ->set('saveName', 'Gara Welfare 2025 - Dettaglio Agenzie')
                ->setView('file-excel:/agencies-detail');
        } catch(Exception $Ex) {
            http_response_code(500);
            TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
        }

    }

    /**
     * Get Agencies status list
     * @routing(pattern="/policies-detail")
     * @param Request $Req
     * @param Response $Res
     * @throws \metadigit\core\context\ContextException
     */
    public function policiesDetailAction(Request $Req, Response $Res)
    {
        if ($_SESSION['AUTH']['UTYPE'] !== 'AGENTE') {
            return http_response_code(401);
        }

        try {
            // Get all policies for the agency
            $data = $this->data->fetchAll(null, null, null, "agenzia_id,EQ," . $_SESSION['AUTH']['AGENZIA'], Repository::FETCH_ARRAY);

            return $Res
                ->set('data', $data)
                ->set('saveName', 'Gara Welfare 2025 - Dettaglio polizze')
                ->setView('file-excel:/policies-detail');
        } catch(Exception $Ex) {
            http_response_code(500);
            TRACE and $this->trace(LOG_ERR, 0, __FUNCTION__, $Ex->getMessage(), $Ex->getMessage());
        }
    }

    /**
     * Find agency placement in global ranking
     * @param string $slot
     * @return integer
     */
    protected function findAgencyPlacement()
    {
        $agencyRanking = 0;
        $agenzia_id = $_SESSION['AUTH']['AGENZIA'];

        $ranking = $this->status->fetchAll(
            null,
            null,
            "premiTot.DESC|pezzi.DESC",
            null,
            \PDO::FETCH_ASSOC
        );

        if (! $ranking) {
            return $agencyRanking;
        }

        // Apply the same reordering logic as in statusAction
        $qualifyingAgencies = [];
        $nonQualifyingAgencies = [];

        foreach ($ranking as $agency) {
            if ($agency['pezzi'] >= 2 && $agency['premiTot'] >= 10000) {
                $qualifyingAgencies[] = $agency;
            } else {
                $nonQualifyingAgencies[] = $agency;
            }
        }

        // Merge arrays with qualifying agencies first
        $reorderedRanking = array_merge($qualifyingAgencies, $nonQualifyingAgencies);

        foreach ($reorderedRanking as $key => $object) {
            if ( $object['agenzia_id'] === $agenzia_id ) {
                $agencyRanking = $key;
            }
        }

        return (int)$agencyRanking + 1;
    }

}
