<?xml version="1.0" encoding="UTF-8"?>
<context namespace="api.apps.incentive.garaWelfare2025">
	<includes>
		<include namespace="data"/>
		<include namespace="data.incentive"/>
		<include namespace="data.incentive.garaWelfare2025"/>
		<include namespace="service"/>
		<include namespace="system"/>
	</includes>
	<objects>
		<!-- Dispatcher -->
		<object id="api.apps.incentive.garaWelfare2025.Dispatcher" class="metadigit\core\web\Dispatcher">
			<properties>
				<property name="defaultViewEngine">json</property>
				<property name="routes" type="array">
					<item key="/backend*">api.apps.incentive.garaWelfare2025.BackendController</item>
					<item key="/*">api.apps.incentive.garaWelfare2025.FrontendController</item>
				</property>
				<property name="resourcesDir">${BASE_DIR}api/apps/incentive/garaWelfare2025/tpl/</property>
			</properties>
		</object>

        <object id="api.apps.incentive.garaWelfare2025.GaraWelfare2025" class="api\apps\incentive\garaWelfare2025\GaraWelfare2025">
            <properties>
                <property name="static" type="object">data.incentive.garaWelfare2025.StaticRepository</property>
                <property name="data" type="object">data.incentive.garaWelfare2025.DataRepository</property>
                <property name="status" type="object">data.incentive.garaWelfare2025.StatusRepository</property>
            </properties>
        </object>

		<object id="api.apps.incentive.garaWelfare2025.FrontendController" class="api\apps\incentive\garaWelfare2025\FrontendController">
			<properties>
				<property name="incentive" type="object">data.incentive.IncentiveRepository</property>
				<property name="data" type="object">data.incentive.garaWelfare2025.DataRepository</property>
				<property name="status" type="object">data.incentive.garaWelfare2025.StatusRepository</property>
			</properties>
		</object>

		<!--<object id="api.apps.incentive.garaWelfare2025.BackendController" class="api\apps\incentive\garaWelfare2025\BackendController">
			<properties>
				<property name="data" type="object">data.incentive.garaWelfare2025.DataRepository</property>
				<property name="status" type="object">data.incentive.garaWelfare2025.StatusRepository</property>
			</properties>
		</object>-->

	</objects>

	<events>
		<event name="dispatcher:controller">
			<listeners>
				<listener>service.AuthService->fixture</listener>
				<listener>system.SessionManager->start</listener>
				<listener>service.AuthService->checkAuth</listener>
				<listener>service.AuthService->checkAreaMngDep</listener>
			</listeners>
		</event>
		<event name="dispatcher:view">
			<listeners>
				<listener>system.SessionManager->end</listener>
			</listeners>
		</event>
	</events>
</context>
