<?php

namespace api\apps\incentive\garaWelfare2025;

use api\apps\incentive\AbstractIncentive;
use api\apps\incentive\focus2024\obj;
use api\apps\incentive\IncentiveLifeInterface;
use metadigit\core\CoreTrait;
use metadigit\core\db\orm\Repository;
use metadigit\core\db\PdoTrait;
use metadigit\lib\batch\BatchTrait;

class GaraWelfare2025 implements IncentiveLifeInterface
{
    use CoreTrait, PdoTrait, BatchTrait;

    /**
     * @var Repository
     */
    protected $data;

    /**
     * @var Repository
     */
    protected $static;

    /**
     * @var Repository
     */
    protected $status;

    /**
     * @var Repository
     */
    protected $clients;

    public function getIds()
    {
        return [999];
    }

    /**
     * Convert agency code and id to formatted string
     *
     * @param $code
     * @param $id
     * @return string
     */
    protected function convertAgency($code, $id)
    {
        return $code . str_pad($id, 3, "0", STR_PAD_LEFT);
    }

    public function parseDataRow(array $row, $incentiveId)
    {
        return false;
    }

    public function getDataInitStatement()
    {
        return false;
    }

    public function getDataStatement()
    {
        return false;
    }

    public function getStaticPath()
    {
        return false;
    }

    public function parseStaticRow(array $row, $incentiveId)
    {
        return false;
    }

    public function getStaticInitStatement()
    {
        return false;
    }

    public function getStaticStatement()
    {
        return false;
    }

    /**
     * Post data insert event handler.
     *
     * @param obj $parsed[id, date]
     *
     * @return mixed
     */
    public function postInsert($parsed)
    {
        $this->status->deleteAll(null);

        $static = $this->pdoQuery("SELECT * FROM inc_gara_welfare_2025_static WHERE active = 1")->fetchAll(\PDO::FETCH_ASSOC);

        foreach ($static as $row) {
            $agenzia =  $this->pdoQuery("
            SELECT agenzie.id
            FROM agenzie
            WHERE id = '" . $row["agenzia_id"] . "'
            ")->fetch();
            if (!$agenzia) {
                //$this->trace(LOG_DEBUG, TRACE_ERROR, __FUNCTION__, "AGENCY NOT FOUND : " . $row["agenzia_id"]);
                $this->log("Agenzia non trovata: " . $row["agenzia_id"]);
                continue;
            }

            $this->status->insert(null, $this->calculateStatus('999', $row["agenzia_id"], $row));
        }
    }

    public function calculateStatus($initiativeId, $agencyId, $static)
    {
        $status = [
            "agenzia_id" => $agencyId,
            "pezzi" => 0,
            "premiTot" => 0,
        ];

        $policies = $this->pdoQuery("SELECT * FROM inc_gara_welfare_2025_data WHERE agenzia_id = '" . $agencyId . "'")->fetchAll(\PDO::FETCH_ASSOC);
        $status['pezzi'] = count($this->pdoQuery("SELECT * FROM inc_gara_welfare_2025_data WHERE agenzia_id = '" . $agencyId . "' GROUP BY polizzaMadre")->fetchAll(\PDO::FETCH_ASSOC));

        foreach ($policies as $policy) {
            $status['premiTot'] += $policy['premio'];
        }

        return $status;

    }

    public function computeRuleset($parsed, $data)
    {
    }

}
