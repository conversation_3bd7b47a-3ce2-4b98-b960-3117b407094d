@font-face {
  font-family: 'Droid Sans';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/droidsans/v18/SlGVmQWMvZQIdix7AFxXkHNSaA.ttf) format('truetype');
}
@font-face {
  font-family: 'Roboto Condensed';
  font-style: italic;
  font-weight: 300;
  src: url(https://fonts.gstatic.com/s/robotocondensed/v25/ieVg2ZhZI2eCN5jzbjEETS9weq8-19eDpCEobdNc.ttf) format('truetype');
}
@font-face {
  font-family: 'Roboto Condensed';
  font-style: italic;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/robotocondensed/v25/ieVj2ZhZI2eCN5jzbjEETS9weq8-19eLDwM4.ttf) format('truetype');
}
@font-face {
  font-family: 'Roboto Condensed';
  font-style: italic;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/s/robotocondensed/v25/ieVg2ZhZI2eCN5jzbjEETS9weq8-19eDtCYobdNc.ttf) format('truetype');
}
@font-face {
  font-family: 'Roboto Condensed';
  font-style: normal;
  font-weight: 300;
  src: url(https://fonts.gstatic.com/s/robotocondensed/v25/ieVi2ZhZI2eCN5jzbjEETS9weq8-33mZGCQYag.ttf) format('truetype');
}
@font-face {
  font-family: 'Roboto Condensed';
  font-style: normal;
  font-weight: 400;
  src: url(https://fonts.gstatic.com/s/robotocondensed/v25/ieVl2ZhZI2eCN5jzbjEETS9weq8-19K7CA.ttf) format('truetype');
}
@font-face {
  font-family: 'Roboto Condensed';
  font-style: normal;
  font-weight: 700;
  src: url(https://fonts.gstatic.com/s/robotocondensed/v25/ieVi2ZhZI2eCN5jzbjEETS9weq8-32meGCQYag.ttf) format('truetype');
}
/*! normalize.css v2.1.2 | MIT License | git.io/normalize */
/* ==========================================================================
   HTML5 display definitions
   ========================================================================== */
/**
 * Correct `block` display not defined in IE 8/9.
 */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
}
/**
 * Correct `inline-block` display not defined in IE 8/9.
 */
audio,
canvas,
video {
  display: inline-block;
}
/**
 * Prevent modern browsers from displaying `audio` without controls.
 * Remove excess height in iOS 5 devices.
 */
audio:not([controls]) {
  display: none;
  height: 0;
}
/**
 * Address `[hidden]` styling not present in IE 8/9.
 * Hide the `template` element in IE, Safari, and Firefox < 22.
 */
[hidden],
template {
  display: none;
}
/* ==========================================================================
   Base
   ========================================================================== */
/**
 * 1. Set default font family to sans-serif.
 * 2. Prevent iOS text size adjust after orientation change, without disabling
 *    user zoom.
 */
html {
  font-family: sans-serif;
  /* 1 */
  -ms-text-size-adjust: 100%;
  /* 2 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
}
/**
 * Remove default margin.
 */
body {
  margin: 0;
}
/* ==========================================================================
   Links
   ========================================================================== */
/**
 * Remove the gray background color from active links in IE 10.
 */
a {
  background: transparent;
}
/**
 * Address `outline` inconsistency between Chrome and other browsers.
 */
a:focus {
  outline: thin dotted;
}
/**
 * Improve readability when focused and also mouse hovered in all browsers.
 */
a:active,
a:hover {
  outline: 0;
}
/* ==========================================================================
   Typography
   ========================================================================== */
/**
 * Address styling not present in IE 8/9, Safari 5, and Chrome.
 */
abbr[title] {
  border-bottom: 1px dotted;
}
/**
 * Address style set to `bolder` in Firefox 4+, Safari 5, and Chrome.
 */
b,
strong {
  font-weight: bold;
}
/**
 * Address styling not present in Safari 5 and Chrome.
 */
dfn {
  font-style: italic;
}
/**
 * Address differences between Firefox and other browsers.
 */
hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}
/**
 * Address styling not present in IE 8/9.
 */
mark {
  background: #ff0;
  color: #000;
}
/**
 * Correct font family set oddly in Safari 5 and Chrome.
 */
code,
kbd,
pre,
samp {
  font-family: monospace, serif;
  font-size: 1em;
}
/**
 * Improve readability of pre-formatted text in all browsers.
 */
pre {
  white-space: pre-wrap;
}
/**
 * Set consistent quote types.
 */
q {
  quotes: "\201C" "\201D" "\2018" "\2019";
}
/**
 * Address inconsistent and variable font size in all browsers.
 */
small {
  font-size: 80%;
}
/**
 * Prevent `sub` and `sup` affecting `line-height` in all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
/* ==========================================================================
   Embedded content
   ========================================================================== */
/**
 * Remove border when inside `a` element in IE 8/9.
 */
img {
  border: 0;
}
/**
 * Correct overflow displayed oddly in IE 9.
 */
svg:not(:root) {
  overflow: hidden;
}
/* ==========================================================================
   Figures
   ========================================================================== */
/**
 * Address margin not present in IE 8/9 and Safari 5.
 */
figure {
  margin: 0;
}
/* ==========================================================================
   Forms
   ========================================================================== */
/**
 * Define consistent border, margin, and padding.
 */
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
/**
 * 1. Correct `color` not being inherited in IE 8/9.
 * 2. Remove padding so people aren't caught out if they zero out fieldsets.
 */
legend {
  border: 0;
  /* 1 */
  padding: 0;
  /* 2 */
}
/**
 * 1. Correct font family not being inherited in all browsers.
 * 2. Correct font size not being inherited in all browsers.
 * 3. Address margins set differently in Firefox 4+, Safari 5, and Chrome.
 */
button,
input,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 2 */
  margin: 0;
  /* 3 */
}
/**
 * Address Firefox 4+ setting `line-height` on `input` using `!important` in
 * the UA stylesheet.
 */
button,
input {
  line-height: normal;
}
/**
 * Address inconsistent `text-transform` inheritance for `button` and `select`.
 * All other form control elements do not inherit `text-transform` values.
 * Correct `button` style inheritance in Chrome, Safari 5+, and IE 8+.
 * Correct `select` style inheritance in Firefox 4+ and Opera.
 */
button,
select {
  text-transform: none;
}
/**
 * 1. Avoid the WebKit bug in Android 4.0.* where (2) destroys native `audio`
 *    and `video` controls.
 * 2. Correct inability to style clickable `input` types in iOS.
 * 3. Improve usability and consistency of cursor style between image-type
 *    `input` and others.
 */
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  /* 2 */
  cursor: pointer;
  /* 3 */
}
/**
 * Re-set default cursor for disabled elements.
 */
button[disabled],
html input[disabled] {
  cursor: default;
}
/**
 * 1. Address box sizing set to `content-box` in IE 8/9.
 * 2. Remove excess padding in IE 8/9.
 */
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  /* 1 */
  padding: 0;
  /* 2 */
}
/**
 * 1. Address `appearance` set to `searchfield` in Safari 5 and Chrome.
 * 2. Address `box-sizing` set to `border-box` in Safari 5 and Chrome
 *    (include `-moz` to future-proof).
 */
input[type="search"] {
  -webkit-appearance: textfield;
  /* 1 */
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  /* 2 */
  box-sizing: content-box;
}
/**
 * Remove inner padding and search cancel button in Safari 5 and Chrome
 * on OS X.
 */
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
/**
 * Remove inner padding and border in Firefox 4+.
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
/**
 * 1. Remove default vertical scrollbar in IE 8/9.
 * 2. Improve readability and alignment in all browsers.
 */
textarea {
  overflow: auto;
  /* 1 */
  vertical-align: top;
  /* 2 */
}
/* ==========================================================================
   Tables
   ========================================================================== */
/**
 * Remove most spacing between table cells.
 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
  margin: 0;
  font-size: 62.5%;
}
body {
  overflow-x: hidden;
  padding: 0;
  background: #fff url('backgrounds/body.jpg') repeat-y center;
  font-family: 'Roboto Condensed', Verdana, Arial, sans-serif;
  color: #333333;
}
.x-body {
  position: relative;
  padding: 0.6rem 1rem;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0.8rem 0;
  font-family: 'Roboto Condensed', Verdana, Arial, sans-serif;
  color: #333333;
}
h1 {
  font-size: 2.6rem;
}
h2 {
  font-size: 2.34rem;
}
h3 {
  font-size: 2.08rem;
}
h4 {
  font-size: 1.82rem;
}
h5 {
  font-size: 1.56rem;
}
h6 {
  font-size: 1.43rem;
}
p {
  margin: 0.8rem 0;
}
a {
  color: #008dc6;
  text-decoration: none;
}
a:hover {
  color: #007bad;
  text-decoration: underline;
}
.x-info {
  color: #3a87ad;
}
.x-success {
  color: #468847;
}
.x-warning {
  color: #c09853;
}
.x-error {
  color: #b94a48;
}
img.x-responsive {
  width: 100%;
  max-width: 100%;
  height: auto;
}
img.x-rounded {
  -webkit-border-radius: 1rem;
  -moz-border-radius: 1rem;
  border-radius: 1rem;
}
img.x--polaroid {
  padding: 4px;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
img.x-circle {
  -webkit-border-radius: 500px;
  -moz-border-radius: 500px;
  border-radius: 500px;
}
table {
  max-width: 100%;
}
table th {
  text-align: left;
}
.x-table {
  width: 100%;
  border: 1px solid #ffffff;
}
.x-table tr {
  border-bottom: 1px solid #ffffff;
}
.x-table tr th,
.x-table tr td {
  padding: 0.6rem 1rem;
  line-height: 1.82rem;
  vertical-align: middle;
}
.x-table thead tr th {
  background: #eeeeee;
  color: #909090;
  font-size: 100%;
  font-weight: normal;
}
.x-table thead tr th.x-sorting,
.x-table thead tr th.x-sorting-asc,
.x-table thead tr th.x-sorting-desc {
  cursor: pointer;
}
.x-table thead tr th.x-sorting:hover,
.x-table thead tr th.x-sorting-asc:hover,
.x-table thead tr th.x-sorting-desc:hover {
  color: #5d5d5d;
}
.x-table thead tr th.x-sorting {
  background: #eeeeee url("elements/tables/sort.png") no-repeat scroll right center;
}
.x-table thead tr th.x-sorting-asc {
  background: #eeeeee url("elements/tables/sort_asc.png") no-repeat scroll right center;
}
.x-table thead tr th.x-sorting-desc {
  background: #eeeeee url("elements/tables/sort_desc.png") no-repeat scroll right center;
}
.x-table tbody {
  background: #f7f7f7;
}
.x-table tbody tr:last-child {
  border-bottom: 0;
}
.x-table.x-mini th,
.x-table.x-mini td {
  padding: 1px 2px;
}
.x-table.x-small th,
.x-table.x-small td {
  padding: 0.4rem 0.6rem;
}
.x-table.x-large th,
.x-table.x-large td {
  padding: 1rem 1.8rem;
}
.x-table-bordered th,
.x-table-bordered td {
  border-right: 1px solid #ffffff;
  border-left: 1px solid #ffffff;
}
.x-table-bordered th:first-child,
.x-table-bordered td:first-child {
  border-left: 0;
}
.x-table-bordered th:last-child,
.x-table-bordered td:last-child {
  border-right: 0;
}
.x-table-striped tbody tr:nth-child(even) td {
  background-color: #f2f2f2;
}
.x-table-hover tbody tr:hover td {
  background-color: #f4f4f4;
}
.x-panel > .x-table,
.x-panel > .x-table.dataTable,
.x-panel > .x-table-container > .x-table {
  border: 0;
}
.x-table-header {
  border-bottom: 1px solid #ffffff;
}
.x-table-footer {
  border-top: 1px solid #ffffff;
}
.x-table-header,
.x-table-footer {
  padding: 0.6rem 1rem;
}
.x-table-header:before,
.x-table-footer:before,
.x-table-header:after,
.x-table-footer:after {
  content: " ";
  display: table;
}
.x-table-header:after,
.x-table-footer:after {
  clear: both;
}
.x-table-container {
  overflow-y: hidden;
  overflow-x: auto;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  border: 1px solid #ffffff;
  -webkit-overflow-scrolling: touch;
  padding: 0 !important;
}
.x-table-container > .x-table {
  border: 0;
  margin-bottom: 0;
}
.x-table-container > .x-table th,
.x-table-container > .x-table td {
  white-space: nowrap;
}
.x-panel > .x-table-container {
  border: 0;
}
@media only screen and (max-width: 48rem) {
  .x-table-container-flip {
    display: block;
    position: relative;
    width: 100%;
  }
  .x-table-container-flip thead {
    display: block;
    float: left;
  }
  .x-table-container-flip thead tr {
    display: block;
    border-right: 1px solid #dfdfdf;
  }
  .x-table-container-flip thead tr th {
    display: block;
    border: none;
    border-top: 1px solid #dfdfdf;
    box-shadow: none;
    padding: 0.6rem 1rem;
  }
  .x-table-container-flip thead tr th:first-child {
    border-left: none;
    border-top: none;
  }
  .x-table-container-flip tbody {
    display: block;
    position: relative;
    width: auto;
    overflow-x: auto;
    overflow-y: scroll;
    white-space: nowrap;
  }
  .x-table-container-flip tbody tr {
    display: inline-block;
    vertical-align: top;
    margin-left: -3px;
    border-top: none;
    border-right: 1px solid #dfdfdf;
  }
  .x-table-container-flip tbody tr:first-child {
    margin-left: 0;
  }
  .x-table-container-flip tbody tr td {
    display: block;
    border-left: none;
    border-top: 1px solid #dfdfdf;
    min-width: 80px;
    padding: 0.6rem 1rem;
  }
  .x-table-container-flip tbody tr td:first-child {
    border-top: none;
  }
}
.x-form-row {
  margin-bottom: 0.728rem;
}
.x-form-row:before,
.x-form-row:after {
  content: " ";
  display: table;
}
.x-form-row:after {
  clear: both;
}
.x-form-row .checkbox[class*="x-grid-col"],
.x-form-row .radio[class*="x-grid-col"] {
  padding-top: 5px;
}
.x-form-actions {
  padding: 0.6rem 1rem;
  background-color: #ffffff;
  text-align: right;
}
.x-form-actions:before,
.x-form-actions:after {
  content: " ";
  display: table;
}
.x-form-actions:after {
  clear: both;
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 1.82rem;
  font-size: 1.95rem;
  line-height: 3.64rem;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
label {
  display: inline-block;
  padding: 4px 0;
  margin-bottom: 0.455rem;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  /* IE8-9 */
  line-height: normal;
}
input[type="file"] {
  display: block;
}
select[multiple],
select[size] {
  height: auto;
}
select optgroup {
  font-size: inherit;
  font-style: inherit;
  font-family: inherit;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  height: auto;
}
output {
  display: block;
  padding-top: 0.7em;
  font-size: 1.3rem;
  line-height: 1.82rem;
  vertical-align: middle;
}
select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"] {
  display: inline-block;
  width: 100%;
  padding: 4px 4px;
  background-color: #fff;
  border: 1px solid #ccc;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  font-size: 1.3rem;
  font-weight: normal;
  font-family: 'Roboto Condensed', Verdana, Arial, sans-serif;
  color: #555555;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -moz-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
select:focus,
textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
  -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
select:-moz-placeholder,
textarea:-moz-placeholder,
input[type="text"]:-moz-placeholder,
input[type="password"]:-moz-placeholder,
input[type="datetime"]:-moz-placeholder,
input[type="datetime-local"]:-moz-placeholder,
input[type="date"]:-moz-placeholder,
input[type="month"]:-moz-placeholder,
input[type="time"]:-moz-placeholder,
input[type="week"]:-moz-placeholder,
input[type="number"]:-moz-placeholder,
input[type="email"]:-moz-placeholder,
input[type="url"]:-moz-placeholder,
input[type="search"]:-moz-placeholder,
input[type="tel"]:-moz-placeholder,
input[type="color"]:-moz-placeholder {
  color: #999999;
}
select::-moz-placeholder,
textarea::-moz-placeholder,
input[type="text"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="datetime"]::-moz-placeholder,
input[type="datetime-local"]::-moz-placeholder,
input[type="date"]::-moz-placeholder,
input[type="month"]::-moz-placeholder,
input[type="time"]::-moz-placeholder,
input[type="week"]::-moz-placeholder,
input[type="number"]::-moz-placeholder,
input[type="email"]::-moz-placeholder,
input[type="url"]::-moz-placeholder,
input[type="search"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
input[type="color"]::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
select:-ms-input-placeholder,
textarea:-ms-input-placeholder,
input[type="text"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="datetime"]:-ms-input-placeholder,
input[type="datetime-local"]:-ms-input-placeholder,
input[type="date"]:-ms-input-placeholder,
input[type="month"]:-ms-input-placeholder,
input[type="time"]:-ms-input-placeholder,
input[type="week"]:-ms-input-placeholder,
input[type="number"]:-ms-input-placeholder,
input[type="email"]:-ms-input-placeholder,
input[type="url"]:-ms-input-placeholder,
input[type="search"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
input[type="color"]:-ms-input-placeholder {
  color: #999999;
}
select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder,
input[type="text"]::-webkit-input-placeholder,
input[type="password"]::-webkit-input-placeholder,
input[type="datetime"]::-webkit-input-placeholder,
input[type="datetime-local"]::-webkit-input-placeholder,
input[type="date"]::-webkit-input-placeholder,
input[type="month"]::-webkit-input-placeholder,
input[type="time"]::-webkit-input-placeholder,
input[type="week"]::-webkit-input-placeholder,
input[type="number"]::-webkit-input-placeholder,
input[type="email"]::-webkit-input-placeholder,
input[type="url"]::-webkit-input-placeholder,
input[type="search"]::-webkit-input-placeholder,
input[type="tel"]::-webkit-input-placeholder,
input[type="color"]::-webkit-input-placeholder {
  color: #999999;
}
select.x-disabled,
textarea.x-disabled,
input[type="text"].x-disabled,
input[type="password"].x-disabled,
input[type="datetime"].x-disabled,
input[type="datetime-local"].x-disabled,
input[type="date"].x-disabled,
input[type="month"].x-disabled,
input[type="time"].x-disabled,
input[type="week"].x-disabled,
input[type="number"].x-disabled,
input[type="email"].x-disabled,
input[type="url"].x-disabled,
input[type="search"].x-disabled,
input[type="tel"].x-disabled,
input[type="color"].x-disabled,
select[disabled],
textarea[disabled],
input[type="text"][disabled],
input[type="password"][disabled],
input[type="datetime"][disabled],
input[type="datetime-local"][disabled],
input[type="date"][disabled],
input[type="month"][disabled],
input[type="time"][disabled],
input[type="week"][disabled],
input[type="number"][disabled],
input[type="email"][disabled],
input[type="url"][disabled],
input[type="search"][disabled],
input[type="tel"][disabled],
input[type="color"][disabled],
select[readonly],
textarea[readonly],
input[type="text"][readonly],
input[type="password"][readonly],
input[type="datetime"][readonly],
input[type="datetime-local"][readonly],
input[type="date"][readonly],
input[type="month"][readonly],
input[type="time"][readonly],
input[type="week"][readonly],
input[type="number"][readonly],
input[type="email"][readonly],
input[type="url"][readonly],
input[type="search"][readonly],
input[type="tel"][readonly],
input[type="color"][readonly],
fieldset[disabled] select,
fieldset[disabled] textarea,
fieldset[disabled] input[type="text"],
fieldset[disabled] input[type="password"],
fieldset[disabled] input[type="datetime"],
fieldset[disabled] input[type="datetime-local"],
fieldset[disabled] input[type="date"],
fieldset[disabled] input[type="month"],
fieldset[disabled] input[type="time"],
fieldset[disabled] input[type="week"],
fieldset[disabled] input[type="number"],
fieldset[disabled] input[type="email"],
fieldset[disabled] input[type="url"],
fieldset[disabled] input[type="search"],
fieldset[disabled] input[type="tel"],
fieldset[disabled] input[type="color"] {
  cursor: not-allowed;
  background-color: #fafafa;
}
textarea {
  height: auto;
  line-height: 1.82rem;
}
select.x-mini,
textarea.x-mini,
input.x-mini {
  width: 3rem;
}
select.x-small,
textarea.x-small,
input.x-small {
  width: 6rem;
}
select.x-medium,
textarea.x-medium,
input.x-medium {
  max-width: 14rem;
}
select.x-large,
textarea.x-large,
input.x-large {
  max-width: 24rem;
}
select.x-xlarge,
textarea.x-xlarge,
input.x-xlarge {
  max-width: 36rem;
}
select.x-xxlarge,
textarea.x-xxlarge,
input.x-xxlarge {
  max-width: 48rem;
}
.x-info {
  /*
	// Set validation states also for addons
	.input-group-addon {
		color: @text-color;
		border-color: @border-color;
		background-color: @background-color;
	}
	*/
}
.x-info label,
.x-info select,
.x-info textarea,
.x-info input,
.x-info .x-help-block,
.x-info .x-help-inline {
  color: #3a87ad;
}
.x-info input,
.x-info select,
.x-info textarea {
  border-color: #3a87ad;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.x-info input:focus,
.x-info select:focus,
.x-info textarea:focus {
  border-color: #2d6987;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7ab5d3;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7ab5d3;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7ab5d3;
}
.x-success {
  /*
	// Set validation states also for addons
	.input-group-addon {
		color: @text-color;
		border-color: @border-color;
		background-color: @background-color;
	}
	*/
}
.x-success label,
.x-success select,
.x-success textarea,
.x-success input,
.x-success .x-help-block,
.x-success .x-help-inline {
  color: #468847;
}
.x-success input,
.x-success select,
.x-success textarea {
  border-color: #468847;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.x-success input:focus,
.x-success select:focus,
.x-success textarea:focus {
  border-color: #356635;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #7aba7b;
}
.x-warning {
  /*
	// Set validation states also for addons
	.input-group-addon {
		color: @text-color;
		border-color: @border-color;
		background-color: @background-color;
	}
	*/
}
.x-warning label,
.x-warning select,
.x-warning textarea,
.x-warning input,
.x-warning .x-help-block,
.x-warning .x-help-inline {
  color: #c09853;
}
.x-warning input,
.x-warning select,
.x-warning textarea {
  border-color: #c09853;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.x-warning input:focus,
.x-warning select:focus,
.x-warning textarea:focus {
  border-color: #a47e3c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #dbc59e;
}
.x-error {
  /*
	// Set validation states also for addons
	.input-group-addon {
		color: @text-color;
		border-color: @border-color;
		background-color: @background-color;
	}
	*/
}
.x-error label,
.x-error select,
.x-error textarea,
.x-error input,
.x-error .x-help-block,
.x-error .x-help-inline {
  color: #b94a48;
}
.x-error input,
.x-error select,
.x-error textarea {
  border-color: #b94a48;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.x-error input:focus,
.x-error select:focus,
.x-error textarea:focus {
  border-color: #953b39;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #d59392;
}
.x-help-block,
.x-help-inline {
  color: #595959;
  font-size: 90%;
}
.x-help-block {
  display: block;
}
.x-help-inline {
  display: inline-block;
  vertical-align: middle;
  padding-left: 5px;
}
.x-clearfix:before,
.x-clearfix:after {
  content: " ";
  display: table;
}
.x-clearfix:after {
  clear: both;
}
.x-center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.x-pull-right {
  float: right !important;
}
.x-pull-left {
  float: left !important;
}
.x-flex-hbox {
  display: block;
}
@media (min-width: 48.1rem) {
  .flexbox .x-flex-hbox {
    display: block;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: horizontal;
    -moz-box-orient: horizontal;
    -ms-box-orient: horizontal;
    box-direction: normal;
    flex-direction: row;
    -webkit-box-pack: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: stretch;
    -moz-box-align: stretch;
    -ms-flex-align: stretch;
    -webkit-align-items: stretch;
    align-items: stretch;
  }
  .flexbox .x-flex-hbox > * {
    display: block;
    flex: 0 1 auto;
    min-width: 0;
  }
  .no-flexbox .x-flex-hbox,
  .browser-safari .x-flex-hbox {
    display: table;
    table-layout: fixed;
    width: 100%;
  }
  .no-flexbox .x-flex-hbox > *,
  .browser-safari .x-flex-hbox > * {
    display: table-cell;
  }
}
.x-flex-vbox {
  display: block;
}
@media (min-width: 48.1rem) {
  .flexbox .x-flex-vbox {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    -ms-box-orient: vertical;
    box-direction: normal;
    flex-direction: column;
    -webkit-box-pack: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: stretch;
    -moz-box-align: stretch;
    -ms-flex-align: stretch;
    -webkit-align-items: stretch;
    align-items: stretch;
  }
  .flexbox .x-flex-vbox > * {
    flex: 0 1 auto;
    min-height: 0;
  }
  .no-flexbox .x-flex-vbox,
  .browser-safari .x-flex-vbox {
    display: table;
  }
}
.x-flex-1 {
  -webkit-box-flex: 1 !important;
  -moz-box-flex: 1 !important;
  -ms-box-flex: 1 !important;
  box-flex: 1 !important;
  -webkit-flex: 1 !important;
  -moz-flex: 1 !important;
  -ms-flex: 1 !important;
  flex: 1 !important;
}
.x-flex-2 {
  -webkit-box-flex: 2 !important;
  -moz-box-flex: 2 !important;
  -ms-box-flex: 2 !important;
  box-flex: 2 !important;
  -webkit-flex: 2 !important;
  -moz-flex: 2 !important;
  -ms-flex: 2 !important;
  flex: 2 !important;
}
.x-flex-3 {
  -webkit-box-flex: 3 !important;
  -moz-box-flex: 3 !important;
  -ms-box-flex: 3 !important;
  box-flex: 3 !important;
  -webkit-flex: 3 !important;
  -moz-flex: 3 !important;
  -ms-flex: 3 !important;
  flex: 3 !important;
}
.x-flex-4 {
  -webkit-box-flex: 4 !important;
  -moz-box-flex: 4 !important;
  -ms-box-flex: 4 !important;
  box-flex: 4 !important;
  -webkit-flex: 4 !important;
  -moz-flex: 4 !important;
  -ms-flex: 4 !important;
  flex: 4 !important;
}
.x-flex-5 {
  -webkit-box-flex: 5 !important;
  -moz-box-flex: 5 !important;
  -ms-box-flex: 5 !important;
  box-flex: 5 !important;
  -webkit-flex: 5 !important;
  -moz-flex: 5 !important;
  -ms-flex: 5 !important;
  flex: 5 !important;
}
.x-flex-6 {
  -webkit-box-flex: 6 !important;
  -moz-box-flex: 6 !important;
  -ms-box-flex: 6 !important;
  box-flex: 6 !important;
  -webkit-flex: 6 !important;
  -moz-flex: 6 !important;
  -ms-flex: 6 !important;
  flex: 6 !important;
}
.x-flex-7 {
  -webkit-box-flex: 7 !important;
  -moz-box-flex: 7 !important;
  -ms-box-flex: 7 !important;
  box-flex: 7 !important;
  -webkit-flex: 7 !important;
  -moz-flex: 7 !important;
  -ms-flex: 7 !important;
  flex: 7 !important;
}
.x-flex-8 {
  -webkit-box-flex: 8 !important;
  -moz-box-flex: 8 !important;
  -ms-box-flex: 8 !important;
  box-flex: 8 !important;
  -webkit-flex: 8 !important;
  -moz-flex: 8 !important;
  -ms-flex: 8 !important;
  flex: 8 !important;
}
.x-hide {
  display: none !important;
}
.x-block {
  display: block !important;
}
.x-inline {
  display: inline-block !important;
}
@media (max-width: 30rem) {
  .x-show-xs {
    display: block;
  }
  .x-show-inline-xs {
    display: inline !important;
  }
  .x-hide-xs {
    display: none !important;
  }
}
@media (min-width: 30.1rem) and (max-width: 48rem) {
  .x-show-sm {
    display: block;
  }
  .x-show-inline-sm {
    display: inline;
  }
  .x-hide-sm {
    display: none !important;
  }
}
@media (min-width: 48.1rem) and (max-width: 60rem) {
  .x-show-md {
    display: block;
  }
  .x-show-inline-md {
    display: inline;
  }
  .x-hide-md {
    display: none !important;
  }
}
@media (min-width: 60.1rem) and (max-width: 75rem) {
  .x-show-lg {
    display: block;
  }
  .x-show-inline-lg {
    display: inline;
  }
  .x-hide-lg {
    display: none !important;
  }
}
@media (min-width: 75.1rem) {
  .x-show-xl {
    display: block;
  }
  .x-show-inline-xl {
    display: inline;
  }
  .x-hide-xl {
    display: none !important;
  }
}
.x-bg-cover-fixed {
  background-size: cover;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-position: center center;
}
.x-bg-cover-scrolling {
  background-size: cover;
  background-attachment: scroll;
  background-repeat: no-repeat;
  background-position: center center;
}
.x-overflow-y {
  overflow-y: auto;
}
.x-overflow-x {
  overflow-x: auto;
}
.x-width-100 {
  width: 100%;
}
.x-height-100 {
  height: 100%;
}
@media (min-width: 60.1rem) {
  body > header {
    padding: 0 4rem !important;
  }
  body > main {
    padding: 1rem 0;
  }
  body > main > * {
    padding-left: 4rem !important;
    padding-right: 4rem !important;
  }
  body > main > .x-panel,
  body > main > .x-table-container {
    margin-left: 4rem !important;
    margin-right: 4rem !important;
  }
  body > section.x-flex-hbox > *:first-child {
    padding-left: 4rem;
  }
  body > section.x-flex-hbox > *:last-child {
    padding-right: 4rem;
  }
  body > section.x-flex-hbox > main:last-child {
    padding-right: 0;
  }
  body > section.x-flex-hbox > main:last-child > * {
    padding-left: 1rem;
    padding-right: 4rem;
  }
  body > section.x-flex-hbox > main:last-child > .x-panel,
  body > section.x-flex-hbox > main:last-child > .x-table-container {
    margin-left: 1rem !important;
    margin-right: 4rem !important;
  }
  #x-nav-top {
    padding: 0 4rem;
  }
}
.x-grid,
.x-grid-fixed {
  margin-right: auto;
  margin-left: auto;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.x-grid:before,
.x-grid-fixed:before,
.x-grid:after,
.x-grid-fixed:after {
  content: " ";
  display: table;
}
.x-grid:after,
.x-grid-fixed:after {
  clear: both;
}
@media (max-width: 48rem) {
  .x-grid-fixed {
    width: 721px;
  }
}
@media (max-width: 60rem) {
  .x-grid-fixed {
    width: 941px;
  }
}
@media (max-width: 75rem) {
  .x-grid-fixed {
    width: 1141px;
  }
}
.x-grid-full {
  margin: 0 auto;
  width: 100%;
}
.x-grid-row {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.x-grid-row:before,
.x-grid-row:after {
  content: " ";
  display: table;
}
.x-grid-row:after {
  clear: both;
}
.x-col-1-xs,
.x-col-1-sm,
.x-col-1-md,
.x-col-1-lg,
.x-col-1-xl,
.x-col-2-xs,
.x-col-2-sm,
.x-col-2-md,
.x-col-2-lg,
.x-col-2-xl,
.x-col-3-xs,
.x-col-3-sm,
.x-col-3-md,
.x-col-3-lg,
.x-col-3-xl,
.x-col-4-xs,
.x-col-4-sm,
.x-col-4-md,
.x-col-4-lg,
.x-col-4-xl,
.x-col-5-xs,
.x-col-5-sm,
.x-col-5-md,
.x-col-5-lg,
.x-col-5-xl,
.x-col-6-xs,
.x-col-6-sm,
.x-col-6-md,
.x-col-6-lg,
.x-col-6-xl,
.x-col-7-xs,
.x-col-7-sm,
.x-col-7-md,
.x-col-7-lg,
.x-col-7-xl,
.x-col-8-xs,
.x-col-8-sm,
.x-col-8-md,
.x-col-8-lg,
.x-col-8-xl,
.x-col-9-xs,
.x-col-9-sm,
.x-col-9-md,
.x-col-9-lg,
.x-col-9-xl,
.x-col-10-xs,
.x-col-10-sm,
.x-col-10-md,
.x-col-10-lg,
.x-col-10-xl,
.x-col-11-xs,
.x-col-11-sm,
.x-col-11-md,
.x-col-11-lg,
.x-col-11-xl,
.x-col-12-xs,
.x-col-12-sm,
.x-col-12-md,
.x-col-12-lg,
.x-col-12-xl {
  display: inline-block;
  position: relative;
  min-height: 1px;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.x-col-1-xs,
.x-col-2-xs,
.x-col-3-xs,
.x-col-4-xs,
.x-col-5-xs,
.x-col-6-xs,
.x-col-7-xs,
.x-col-8-xs,
.x-col-9-xs,
.x-col-10-xs,
.x-col-11-xs {
  float: left;
}
.x-col-12-xs {
  width: 100%;
}
.x-col-11-xs {
  width: 91.66666667%;
}
.x-col-10-xs {
  width: 83.33333333%;
}
.x-col-9-xs {
  width: 75%;
}
.x-col-8-xs {
  width: 66.66666667%;
}
.x-col-7-xs {
  width: 58.33333333%;
}
.x-col-6-xs {
  width: 50%;
}
.x-col-5-xs {
  width: 41.66666667%;
}
.x-col-4-xs {
  width: 33.33333333%;
}
.x-col-3-xs {
  width: 25%;
}
.x-col-2-xs {
  width: 16.66666667%;
}
.x-col-1-xs {
  width: 8.33333333%;
}
.x-col-pull-12-xs {
  right: 100%;
}
.x-col-pull-11-xs {
  right: 91.66666667%;
}
.x-col-pull-10-xs {
  right: 83.33333333%;
}
.x-col-pull-9-xs {
  right: 75%;
}
.x-col-pull-8-xs {
  right: 66.66666667%;
}
.x-col-pull-7-xs {
  right: 58.33333333%;
}
.x-col-pull-6-xs {
  right: 50%;
}
.x-col-pull-5-xs {
  right: 41.66666667%;
}
.x-col-pull-4-xs {
  right: 33.33333333%;
}
.x-col-pull-3-xs {
  right: 25%;
}
.x-col-pull-2-xs {
  right: 16.66666667%;
}
.x-col-pull-1-xs {
  right: 8.33333333%;
}
.x-col-pull-0-xs {
  right: 0%;
}
.x-col-push-12-xs {
  left: 100%;
}
.x-col-push-11-xs {
  left: 91.66666667%;
}
.x-col-push-10-xs {
  left: 83.33333333%;
}
.x-col-push-9-xs {
  left: 75%;
}
.x-col-push-8-xs {
  left: 66.66666667%;
}
.x-col-push-7-xs {
  left: 58.33333333%;
}
.x-col-push-6-xs {
  left: 50%;
}
.x-col-push-5-xs {
  left: 41.66666667%;
}
.x-col-push-4-xs {
  left: 33.33333333%;
}
.x-col-push-3-xs {
  left: 25%;
}
.x-col-push-2-xs {
  left: 16.66666667%;
}
.x-col-push-1-xs {
  left: 8.33333333%;
}
.x-col-push-0-xs {
  left: 0%;
}
.x-col-offset-12-xs {
  margin-left: 100%;
}
.x-col-offset-11-xs {
  margin-left: 91.66666667%;
}
.x-col-offset-10-xs {
  margin-left: 83.33333333%;
}
.x-col-offset-9-xs {
  margin-left: 75%;
}
.x-col-offset-8-xs {
  margin-left: 66.66666667%;
}
.x-col-offset-7-xs {
  margin-left: 58.33333333%;
}
.x-col-offset-6-xs {
  margin-left: 50%;
}
.x-col-offset-5-xs {
  margin-left: 41.66666667%;
}
.x-col-offset-4-xs {
  margin-left: 33.33333333%;
}
.x-col-offset-3-xs {
  margin-left: 25%;
}
.x-col-offset-2-xs {
  margin-left: 16.66666667%;
}
.x-col-offset-1-xs {
  margin-left: 8.33333333%;
}
.x-col-offset-0-xs {
  margin-left: 0%;
}
@media (min-width: 30.1rem) {
  .x-col-1-sm,
  .x-col-2-sm,
  .x-col-3-sm,
  .x-col-4-sm,
  .x-col-5-sm,
  .x-col-6-sm,
  .x-col-7-sm,
  .x-col-8-sm,
  .x-col-9-sm,
  .x-col-10-sm,
  .x-col-11-sm {
    float: left;
  }
  .x-col-12-sm {
    width: 100%;
  }
  .x-col-11-sm {
    width: 91.66666667%;
  }
  .x-col-10-sm {
    width: 83.33333333%;
  }
  .x-col-9-sm {
    width: 75%;
  }
  .x-col-8-sm {
    width: 66.66666667%;
  }
  .x-col-7-sm {
    width: 58.33333333%;
  }
  .x-col-6-sm {
    width: 50%;
  }
  .x-col-5-sm {
    width: 41.66666667%;
  }
  .x-col-4-sm {
    width: 33.33333333%;
  }
  .x-col-3-sm {
    width: 25%;
  }
  .x-col-2-sm {
    width: 16.66666667%;
  }
  .x-col-1-sm {
    width: 8.33333333%;
  }
  .x-col-pull-12-sm {
    right: 100%;
  }
  .x-col-pull-11-sm {
    right: 91.66666667%;
  }
  .x-col-pull-10-sm {
    right: 83.33333333%;
  }
  .x-col-pull-9-sm {
    right: 75%;
  }
  .x-col-pull-8-sm {
    right: 66.66666667%;
  }
  .x-col-pull-7-sm {
    right: 58.33333333%;
  }
  .x-col-pull-6-sm {
    right: 50%;
  }
  .x-col-pull-5-sm {
    right: 41.66666667%;
  }
  .x-col-pull-4-sm {
    right: 33.33333333%;
  }
  .x-col-pull-3-sm {
    right: 25%;
  }
  .x-col-pull-2-sm {
    right: 16.66666667%;
  }
  .x-col-pull-1-sm {
    right: 8.33333333%;
  }
  .x-col-pull-0-sm {
    right: 0%;
  }
  .x-col-push-12-sm {
    left: 100%;
  }
  .x-col-push-11-sm {
    left: 91.66666667%;
  }
  .x-col-push-10-sm {
    left: 83.33333333%;
  }
  .x-col-push-9-sm {
    left: 75%;
  }
  .x-col-push-8-sm {
    left: 66.66666667%;
  }
  .x-col-push-7-sm {
    left: 58.33333333%;
  }
  .x-col-push-6-sm {
    left: 50%;
  }
  .x-col-push-5-sm {
    left: 41.66666667%;
  }
  .x-col-push-4-sm {
    left: 33.33333333%;
  }
  .x-col-push-3-sm {
    left: 25%;
  }
  .x-col-push-2-sm {
    left: 16.66666667%;
  }
  .x-col-push-1-sm {
    left: 8.33333333%;
  }
  .x-col-push-0-sm {
    left: 0%;
  }
  .x-col-offset-12-sm {
    margin-left: 100%;
  }
  .x-col-offset-11-sm {
    margin-left: 91.66666667%;
  }
  .x-col-offset-10-sm {
    margin-left: 83.33333333%;
  }
  .x-col-offset-9-sm {
    margin-left: 75%;
  }
  .x-col-offset-8-sm {
    margin-left: 66.66666667%;
  }
  .x-col-offset-7-sm {
    margin-left: 58.33333333%;
  }
  .x-col-offset-6-sm {
    margin-left: 50%;
  }
  .x-col-offset-5-sm {
    margin-left: 41.66666667%;
  }
  .x-col-offset-4-sm {
    margin-left: 33.33333333%;
  }
  .x-col-offset-3-sm {
    margin-left: 25%;
  }
  .x-col-offset-2-sm {
    margin-left: 16.66666667%;
  }
  .x-col-offset-1-sm {
    margin-left: 8.33333333%;
  }
  .x-col-offset-0-sm {
    margin-left: 0%;
  }
}
@media (min-width: 48.1rem) {
  .x-col-1-md,
  .x-col-2-md,
  .x-col-3-md,
  .x-col-4-md,
  .x-col-5-md,
  .x-col-6-md,
  .x-col-7-md,
  .x-col-8-md,
  .x-col-9-md,
  .x-col-10-md,
  .x-col-11-md {
    float: left;
  }
  .x-col-12-md {
    width: 100%;
  }
  .x-col-11-md {
    width: 91.66666667%;
  }
  .x-col-10-md {
    width: 83.33333333%;
  }
  .x-col-9-md {
    width: 75%;
  }
  .x-col-8-md {
    width: 66.66666667%;
  }
  .x-col-7-md {
    width: 58.33333333%;
  }
  .x-col-6-md {
    width: 50%;
  }
  .x-col-5-md {
    width: 41.66666667%;
  }
  .x-col-4-md {
    width: 33.33333333%;
  }
  .x-col-3-md {
    width: 25%;
  }
  .x-col-2-md {
    width: 16.66666667%;
  }
  .x-col-1-md {
    width: 8.33333333%;
  }
  .x-col-pull-12-md {
    right: 100%;
  }
  .x-col-pull-11-md {
    right: 91.66666667%;
  }
  .x-col-pull-10-md {
    right: 83.33333333%;
  }
  .x-col-pull-9-md {
    right: 75%;
  }
  .x-col-pull-8-md {
    right: 66.66666667%;
  }
  .x-col-pull-7-md {
    right: 58.33333333%;
  }
  .x-col-pull-6-md {
    right: 50%;
  }
  .x-col-pull-5-md {
    right: 41.66666667%;
  }
  .x-col-pull-4-md {
    right: 33.33333333%;
  }
  .x-col-pull-3-md {
    right: 25%;
  }
  .x-col-pull-2-md {
    right: 16.66666667%;
  }
  .x-col-pull-1-md {
    right: 8.33333333%;
  }
  .x-col-pull-0-md {
    right: 0%;
  }
  .x-col-push-12-md {
    left: 100%;
  }
  .x-col-push-11-md {
    left: 91.66666667%;
  }
  .x-col-push-10-md {
    left: 83.33333333%;
  }
  .x-col-push-9-md {
    left: 75%;
  }
  .x-col-push-8-md {
    left: 66.66666667%;
  }
  .x-col-push-7-md {
    left: 58.33333333%;
  }
  .x-col-push-6-md {
    left: 50%;
  }
  .x-col-push-5-md {
    left: 41.66666667%;
  }
  .x-col-push-4-md {
    left: 33.33333333%;
  }
  .x-col-push-3-md {
    left: 25%;
  }
  .x-col-push-2-md {
    left: 16.66666667%;
  }
  .x-col-push-1-md {
    left: 8.33333333%;
  }
  .x-col-push-0-md {
    left: 0%;
  }
  .x-col-offset-12-md {
    margin-left: 100%;
  }
  .x-col-offset-11-md {
    margin-left: 91.66666667%;
  }
  .x-col-offset-10-md {
    margin-left: 83.33333333%;
  }
  .x-col-offset-9-md {
    margin-left: 75%;
  }
  .x-col-offset-8-md {
    margin-left: 66.66666667%;
  }
  .x-col-offset-7-md {
    margin-left: 58.33333333%;
  }
  .x-col-offset-6-md {
    margin-left: 50%;
  }
  .x-col-offset-5-md {
    margin-left: 41.66666667%;
  }
  .x-col-offset-4-md {
    margin-left: 33.33333333%;
  }
  .x-col-offset-3-md {
    margin-left: 25%;
  }
  .x-col-offset-2-md {
    margin-left: 16.66666667%;
  }
  .x-col-offset-1-md {
    margin-left: 8.33333333%;
  }
  .x-col-offset-0-md {
    margin-left: 0%;
  }
}
@media (min-width: 60.1rem) {
  .x-col-1-lg,
  .x-col-2-lg,
  .x-col-3-lg,
  .x-col-4-lg,
  .x-col-5-lg,
  .x-col-6-lg,
  .x-col-7-lg,
  .x-col-8-lg,
  .x-col-9-lg,
  .x-col-10-lg,
  .x-col-11-lg {
    float: left;
  }
  .x-col-12-lg {
    width: 100%;
  }
  .x-col-11-lg {
    width: 91.66666667%;
  }
  .x-col-10-lg {
    width: 83.33333333%;
  }
  .x-col-9-lg {
    width: 75%;
  }
  .x-col-8-lg {
    width: 66.66666667%;
  }
  .x-col-7-lg {
    width: 58.33333333%;
  }
  .x-col-6-lg {
    width: 50%;
  }
  .x-col-5-lg {
    width: 41.66666667%;
  }
  .x-col-4-lg {
    width: 33.33333333%;
  }
  .x-col-3-lg {
    width: 25%;
  }
  .x-col-2-lg {
    width: 16.66666667%;
  }
  .x-col-1-lg {
    width: 8.33333333%;
  }
  .x-col-pull-12-lg {
    right: 100%;
  }
  .x-col-pull-11-lg {
    right: 91.66666667%;
  }
  .x-col-pull-10-lg {
    right: 83.33333333%;
  }
  .x-col-pull-9-lg {
    right: 75%;
  }
  .x-col-pull-8-lg {
    right: 66.66666667%;
  }
  .x-col-pull-7-lg {
    right: 58.33333333%;
  }
  .x-col-pull-6-lg {
    right: 50%;
  }
  .x-col-pull-5-lg {
    right: 41.66666667%;
  }
  .x-col-pull-4-lg {
    right: 33.33333333%;
  }
  .x-col-pull-3-lg {
    right: 25%;
  }
  .x-col-pull-2-lg {
    right: 16.66666667%;
  }
  .x-col-pull-1-lg {
    right: 8.33333333%;
  }
  .x-col-pull-0-lg {
    right: 0%;
  }
  .x-col-push-12-lg {
    left: 100%;
  }
  .x-col-push-11-lg {
    left: 91.66666667%;
  }
  .x-col-push-10-lg {
    left: 83.33333333%;
  }
  .x-col-push-9-lg {
    left: 75%;
  }
  .x-col-push-8-lg {
    left: 66.66666667%;
  }
  .x-col-push-7-lg {
    left: 58.33333333%;
  }
  .x-col-push-6-lg {
    left: 50%;
  }
  .x-col-push-5-lg {
    left: 41.66666667%;
  }
  .x-col-push-4-lg {
    left: 33.33333333%;
  }
  .x-col-push-3-lg {
    left: 25%;
  }
  .x-col-push-2-lg {
    left: 16.66666667%;
  }
  .x-col-push-1-lg {
    left: 8.33333333%;
  }
  .x-col-push-0-lg {
    left: 0%;
  }
  .x-col-offset-12-lg {
    margin-left: 100%;
  }
  .x-col-offset-11-lg {
    margin-left: 91.66666667%;
  }
  .x-col-offset-10-lg {
    margin-left: 83.33333333%;
  }
  .x-col-offset-9-lg {
    margin-left: 75%;
  }
  .x-col-offset-8-lg {
    margin-left: 66.66666667%;
  }
  .x-col-offset-7-lg {
    margin-left: 58.33333333%;
  }
  .x-col-offset-6-lg {
    margin-left: 50%;
  }
  .x-col-offset-5-lg {
    margin-left: 41.66666667%;
  }
  .x-col-offset-4-lg {
    margin-left: 33.33333333%;
  }
  .x-col-offset-3-lg {
    margin-left: 25%;
  }
  .x-col-offset-2-lg {
    margin-left: 16.66666667%;
  }
  .x-col-offset-1-lg {
    margin-left: 8.33333333%;
  }
  .x-col-offset-0-lg {
    margin-left: 0%;
  }
}
@media (min-width: 75.1rem) {
  .x-col-1-xl,
  .x-col-2-xl,
  .x-col-3-xl,
  .x-col-4-xl,
  .x-col-5-xl,
  .x-col-6-xl,
  .x-col-7-xl,
  .x-col-8-xl,
  .x-col-9-xl,
  .x-col-10-xl,
  .x-col-11-xl {
    float: left;
  }
  .x-col-12-xl {
    width: 100%;
  }
  .x-col-11-xl {
    width: 91.66666667%;
  }
  .x-col-10-xl {
    width: 83.33333333%;
  }
  .x-col-9-xl {
    width: 75%;
  }
  .x-col-8-xl {
    width: 66.66666667%;
  }
  .x-col-7-xl {
    width: 58.33333333%;
  }
  .x-col-6-xl {
    width: 50%;
  }
  .x-col-5-xl {
    width: 41.66666667%;
  }
  .x-col-4-xl {
    width: 33.33333333%;
  }
  .x-col-3-xl {
    width: 25%;
  }
  .x-col-2-xl {
    width: 16.66666667%;
  }
  .x-col-1-xl {
    width: 8.33333333%;
  }
  .x-col-pull-12-xl {
    right: 100%;
  }
  .x-col-pull-11-xl {
    right: 91.66666667%;
  }
  .x-col-pull-10-xl {
    right: 83.33333333%;
  }
  .x-col-pull-9-xl {
    right: 75%;
  }
  .x-col-pull-8-xl {
    right: 66.66666667%;
  }
  .x-col-pull-7-xl {
    right: 58.33333333%;
  }
  .x-col-pull-6-xl {
    right: 50%;
  }
  .x-col-pull-5-xl {
    right: 41.66666667%;
  }
  .x-col-pull-4-xl {
    right: 33.33333333%;
  }
  .x-col-pull-3-xl {
    right: 25%;
  }
  .x-col-pull-2-xl {
    right: 16.66666667%;
  }
  .x-col-pull-1-xl {
    right: 8.33333333%;
  }
  .x-col-pull-0-xl {
    right: 0%;
  }
  .x-col-push-12-xl {
    left: 100%;
  }
  .x-col-push-11-xl {
    left: 91.66666667%;
  }
  .x-col-push-10-xl {
    left: 83.33333333%;
  }
  .x-col-push-9-xl {
    left: 75%;
  }
  .x-col-push-8-xl {
    left: 66.66666667%;
  }
  .x-col-push-7-xl {
    left: 58.33333333%;
  }
  .x-col-push-6-xl {
    left: 50%;
  }
  .x-col-push-5-xl {
    left: 41.66666667%;
  }
  .x-col-push-4-xl {
    left: 33.33333333%;
  }
  .x-col-push-3-xl {
    left: 25%;
  }
  .x-col-push-2-xl {
    left: 16.66666667%;
  }
  .x-col-push-1-xl {
    left: 8.33333333%;
  }
  .x-col-push-0-xl {
    left: 0%;
  }
  .x-col-offset-12-xl {
    margin-left: 100%;
  }
  .x-col-offset-11-xl {
    margin-left: 91.66666667%;
  }
  .x-col-offset-10-xl {
    margin-left: 83.33333333%;
  }
  .x-col-offset-9-xl {
    margin-left: 75%;
  }
  .x-col-offset-8-xl {
    margin-left: 66.66666667%;
  }
  .x-col-offset-7-xl {
    margin-left: 58.33333333%;
  }
  .x-col-offset-6-xl {
    margin-left: 50%;
  }
  .x-col-offset-5-xl {
    margin-left: 41.66666667%;
  }
  .x-col-offset-4-xl {
    margin-left: 33.33333333%;
  }
  .x-col-offset-3-xl {
    margin-left: 25%;
  }
  .x-col-offset-2-xl {
    margin-left: 16.66666667%;
  }
  .x-col-offset-1-xl {
    margin-left: 8.33333333%;
  }
  .x-col-offset-0-xl {
    margin-left: 0%;
  }
}
body > header {
  min-height: 5rem;
  background: #e6e6e6;
  border-top: 0;
  border-bottom: 2px solid #d2d2d2;
  text-align: right;
}
body > header .x-logo {
  float: left;
  margin: 1rem 2rem;
}
body > header ul {
  list-style: none outside none;
  padding-left: 0;
  margin: 0;
}
body > header ul li {
  display: inline-block;
}
body > header .x-btn {
  background: #e6e6e6;
  filter: 0;
  border: 0;
  -webkit-border-radius: 0.25rem;
  -moz-border-radius: 0.25rem;
  border-radius: 0.25rem;
  margin: 1rem 0.2rem;
  padding: 0.5rem 0.7rem;
  font-size: 1.4rem;
  color: #999999;
  text-decoration: none;
  cursor: pointer;
}
body > header .x-btn.x-active,
body > header .x-btn:hover {
  background: #cdcdcd;
  color: #fff;
  text-decoration: none;
}
main {
  padding: 1rem 0;
  font-size: 1.3rem;
  line-height: 1.82rem;
}
main > header {
  margin: 10px 0 30px 0;
  padding: 0 1rem;
  font-size: 3rem;
  font-weight: 300;
  color: #626262;
}
main > header ul {
  list-style: none outside none;
  float: right;
}
main > header ul li {
  display: block;
  float: left;
  border-left: 1px solid #D9D9D9;
  padding: 2px 15px 0;
}
main > header ul li:first-child {
  border-left: 0 none;
}
main > * {
  padding-left: 1rem;
  padding-right: 1rem;
}
main > .x-grid {
  padding-left: 1rem;
  padding-right: 1rem;
}
main > .x-panel,
main > .x-table-container {
  margin-left: 1rem;
  margin-right: 1rem;
}
#x-nav-top,
#x-nav-main,
#x-nav-sec {
  font-family: 'Roboto Condensed', Verdana, Arial, sans-serif;
}
#x-nav-top ul,
#x-nav-main ul,
#x-nav-sec ul {
  display: block;
  position: relative;
  list-style: none outside none;
  margin: 0;
  padding: 0;
}
#x-nav-top li,
#x-nav-main li,
#x-nav-sec li {
  display: block;
  position: relative;
}
#x-nav-top a,
#x-nav-main a,
#x-nav-sec a {
  display: block;
  position: relative;
  padding: 0.6rem 1rem;
  text-decoration: none;
  cursor: pointer;
  outline: 0;
}
#x-nav-top a i,
#x-nav-main a i,
#x-nav-sec a i {
  margin-right: 0.5em;
}
#x-nav-top {
  font-weight: 400;
}
#x-nav-main {
  font-weight: 300;
}
#x-nav-sec {
  font-weight: 400;
}
#x-nav-btn {
  position: relative;
}
@media (min-width: 48.1rem) {
  #x-nav-btn {
    display: none;
  }
}
#x-nav-breadcrumbs {
  min-height: 2.3rem;
  border-bottom: 1px dashed #C3C3C3;
  margin-top: -1rem;
  font-family: Verdana, Arial, sans-serif;
  font-size: 0.9rem;
  line-height: 2.3rem;
}
#x-nav-breadcrumbs ul {
  list-style: none outside none;
  display: block;
  float: left;
  margin: 0;
  padding: 0;
  vertical-align: baseline;
}
#x-nav-breadcrumbs ul li {
  float: left;
  display: block;
  padding-left: 0.7rem;
}
#x-nav-breadcrumbs ul li:after {
  content: ' >';
}
#x-nav-breadcrumbs ul li:last-child:after {
  content: none;
}
#x-nav-breadcrumbs ul li a {
  color: #666666;
  text-decoration: none;
}
#x-nav-breadcrumbs ul li a:hover {
  color: #2B6893;
}
#x-nav-breadcrumbs ul:after {
  clear: both;
  content: '';
  display: block;
}
#x-nav-top {
  background-color: '';
  background-image: url('');
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
#x-nav-top li a {
  color: #6a6a6a;
}
#x-nav-top li a.x-active,
#x-nav-top li a:active,
#x-nav-top li a:hover {
  color: #008dc6;
}
#x-nav-top ul ul {
  background: white;
}
#x-nav-top li li a {
  border-top: 0;
  border-bottom: 0;
  color: #6a6a6a;
}
#x-nav-top li li a.x-active,
#x-nav-top li li a:active,
#x-nav-top li li a:hover {
  color: #008dc6;
}
#x-nav-top li li a.x-active {
  border-left: 0;
}
@media (max-width: 48rem) {
  #x-nav-top {
    font-size: 2.08rem;
  }
  #x-nav-top ul {
    display: block;
  }
  #x-nav-top > ul a {
    border-top: 0;
    border-bottom: 0;
  }
  #x-nav-top li li a {
    padding-left: 2rem;
  }
  #x-nav-top li > ul {
    display: none;
  }
  #x-nav-top li.x-open > ul {
    display: block;
  }
}
@media (min-width: 48.1rem) {
  #x-nav-top {
    border-top: 0;
    border-bottom: 0;
    border-bottom: 4px solid #d2d2d2;
    font-size: 1.6rem;
  }
  #x-nav-top li {
    display: inline-block;
  }
  #x-nav-top ul ul {
    z-index: 99;
    border: 1px solid #c3c3c3;
  }
  #x-nav-top li li a {
    width: 22rem;
    min-width: 22rem;
  }
  #x-nav-top ul ul li.x-open > ul {
    top: 0;
    left: 100%;
  }
  #x-nav-top li ul {
    position: absolute;
    display: none;
  }
  #x-nav-top li.x-open > ul {
    display: block;
  }
}
#x-nav-sec {
  background: white;
}
#x-nav-sec a {
  border-top: 0;
  border-bottom: 0;
  color: #6a6a6a;
}
#x-nav-sec a.x-active,
#x-nav-sec a:active,
#x-nav-sec a:hover {
  color: #008dc6;
}
#x-nav-sec a.x-active {
  border-left: 0;
}
@media (max-width: 48rem) {
  #x-nav-main {
    background-image: url('');
    font-size: 2.6rem;
  }
  #x-nav-main a {
    border-top: 0;
    border-bottom: 0;
    color: #6a6a6a;
  }
  #x-nav-main a.x-active,
  #x-nav-main a:active,
  #x-nav-main a:hover {
    color: #008dc6;
  }
  #x-nav-sec {
    font-size: 1.3rem;
  }
  #x-nav-sec a {
    padding-left: 2rem;
  }
}
@media (min-width: 48.1rem) {
  #x-nav-main {
    width: 26rem;
    padding-top: 4rem;
    padding-bottom: 0.6rem;
    background: '';
    border-right: 0;
    font-size: 2rem;
  }
  #x-nav-main ul {
    border-top: 0;
  }
  #x-nav-main a {
    border-top: 0;
    border-bottom: 0;
    color: #6a6a6a;
    text-align: left;
  }
  #x-nav-main a.x-active,
  #x-nav-main a:active,
  #x-nav-main a:hover {
    color: #008dc6;
  }
  #x-nav-main a i {
    font-size: 2.4rem;
    margin-right: 0;
  }
  #x-nav-main a span {
    display: inline-block;
    margin-left: 0.8rem;
  }
  #x-nav-main a.x-active {
    border-left: 0;
  }
  #x-nav-sec {
    width: 16rem;
    border-right: 0;
    padding-top: 2.8rem;
    padding-bottom: 0.6rem;
    font-size: 1rem;
  }
  #x-nav-sec ul {
    border-top: 0;
  }
}
@media (min-width: 75.1rem) {
  #x-nav-sec {
    width: 22rem;
  }
}
.x-alert {
  padding: 0.6rem 1rem;
  margin-bottom: 1.82rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.x-alert h4 {
  margin-top: 0;
  color: inherit;
}
.x-alert a {
  font-weight: bold;
}
.x-alert > p,
.x-alert > ul {
  margin-bottom: 0;
}
.x-alert > p + p {
  margin-top: 5px;
}
.x-alert-dismissable {
  padding-right: 2em;
}
.x-alert.x-blue {
  background-color: #a4d5ff;
  border-color: #0074d7;
  color: #0074d7;
}
.x-alert.x-blue hr {
  border-top-color: #0066be;
}
.x-alert.x-blue a {
  color: #0058a4;
}
.x-alert.x-blue-light {
  background-color: #bcddec;
  border-color: #3185ab;
  color: #3185ab;
}
.x-alert.x-blue-light hr {
  border-top-color: #2b7697;
}
.x-alert.x-blue-light a {
  color: #266683;
}
.x-alert.x-red {
  background-color: #eca09a;
  border-color: #9d261d;
  color: #9d261d;
}
.x-alert.x-red hr {
  border-top-color: #872119;
}
.x-alert.x-red a {
  color: #721c15;
}
.x-alert.x-green {
  background-color: #cdeacd;
  border-color: #46a546;
  color: #46a546;
}
.x-alert.x-green hr {
  border-top-color: #3e933e;
}
.x-alert.x-green a {
  color: #378137;
}
.x-alert.x-black {
  background-color: black;
  border-color: #eeeeee;
  color: #eeeeee;
}
.x-alert.x-black hr {
  border-top-color: #e2e2e2;
}
.x-alert.x-black a {
  color: #d5d5d5;
}
.x-alert.x-yellow {
  background-color: #fff6d9;
  border-color: #ffc40d;
  color: #ffc40d;
}
.x-alert.x-yellow hr {
  border-top-color: #f3b700;
}
.x-alert.x-yellow a {
  color: #d9a400;
}
.x-alert.x-orange {
  background-color: #fddfb3;
  border-color: #f89406;
  color: #f89406;
}
.x-alert.x-orange hr {
  border-top-color: #df8505;
}
.x-alert.x-orange a {
  color: #c67605;
}
.x-alert.x-purple {
  background-color: #e2d5f0;
  border-color: #7a43b6;
  color: #7a43b6;
}
.x-alert.x-purple hr {
  border-top-color: #6e3ca3;
}
.x-alert.x-purple a {
  color: #613591;
}
#x-ajax-spinner {
  display: none;
  position: fixed;
  z-index: 1110;
  top: 6rem;
  right: 2rem;
  padding-left: 30px;
  padding-right: 1rem;
  background: #eeeeee url('elements/ajax-spinner.gif') no-repeat 10px center;
  border: 1px solid #0074d7;
  -webkit-border-radius: 0.25rem;
  -moz-border-radius: 0.25rem;
  border-radius: 0.25rem;
  color: #0074d7;
  font-size: 1.3rem;
  font-weight: bold;
  line-height: 2.1rem;
}
.x-btn {
  display: inline-block;
  margin-bottom: 0;
  padding: 0.2rem 0.5rem;
  font-family: "Droid Sans";
  text-align: center;
  white-space: nowrap;
  cursor: pointer;
  *border: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.x-btn:hover,
.x-btn:focus {
  text-decoration: none;
  background-position: 0 -15px;
  -webkit-transition: background-position 0.1s linear;
  -moz-transition: background-position 0.1s linear;
  -o-transition: background-position 0.1s linear;
  transition: background-position 0.1s linear;
}
.x-btn:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.x-btn.x-active,
.x-btn:active {
  outline: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.x-btn.x-disabled,
.x-btn[disabled],
fieldset[disabled] .x-btn {
  cursor: not-allowed;
  pointer-events: none;
  background-image: none;
  opacity: 0.8;
  filter: alpha(opacity=80);
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.x-btn i[class*="icon-"] {
  vertical-align: text-top;
}
.x-btn {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.x-btn.x-large {
  padding: 0.5rem 1rem;
  font-size: 120%;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.x-btn.x-small {
  padding: 0.1rem 0.4rem;
  font-size: 90%;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.x-btn.x-mini {
  padding: 0px 3px;
  font-size: 80%;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.x-btn {
  color: #333333;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #e8e8e8;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#e8e8e8), to(#e8e8e8));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #e8e8e8 0%, #e8e8e8 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #e8e8e8 0%, #e8e8e8 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #e8e8e8 0%, #e8e8e8 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe8e8e8', endColorstr='#ffe8e8e8', GradientType=0);
  border-color: #e8e8e8 #e8e8e8 #c2c2c2;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #c3c3c3;
}
.x-btn:hover,
.x-btn:focus,
.x-btn:active,
.x-btn.x-active,
.x-btn.x-disabled,
.x-btn[disabled] {
  color: #333333;
  background-color: #e8e8e8;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-blue {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #3a70ab;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#3a70ab), to(#3a70ab));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #3a70ab 0%, #3a70ab 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #3a70ab 0%, #3a70ab 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #3a70ab 0%, #3a70ab 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff3a70ab', endColorstr='#ff3a70ab', GradientType=0);
  border-color: #3a70ab #3a70ab #274b72;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #3e76af;
}
.x-btn.x-blue:hover,
.x-btn.x-blue:focus,
.x-btn.x-blue:active,
.x-btn.x-blue.x-active,
.x-btn.x-blue.x-disabled,
.x-btn.x-blue[disabled] {
  color: #fff;
  background-color: #3a70ab;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-blue-dark {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #003366;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#003366), to(#003366));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #003366 0%, #003366 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #003366 0%, #003366 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #003366 0%, #003366 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff003366', endColorstr='#ff003366', GradientType=0);
  border-color: #003366 #003366 #000d1a;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #003366;
}
.x-btn.x-blue-dark:hover,
.x-btn.x-blue-dark:focus,
.x-btn.x-blue-dark:active,
.x-btn.x-blue-dark.x-active,
.x-btn.x-blue-dark.x-disabled,
.x-btn.x-blue-dark[disabled] {
  color: #fff;
  background-color: #003366;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-blue-light {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #45a1ca;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#45a1ca), to(#45a1ca));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #45a1ca 0%, #45a1ca 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #45a1ca 0%, #45a1ca 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #45a1ca 0%, #45a1ca 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff45a1ca', endColorstr='#ff45a1ca', GradientType=0);
  border-color: #45a1ca #45a1ca #2b7697;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #5897c3;
}
.x-btn.x-blue-light:hover,
.x-btn.x-blue-light:focus,
.x-btn.x-blue-light:active,
.x-btn.x-blue-light.x-active,
.x-btn.x-blue-light.x-disabled,
.x-btn.x-blue-light[disabled] {
  color: #fff;
  background-color: #45a1ca;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-green {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #609c3d;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#609c3d), to(#609c3d));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #609c3d 0%, #609c3d 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #609c3d 0%, #609c3d 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #609c3d 0%, #609c3d 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff609c3d', endColorstr='#ff609c3d', GradientType=0);
  border-color: #609c3d #609c3d #3e6527;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #68a341;
}
.x-btn.x-green:hover,
.x-btn.x-green:focus,
.x-btn.x-green:active,
.x-btn.x-green.x-active,
.x-btn.x-green.x-disabled,
.x-btn.x-green[disabled] {
  color: #fff;
  background-color: #609c3d;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-green-dark {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #007e63;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#009d7b), to(#00513f));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #009d7b 0%, #00513f 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #009d7b 0%, #00513f 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #009d7b 0%, #00513f 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff009d7b', endColorstr='#ff00513f', GradientType=0);
  border-color: #009d7b #00513f #00513f;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #006a53;
}
.x-btn.x-green-dark:hover,
.x-btn.x-green-dark:focus,
.x-btn.x-green-dark:active,
.x-btn.x-green-dark.x-active,
.x-btn.x-green-dark.x-disabled,
.x-btn.x-green-dark[disabled] {
  color: #fff;
  background-color: #00513f;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-red {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #a34c4c;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#a34c4c), to(#a34c4c));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #a34c4c 0%, #a34c4c 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #a34c4c 0%, #a34c4c 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #a34c4c 0%, #a34c4c 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffa34c4c', endColorstr='#ffa34c4c', GradientType=0);
  border-color: #a34c4c #a34c4c #6f3434;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #a95151;
}
.x-btn.x-red:hover,
.x-btn.x-red:focus,
.x-btn.x-red:active,
.x-btn.x-red.x-active,
.x-btn.x-red.x-disabled,
.x-btn.x-red[disabled] {
  color: #fff;
  background-color: #a34c4c;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-black {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #444444;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#444444), to(#444444));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #444444 0%, #444444 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #444444 0%, #444444 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #444444 0%, #444444 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff444444', endColorstr='#ff444444', GradientType=0);
  border-color: #444444 #444444 #1e1e1e;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #4e4e4e;
}
.x-btn.x-black:hover,
.x-btn.x-black:focus,
.x-btn.x-black:active,
.x-btn.x-black.x-active,
.x-btn.x-black.x-disabled,
.x-btn.x-black[disabled] {
  color: #fff;
  background-color: #444444;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-gray {
  color: #333333;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #969696;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#969696), to(#969696));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #969696 0%, #969696 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #969696 0%, #969696 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #969696 0%, #969696 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff969696', endColorstr='#ff969696', GradientType=0);
  border-color: #969696 #969696 #707070;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #4e4e4e;
}
.x-btn.x-gray:hover,
.x-btn.x-gray:focus,
.x-btn.x-gray:active,
.x-btn.x-gray.x-active,
.x-btn.x-gray.x-disabled,
.x-btn.x-gray[disabled] {
  color: #333333;
  background-color: #969696;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-yellow {
  color: #333333;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #f2d721;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#f2d721), to(#f2d721));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #f2d721 0%, #f2d721 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #f2d721 0%, #f2d721 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #f2d721 0%, #f2d721 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff2d721', endColorstr='#fff2d721', GradientType=0);
  border-color: #f2d721 #f2d721 #bca50b;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #c4bd44;
}
.x-btn.x-yellow:hover,
.x-btn.x-yellow:focus,
.x-btn.x-yellow:active,
.x-btn.x-yellow.x-active,
.x-btn.x-yellow.x-disabled,
.x-btn.x-yellow[disabled] {
  color: #333333;
  background-color: #f2d721;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-orange {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #c07833;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#c07833), to(#c07833));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #c07833 0%, #c07833 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #c07833 0%, #c07833 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #c07833 0%, #c07833 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffc07833', endColorstr='#ffc07833', GradientType=0);
  border-color: #c07833 #c07833 #845223;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #c4853e;
}
.x-btn.x-orange:hover,
.x-btn.x-orange:focus,
.x-btn.x-orange:active,
.x-btn.x-orange.x-active,
.x-btn.x-orange.x-disabled,
.x-btn.x-orange[disabled] {
  color: #fff;
  background-color: #c07833;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn.x-purple {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #a044a3;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#a044a3), to(#a044a3));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #a044a3 0%, #a044a3 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #a044a3 0%, #a044a3 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #a044a3 0%, #a044a3 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffa044a3', endColorstr='#ffa044a3', GradientType=0);
  border-color: #a044a3 #a044a3 #6b2d6d;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: 1px solid #a557a9;
}
.x-btn.x-purple:hover,
.x-btn.x-purple:focus,
.x-btn.x-purple:active,
.x-btn.x-purple.x-active,
.x-btn.x-purple.x-disabled,
.x-btn.x-purple[disabled] {
  color: #fff;
  background-color: #a044a3;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.x-btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.x-btn-block + .x-btn-block {
  margin-top: 0.8rem;
}
input[type="submit"].x-btn-block,
input[type="reset"].x-btn-block,
input[type="button"].x-btn-block {
  width: 100%;
}
button.x-btn,
input[type="submit"].x-btn {
  *padding-top: 3px;
  *padding-bottom: 3px;
}
button.x-btn::-moz-focus-inner,
input[type="submit"].x-btn::-moz-focus-inner {
  padding: 0;
  border: 0;
}
button.x-btn.x-large,
input[type="submit"].x-btn.x-large {
  *padding-top: 7px;
  *padding-bottom: 7px;
}
button.x-btn.x-small,
input[type="submit"].x-btn.x-small {
  *padding-top: 3px;
  *padding-bottom: 3px;
}
button.x-btn.x-mini,
input[type="submit"].x-btn.x-mini {
  *padding-top: 1px;
  *padding-bottom: 1px;
}
.x-btn-link,
.x-btn-link:active,
.x-btn-link[disabled] {
  background-color: transparent;
  background-image: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.x-btn-link {
  border-color: transparent;
  cursor: pointer;
  color: #008dc6;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.x-btn-link:hover,
.x-btn-link:focus {
  color: #007bad;
  text-decoration: underline;
  background-color: transparent;
}
.x-btn-link[disabled]:hover,
.x-btn-link[disabled]:focus {
  color: #333333;
  text-decoration: none;
}
.x-btn-group,
.x-btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.x-btn-group > .x-btn,
.x-btn-group-vertical > .x-btn {
  position: relative;
  float: left;
}
.x-btn-group > .x-btn:hover,
.x-btn-group-vertical > .x-btn:hover,
.x-btn-group > .x-btn:focus,
.x-btn-group-vertical > .x-btn:focus,
.x-btn-group > .x-btn:active,
.x-btn-group-vertical > .x-btn:active,
.x-btn-group > .x-btn.active,
.x-btn-group-vertical > .x-btn.active {
  z-index: 2;
}
.x-btn-group > .x-btn:focus,
.x-btn-group-vertical > .x-btn:focus {
  outline: none;
}
.x-btn-group .x-btn + .x-btn,
.x-btn-group .x-btn + .x-btn-group,
.x-btn-group .x-btn-group + .x-btn,
.x-btn-group .x-btn-group + .x-btn-group {
  margin-left: -1px;
}
.x-btn-toolbar:before,
.x-btn-toolbar:after {
  content: " ";
  display: table;
}
.x-btn-toolbar:after {
  clear: both;
}
.x-btn-toolbar .x-btn-group {
  float: left;
}
.x-btn-toolbar > .x-btn + .x-btn,
.x-btn-toolbar > .x-btn-group + .x-btn,
.x-btn-toolbar > .x-btn + .x-btn-group,
.x-btn-toolbar > .x-btn-group + .x-btn-group {
  margin-left: 5px;
}
.x-btn-group > .x-btn:not(:first-child):not(:last-child):not(.x-dropdown-toggle) {
  border-radius: 0;
}
.x-btn-group > .x-btn:first-child {
  margin-left: 0;
}
.x-btn-group > .x-btn:first-child:not(:last-child):not(.x-dropdown-toggle) {
  -webkit-border-top-right-radius: 0;
  -moz-border-radius-topright: 0;
  border-top-right-radius: 0;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomright: 0;
  border-bottom-right-radius: 0;
}
.x-btn-group > .x-btn:last-child:not(:first-child),
.x-btn-group > .x-dropdown-toggle:not(:first-child) {
  -webkit-border-top-left-radius: 0;
  -moz-border-radius-topleft: 0;
  border-top-left-radius: 0;
  -webkit-border-bottom-left-radius: 0;
  -moz-border-radius-bottomleft: 0;
  border-bottom-left-radius: 0;
}
.x-btn-group > .x-btn-group {
  float: left;
}
.x-btn-group > .x-btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.x-btn-group > .x-btn-group:first-child > .x-btn:last-child,
.x-btn-group > .x-btn-group:first-child > .x-dropdown-toggle {
  -webkit-border-top-right-radius: 0;
  -moz-border-radius-topright: 0;
  border-top-right-radius: 0;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomright: 0;
  border-bottom-right-radius: 0;
}
.x-btn-group > .x-btn-group:last-child > .x-btn:first-child {
  -webkit-border-top-left-radius: 0;
  -moz-border-radius-topleft: 0;
  border-top-left-radius: 0;
  -webkit-border-bottom-left-radius: 0;
  -moz-border-radius-bottomleft: 0;
  border-bottom-left-radius: 0;
}
.x-btn-group .x-dropdown-toggle:active,
.x-btn-group.open .x-dropdown-toggle {
  outline: 0;
}
.x-btn-group > .x-btn + .x-dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px;
}
.x-btn-group > .btn-lg + .x-dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
}
.x-btn-group.open .x-dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.x-btn-group-vertical > .x-btn,
.x-btn-group-vertical > .x-btn-group {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.x-btn-group-vertical > .x-btn-group:before,
.x-btn-group-vertical > .x-btn-group:after {
  content: " ";
  display: table;
}
.x-btn-group-vertical > .x-btn-group:after {
  clear: both;
}
.x-btn-group-vertical > .x-btn-group > .btn {
  float: none;
}
.x-btn-group-vertical > .x-btn + .x-btn,
.x-btn-group-vertical > .x-btn + .x-btn-group,
.x-btn-group-vertical > .x-btn-group + .x-btn,
.x-btn-group-vertical > .x-btn-group + .x-btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.x-btn-group-vertical > .x-btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.x-btn-group-vertical > .x-btn:first-child:not(:last-child) {
  border-top-right-radius: 0.25rem;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomright: 0;
  border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius: 0;
  -moz-border-radius-bottomleft: 0;
  border-bottom-left-radius: 0;
}
.x-btn-group-vertical > .x-btn:last-child:not(:first-child) {
  border-bottom-left-radius: 0.25rem;
  -webkit-border-top-right-radius: 0;
  -moz-border-radius-topright: 0;
  border-top-right-radius: 0;
  -webkit-border-top-left-radius: 0;
  -moz-border-radius-topleft: 0;
  border-top-left-radius: 0;
}
.x-btn-group-vertical > .x-btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.x-btn-group-vertical > .x-btn-group:first-child > .x-btn:last-child,
.x-btn-group-vertical > .x-btn-group:first-child > .x-dropdown-toggle {
  -webkit-border-bottom-right-radius: 0;
  -moz-border-radius-bottomright: 0;
  border-bottom-right-radius: 0;
  -webkit-border-bottom-left-radius: 0;
  -moz-border-radius-bottomleft: 0;
  border-bottom-left-radius: 0;
}
.x-btn-group-vertical > .x-btn-group:last-child > .x-btn:first-child {
  -webkit-border-top-right-radius: 0;
  -moz-border-radius-topright: 0;
  border-top-right-radius: 0;
  -webkit-border-top-left-radius: 0;
  -moz-border-radius-topleft: 0;
  border-top-left-radius: 0;
}
.x-btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.x-btn-group-justified .btn {
  float: none;
  display: table-cell;
  width: 1%;
}
[data-toggle="buttons"] > .btn > input[type="radio"],
[data-toggle="buttons"] > .btn > input[type="checkbox"] {
  display: none;
}
.x-caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px solid #555555;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  content: "";
}
.x-dropdown {
  display: inline-block;
  position: relative;
}
.x-dropdown-toggle:focus {
  outline: 0;
}
.x-dropdown-toggle .x-caret {
  margin-left: 0;
}
.x-dropdown-menu {
  position: absolute;
  top: 100%;
  left: -1px;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 16rem;
  padding: 0 0;
  margin: 0 0 0;
  list-style: none;
  font-family: "Droid Sans";
  background-color: #fff;
  border: 1px solid #c3c3c3;
  background-clip: padding-box;
}
.x-dropdown-menu .divider {
  height: 1px;
  margin: -0.09rem 1px;
  overflow: hidden;
  background-color: #e5e5e5;
  border-bottom: 1px solid #fff;
}
.x-dropdown-menu > li > a {
  display: block;
  padding: 5px 10px;
  clear: both;
  border-top: 1px solid #fff;
  border-bottom: 1px solid #e4e4e4;
  font-weight: normal;
  line-height: 1.82rem;
  color: #6a6a6a;
  background: #fdfdfd;
  background-color: #fafafa;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#fdfdfd), to(#f5f5f5));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #fdfdfd 0%, #f5f5f5 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #fdfdfd 0%, #f5f5f5 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #fdfdfd 0%, #f5f5f5 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fffdfdfd', endColorstr='#fff5f5f5', GradientType=0);
  white-space: nowrap;
}
.x-dropdown-menu.x-open {
  display: block;
}
.x-dropdown-menu > li > a:hover,
.x-dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #008dc6;
  background: #f5f5f5;
}
.x-dropdown-menu > .active > a,
.x-dropdown-menu > .active > a:hover,
.x-dropdown-menu > .active > a:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background: #f5f5f5;
}
.x-dropdown-menu > .disabled > a,
.x-dropdown-menu > .disabled > a:hover,
.x-dropdown-menu > .disabled > a:focus {
  color: #999999;
}
.x-dropdown-menu > .disabled > a:hover,
.x-dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.x-dropdown-header {
  display: block;
  padding: 3px 20px;
  line-height: 1.82rem;
  color: #999999;
}
.x-dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.x-dropup .x-caret {
  border-top: 0;
  border-bottom: 4px solid #555555;
  content: "";
}
.x-dropup .x-dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 1px;
}
.x-btn .x-caret {
  border-top-color: #555555;
  border-bottom-color: #555555;
}
.x-btn.x-blue .x-caret,
.x-btn.x-red .x-caret,
.x-btn.x-green .x-caret,
.x-btn.x-black .x-caret,
.x-btn.x-ligthblue .x-caret,
.x-btn.x-yellow .x-caret,
.x-btn.x-orange .x-caret,
.x-btn.x-purple .x-caret {
  border-top-color: #fff;
  border-bottom-color: #fff;
}
.x-growl {
  position: fixed;
  z-index: 1100;
  float: right;
  top: 7rem;
  right: 1rem;
  width: 25rem;
  font-size: 1.3rem;
}
.x-growl-item.ng-enter,
.x-growl-item.ng-leave {
  -webkit-transition: 0.5s linear all;
  -moz-transition: 0.5s linear all;
  -o-transition: 0.5s linear all;
  transition: 0.5s linear all;
}
.x-growl-item.ng-enter,
.x-growl-item.ng-leave.ng-leave-active {
  opacity: 0;
}
.x-growl-item.ng-leave,
.x-growl-item.ng-enter.ng-enter-active {
  opacity: 1;
}
.x-label {
  display: inline;
  padding: 0.2rem 0.3rem;
  font-size: 90%;
  font-weight: bold;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  -webkit-border-radius: 0.25rem;
  -moz-border-radius: 0.25rem;
  border-radius: 0.25rem;
}
.x-label {
  background-color: #999999;
}
.x-label.x-blue {
  background-color: #0074d7;
}
.x-label.x-red {
  background-color: #9d261d;
}
.x-label.x-green {
  background-color: #46a546;
}
.x-label.x-black {
  background-color: #000000;
}
.x-label.x-blue-light {
  background-color: #6db5d5;
}
.x-label.x-yellow {
  background-color: #ffc40d;
}
.x-label.x-orange {
  background-color: #f89406;
}
.x-label.x-purple {
  background-color: #7a43b6;
}
.x-modal-open {
  overflow: hidden;
}
.x-modal {
  display: block;
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  z-index: 1060;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.x-modal.fade .x-modal-dialog {
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}
.x-modal.in .x-modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
}
.x-modal-dialog {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: auto;
  padding: 10px;
}
.x-modal-content {
  position: relative;
  background: #f7f7f7;
  border: 1px solid #ffffff;
  border-radius: 0.25rem;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  font-size: 1.3rem;
  outline: 0;
}
.x-modal-content > header {
  border-bottom: 1px solid #ffffff;
  padding: 0.6rem 1rem;
  padding-right: 2em;
  -webkit-box-shadow: 0 1px 0 #fff;
  -moz-box-shadow: 0 1px 0 #fff;
  box-shadow: 0 1px 0 #fff;
  text-shadow: 0 1px #fff;
  -webkit-border-top-right-radius: 0.25rem;
  -moz-border-radius-topright: 0.25rem;
  border-top-right-radius: 0.25rem;
  -webkit-border-top-left-radius: 0.25rem;
  -moz-border-radius-topleft: 0.25rem;
  border-top-left-radius: 0.25rem;
  background-color: #f2f2f2;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#f8f8f8), to(#e8e8e8));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #f8f8f8 0%, #e8e8e8 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #f8f8f8 0%, #e8e8e8 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #f8f8f8 0%, #e8e8e8 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff8f8f8', endColorstr='#ffe8e8e8', GradientType=0);
  color: #636363;
  font-size: 120%;
  font-weight: bold;
}
.x-modal-content > header .x-close {
  margin-top: -2px;
}
.x-modal-content > section {
  position: relative;
  border-top: 1px solid #ffffff;
  border-bottom: 1px solid #ffffff;
  padding: 0.6rem 1rem;
}
.x-modal-content > footer {
  border-top: 1px solid #ffffff;
  padding: 0.6rem 1rem;
  text-align: right;
}
.x-modal-content > footer:before,
.x-modal-content > footer:after {
  content: " ";
  display: table;
}
.x-modal-content > footer:after {
  clear: both;
}
.x-modal-content > footer .x-btn + .x-btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.x-modal-content > footer .x-btn-group .x-btn + .x-btn {
  margin-left: -1px;
}
.x-modal-content > footer .x-btn-block + .x-btn-block {
  margin-left: 0;
}
.x-modal-backdrop {
  position: fixed;
  z-index: 1050;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: #000;
}
.x-modal-backdrop.fade {
  opacity: 0;
  filter: alpha(opacity=0);
}
.x-modal-backdrop.in {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
@media screen and (min-width: 60.1rem) {
  .x-modal-dialog {
    width: 60rem;
    padding-top: 1.82rem;
    padding-bottom: 1.82rem;
  }
  .x-modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
}
.x-panel {
  display: block;
  margin-bottom: 1rem;
  border: 1px solid #ffffff;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  padding: 0 !important;
  -webkit-box-shadow: 0;
  -moz-box-shadow: 0;
  box-shadow: 0;
  background: #f7f7f7;
}
.x-panel > header {
  display: table;
  width: 100%;
  border-bottom: 1px solid #ffffff;
  -webkit-box-shadow: 0;
  -moz-box-shadow: 0;
  box-shadow: 0;
  -webkit-border-top-right-radius: 0;
  -moz-border-radius-topright: 0;
  border-top-right-radius: 0;
  -webkit-border-top-left-radius: 0;
  -moz-border-radius-topleft: 0;
  border-top-left-radius: 0;
  background: none;
  background-color: #d2d2d2;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#d2d2d2), to(#d2d2d2));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #d2d2d2 0%, #d2d2d2 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #d2d2d2 0%, #d2d2d2 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #d2d2d2 0%, #d2d2d2 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd2d2d2', endColorstr='#ffd2d2d2', GradientType=0);
  color: #ffffff;
}
.x-panel > header > h1,
.x-panel > header > h2,
.x-panel > header > h3 {
  display: table-cell;
  margin: 0;
  padding: 0.6rem 1rem;
  color: #ffffff;
  font-size: 120%;
  font-weight: bold;
  text-shadow: 0;
}
.x-panel > header > .x-toolbar {
  display: table-cell;
  vertical-align: middle;
  padding-left: 0.6rem;
  font-family: "Droid Sans";
  width: 1px;
  white-space: nowrap;
}
.x-panel > header > .x-toolbar .x-btn {
  margin-right: 0.5rem;
  padding: 0.2rem 0.5rem;
  font-size: 90%;
}
.x-panel > header > .x-toolbar .x-tool {
  display: inline-block;
  margin-right: 1rem;
  color: #999999;
  font-size: 90%;
  text-decoration: none;
  cursor: pointer;
}
.x-panel > header > .x-toolbar .x-tool:hover {
  color: #008dc6;
}
.x-panel > header > .x-toolbar .x-tool i[class*="icon-"] {
  vertical-align: middle;
}
.x-panel > header > .x-toolbar .x-dropdown-menu {
  left: auto;
  right: -1px;
}
.x-panel > header > ul.x-toolbar {
  list-style: none outside none;
  margin: 0;
  padding: 0;
}
.x-panel > header > ul.x-toolbar > li {
  float: left;
}
.x-panel > section {
  border-top: 1px solid #ffffff;
  border-bottom: 1px solid #ffffff;
}
.x-panel > section:before,
.x-panel > section:after {
  content: " ";
  display: table;
}
.x-panel > section:after {
  clear: both;
}
.x-tabs {
  margin-right: auto;
  margin-left: auto;
}
.x-tabs:before,
.x-tabs:after {
  content: " ";
  display: table;
}
.x-tabs:after {
  clear: both;
}
.x-nav-tabs,
.x-nav-pills {
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.x-nav-tabs:before,
.x-nav-pills:before,
.x-nav-tabs:after,
.x-nav-pills:after {
  content: " ";
  display: table;
}
.x-nav-tabs:after,
.x-nav-pills:after {
  clear: both;
}
.x-nav-tabs > li,
.x-nav-pills > li {
  position: relative;
  display: block;
}
.x-nav-tabs > li > a,
.x-nav-pills > li > a {
  position: relative;
  display: block;
}
.x-nav-tabs > li > a:hover,
.x-nav-pills > li > a:hover,
.x-nav-tabs > li > a:focus,
.x-nav-pills > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.x-nav-tabs > li.x-disabled > a,
.x-nav-pills > li.x-disabled > a {
  color: #999999;
}
.x-nav-tabs > li.x-disabled > a:hover,
.x-nav-pills > li.x-disabled > a:hover,
.x-nav-tabs > li.x-disabled > a:focus,
.x-nav-pills > li.x-disabled > a:focus {
  color: #999999;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.x-nav-tabs.x-nav-justified,
.x-nav-pills.x-nav-justified {
  width: 100%;
}
.x-nav-tabs.x-nav-justified > li,
.x-nav-pills.x-nav-justified > li {
  float: none;
}
.x-nav-tabs.x-nav-justified > li > a,
.x-nav-pills.x-nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
@media (min-width: 48rem) {
  .x-nav-tabs.x-nav-justified > li,
  .x-nav-pills.x-nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .x-nav-tabs.x-nav-justified > li > a,
  .x-nav-pills.x-nav-justified > li > a {
    margin-bottom: 0;
  }
}
.x-nav-tabs {
  border-bottom: 1px solid #999999;
}
.x-nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.x-nav-tabs > li > a {
  margin-right: 4px;
  line-height: 1.82rem;
  border: 1px solid transparent;
  border-radius: 0.25rem 0.25rem 0 0;
  padding: 0.6rem 1rem;
}
.x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #999999;
}
.x-nav-tabs > li.x-active > a,
.x-nav-tabs > li.x-active > a:hover,
.x-nav-tabs > li.x-active > a:focus {
  color: #999999;
  background: #fff url('backgrounds/body.jpg') repeat-y center;
  border-color: #999999 #999999 transparent;
  cursor: default;
}
.x-nav-tabs.x-nav-justified {
  border-bottom: 0;
}
.x-nav-tabs.x-nav-justified > li > a {
  margin-right: 0;
  border-bottom: 1px solid #999999;
}
.x-nav-tabs.x-nav-justified > li.x-active > a,
.x-nav-tabs.x-nav-justified > li.x-active > a:hover,
.x-nav-tabs.x-nav-justified > li.x-active > a:focus {
  border-bottom: 1px solid transparent;
}
.x-nav-tabs.x-nav-left,
.x-nav-tabs.x-nav-right {
  border-bottom: none;
  padding-left: 0;
  padding-right: 0;
}
.x-nav-tabs.x-nav-left > li,
.x-nav-tabs.x-nav-right > li {
  float: none;
}
.x-nav-tabs.x-nav-left > li > a,
.x-nav-tabs.x-nav-right > li > a {
  margin-right: 0;
  margin-top: 4px;
  margin-bottom: 4px;
}
.x-nav-tabs.x-nav-left {
  border-right: 1px solid #999999;
}
.x-nav-tabs.x-nav-left > li {
  margin-right: -1px;
}
.x-nav-tabs.x-nav-left > li > a {
  border-radius: 0.25rem 0 0 0.25rem;
}
.x-nav-tabs.x-nav-left > li > a:hover {
  border-color: #555555 #999999 #555555 #555555;
}
.x-nav-tabs.x-nav-left > li.x-active > a,
.x-nav-tabs.x-nav-left > li.x-active > a:hover,
.x-nav-tabs.x-nav-left > li.x-active > a:focus {
  border-bottom-color: #999999;
  border-right-color: transparent;
}
.x-nav-tabs.x-nav-right {
  border-left: 1px solid #999999;
}
.x-nav-tabs.x-nav-right > li {
  margin-left: -1px;
}
.x-nav-tabs.x-nav-right > li > a {
  border-radius: 0 0.25rem 0.25rem 0;
}
.x-nav-tabs.x-nav-right > li > a:hover {
  border-color: #555555 #555555 #555555 #999999;
}
.x-nav-tabs.x-nav-right > li.x-active > a,
.x-nav-tabs.x-nav-right > li.x-active > a:hover,
.x-nav-tabs.x-nav-right > li.x-active > a:focus {
  border-bottom-color: #999999;
  border-left-color: transparent;
}
.x-panel .x-nav-tabs.x-nav-left > li {
  margin-left: 1rem;
}
.x-panel .x-nav-tabs.x-nav-right > li {
  margin-right: 1rem;
}
.x-panel .x-nav-tabs > li.x-active > a,
.x-panel .x-nav-tabs > li.x-active > a:hover,
.x-panel .x-nav-tabs > li.x-active > a:focus {
  background: #f7f7f7;
}
.x-panel > header > .x-nav-tabs {
  margin-top: 2px;
  border-bottom: 0;
}
.x-panel > header > .x-nav-tabs > li > a {
  color: #ffffff;
  font-size: 120%;
  font-weight: bold;
  text-shadow: 0;
}
.x-panel > header > .x-nav-tabs > li:first-child > a {
  margin-left: 4px;
}
.x-panel > header > .x-nav-tabs > li.x-active > a,
.x-panel > header > .x-nav-tabs > li.x-active > a:hover,
.x-panel > header > .x-nav-tabs > li.x-active > a:focus {
  border-top: 1px solid #ffffff;
  border-left: 1px solid #ffffff;
  border-right: 1px solid #ffffff;
}
.x-nav-pills > li {
  float: left;
  margin: 0 2px;
}
.x-nav-pills > li > a {
  border-radius: 0.25rem;
  padding: 0.6rem 1rem;
  background-color: #eeeeee;
}
.x-nav-pills > li > a,
.x-nav-pills > li > a:hover,
.x-nav-pills > li > a:focus {
  color: #008dc6;
}
.x-nav-pills > li > a:hover,
.x-nav-pills > li > a:focus {
  background-color: #009fe0;
  color: #eeeeee;
}
.x-nav-pills > li.x-active > a {
  background-color: #008dc6;
}
.x-nav-pills > li.x-active > a,
.x-nav-pills > li.x-active > a:hover,
.x-nav-pills > li.x-active > a:focus {
  color: #eeeeee;
}
.x-panel .x-nav-pills > li.x-disabled > a,
.x-panel .x-nav-pills > li.x-disabled > a:hover,
.x-panel .x-nav-pills > li.x-disabled > a:focus {
  background: #f7f7f7;
}
.x-tabs-content {
  padding-left: 0;
  padding-right: 0;
}
.x-tabs-content > .x-tab-panel {
  display: none;
  visibility: hidden;
}
.x-tabs-content > .x-tab-panel.x-active {
  display: block;
  visibility: visible;
}
.x-tooltip {
  position: absolute;
  z-index: 1030;
  display: block;
  visibility: visible;
  font-family: "Droid Sans";
  font-size: 0.75rem;
  line-height: 1.82rem;
}
.x-tooltip.top {
  margin-top: -3px;
  padding: 6px 0;
}
.x-tooltip.right {
  margin-left: 3px;
  padding: 0 6px;
}
.x-tooltip.bottom {
  margin-top: 3px;
  padding: 6px 0;
}
.x-tooltip.left {
  margin-left: -3px;
  padding: 0 6px;
}
.x-tooltip-inner {
  max-width: 250px;
  padding: 0.4rem 0.6rem;
  color: white;
  text-align: center;
  text-decoration: none;
  background-color: #333333;
  -webkit-border-radius: 0.38rem;
  -moz-border-radius: 0.38rem;
  border-radius: 0.38rem;
}
.x-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.x-tooltip.top .x-tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -6px;
  border-width: 6px 6px 0;
  border-top-color: #333333;
}
.x-tooltip.top-left .x-tooltip-arrow {
  bottom: 0;
  left: 6px;
  border-width: 6px 6px 0;
  border-top-color: #333333;
}
.x-tooltip.top-right .x-tooltip-arrow {
  bottom: 0;
  right: 6px;
  border-width: 6px 6px 0;
  border-top-color: #333333;
}
.x-tooltip.right .x-tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -6px;
  border-width: 6px 6px 6px 0;
  border-right-color: #333333;
}
.x-tooltip.left .x-tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -6px;
  border-width: 6px 0 6px 6px;
  border-left-color: #333333;
}
.x-tooltip.bottom .x-tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -6px;
  border-width: 0 6px 6px;
  border-bottom-color: #333333;
}
.x-tooltip.bottom-left .x-tooltip-arrow {
  top: 0;
  left: 6px;
  border-width: 0 6px 6px;
  border-bottom-color: #333333;
}
.x-tooltip.bottom-right .x-tooltip-arrow {
  top: 0;
  right: 6px;
  border-width: 0 6px 6px;
  border-bottom-color: #333333;
}
.x-close {
  float: right;
  position: relative;
  right: -1em;
  color: inherit;
  font-size: 1.5em;
  font-weight: bolder;
  cursor: pointer;
  opacity: 40;
  filter: alpha(opacity=4000);
}
.x-close:hover {
  opacity: 100;
  filter: alpha(opacity=10000);
}
#x-login-panel {
  width: 20rem;
  max-width: 20rem;
  margin: 4rem auto;
  font-size: 1.3rem;
}
#x-login-panel input[type="text"],
#x-login-panel input[type="password"] {
  padding: 7px 27px 7px 7px;
}
.x-bar-container {
  height: 4px;
  background: #e6e6e6;
  border: 1px solid #ccc;
  -webkit-box-shadow: 0 1px 0 #ddd inset;
  -moz-box-shadow: 0 1px 0 #ddd inset;
  box-shadow: 0 1px 0 #ddd inset;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
}
.x-bar-grey,
.x-bar-blue,
.x-bar-orange,
.x-bar-black,
.x-bar-green,
.x-bar-red {
  height: 4px;
  width: 0;
  margin: -1px 0 0 -1px;
  border: 1px solid;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  background: url('elements/progress-bar.png') repeat-x;
}
.x-bar-green {
  background-position: 0 0;
  border-color: #97af32;
}
.x-bar-blue {
  background-position: 0 -5px;
  border-color: #5f9fce;
}
.x-bar-orange {
  background-position: 0 -10px;
  border-color: #d28346;
}
.x-bar-black {
  background-position: 0 -15px;
  border-color: #4c4c4c;
}
.x-bar-grey {
  background-position: 0 -20px;
  border-color: #7b7b7b;
}
.x-bar-red {
  background-position: 0 -25px;
  border-color: #c8514e;
}
#x-widget-user {
  display: inline-block;
  -webkit-border-radius: 0.25rem;
  -moz-border-radius: 0.25rem;
  border-radius: 0.25rem;
  margin: 1rem 2rem;
  padding: 0.5rem 0.7rem;
  background: none;
  font-size: 1.3rem;
  color: #6a6a6a;
  cursor: pointer;
}
#x-widget-user:hover {
  color: #008dc6;
  text-decoration: none;
}
#x-widget-user img {
  width: 3rem;
  height: 3rem;
  -webkit-border-radius: 0.12rem;
  -moz-border-radius: 0.12rem;
  border-radius: 0.12rem;
  -webkit-box-shadow: 0 1px 3px #1e1e1e;
  -moz-box-shadow: 0 1px 3px #1e1e1e;
  box-shadow: 0 1px 3px #1e1e1e;
  vertical-align: middle;
}
[ng-cloak],
[data-ng-cloak],
[x-ng-cloak],
.ng-cloak,
.x-ng-cloak {
  display: none !important;
}
a[ng-click] {
  cursor: pointer;
}
li[xng-tab] a,
li[xng-router-tab] a {
  cursor: pointer;
}
body > header {
  min-height: initial;
}
main {
  position: relative;
}
.x-btn {
  background-color: inherit;
  background-image: url('backgrounds/blank.gif');
}
#x-login-panel {
  width: 30rem;
  max-width: 30rem;
}
.x-nav-tabs > li > a {
  font-weight: bold;
}
#x-nav-top {
  padding: 0.5rem 4rem;
}
#x-nav-top > ul > li a:hover {
  font-weight: bold;
}
#x-nav-top > ul > li a::before {
  display: block;
  content: attr(title);
  font-weight: bold;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}
#x-nav-top > ul > li:not(:last-child) {
  border-right: 1px solid #D5D5D5;
}
#x-nav-top a {
  display: inline-block;
  padding: 0.6rem 1.2rem;
  font-size: 1.125em;
}
#x-nav-top li.area-incentivazioni a:hover {
  color: #e8501d !important;
}
#x-nav-top li.area-incentivazioni,
body.area-incentivazioni main > header {
  color: #e8501d !important;
}
body.area-incentivazioni main > header {
  font-weight: 400;
}
body.area-incentivazioni main > header i {
  background-color: #e8501d !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-gare a:hover {
  color: #f5a019 !important;
}
#x-nav-top li.area-gare,
body.area-gare main > header {
  color: #f5a019 !important;
}
body.area-gare main > header {
  font-weight: 400;
}
body.area-gare main > header i {
  background-color: #f5a019 !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-formaz a:hover {
  color: #006a53 !important;
}
#x-nav-top li.area-formaz,
body.area-formaz main > header {
  color: #006a53 !important;
}
body.area-formaz main > header {
  font-weight: 400;
}
body.area-formaz main > header i {
  background-color: #006a53 !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-gest a:hover {
  color: #c7d300 !important;
}
#x-nav-top li.area-gest,
body.area-gest main > header {
  color: #c7d300 !important;
}
body.area-gest main > header {
  font-weight: 400;
}
body.area-gest main > header i {
  background-color: #c7d300 !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-marketing a:hover {
  color: #3399cc !important;
}
#x-nav-top li.area-marketing,
body.area-marketing main > header {
  color: #3399cc !important;
}
body.area-marketing main > header {
  font-weight: 400;
}
body.area-marketing main > header i {
  background-color: #3399cc !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-news a:hover {
  color: #952456 !important;
}
#x-nav-top li.area-news,
body.area-news main > header {
  color: #952456 !important;
}
body.area-news main > header {
  font-weight: 400;
}
body.area-news main > header i {
  background-color: #952456 !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-download a:hover {
  color: #003366 !important;
}
#x-nav-top li.area-download,
body.area-download main > header {
  color: #003366 !important;
}
body.area-download main > header {
  font-weight: 400;
}
body.area-download main > header i {
  background-color: #003366 !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-helpdesk a:hover {
  color: #a69bc8 !important;
}
#x-nav-top li.area-helpdesk,
body.area-helpdesk main > header {
  color: #a69bc8 !important;
}
body.area-helpdesk main > header {
  font-weight: 400;
}
body.area-helpdesk main > header i {
  background-color: #a69bc8 !important;
  font-size: 2.2rem;
}
#x-nav-top li.area-iniz-comm a:hover {
  color: #918AD3 !important;
}
#x-nav-top li.area-iniz-comm,
body.area-iniz-comm main > header {
  color: #918AD3 !important;
}
body.area-iniz-comm main > header {
  font-weight: 400;
}
body.area-iniz-comm main > header i {
  background-color: #918AD3 !important;
  font-size: 2.2rem;
}
@media (max-width: 48rem) {
  /*body > header {
		position: fixed;
		z-index: 10;
	}*/
  /*#x-nav-top {
		padding: 15px 0 0 25px;
		position: fixed;
		top: 77px;
		width: 100%;
		height: 100%;
		background-color: rgba(255, 255, 255, 0.9);
		z-index: 10;
	}*/
  #x-nav-top {
    padding: 15px 0 0 25px;
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 10;
  }
  #x-nav-top > ul > li {
    margin-bottom: 15px;
  }
  #x-nav-top li.area-incentivazioni a i,
  body.area-download main > header i {
    background-color: #e8501d !important;
  }
  #x-nav-top li.area-gare a i,
  body.area-gare main > header i {
    background-color: #f5a019 !important;
  }
  #x-nav-top li.area-formaz a i,
  body.area-formaz main > header i {
    background-color: #006a53 !important;
  }
  #x-nav-top li.area-gest a i,
  body.area-gest main > header i {
    background-color: #c7d300 !important;
  }
  #x-nav-top li.area-marketing a i,
  body.area-marketing main > header i {
    background-color: #3399cc !important;
  }
  #x-nav-top li.area-news a i,
  body.area-news main > header i {
    background-color: #952456 !important;
  }
  #x-nav-top li.area-download a i,
  body.area-download main > header i {
    background-color: #003366 !important;
  }
  #x-nav-top li.area-helpdesk a i,
  body.area-helpdesk main > header i {
    background-color: #a69bc8 !important;
  }
  #x-nav-top li.area-iniz-comm a i,
  body.area-iniz-comm main > header i {
    background-color: #918AD3 !important;
  }
}
.grid-cell {
  margin-bottom: 2.5rem;
}
.grid-box {
  display: block;
  min-height: 110px;
  margin-bottom: 2.5rem;
  border-bottom: 1rem solid #d2d2d2;
  padding: 1.5rem 1rem;
}
.grid-box,
.grid-box:hover,
.grid-box:focus {
  color: #626262;
  text-decoration: none;
}
.grid-box strong {
  display: inline-block;
  font-size: 2rem;
  line-height: 2.2rem;
}
.grid-box small {
  font-size: 1.3rem;
}
.x-panel > header .x-nav-tabs > li > a {
  color: #fff;
  padding: 0.3rem 1rem;
}
.x-panel > header .x-nav-tabs > li > a:hover {
  color: #555555;
}
.x-panel > header .x-nav-tabs > li.x-active > a,
.x-panel > header .x-nav-tabs > li.x-active > a:hover,
.x-panel > header .x-nav-tabs > li.x-active > a:focus {
  color: #555555;
}
body.area-incentivazioni #x-nav-top > ul > li.area-incentivazioni > a {
  font-weight: bold;
  color: #e8501d;
}
body.area-incentivazioni #x-nav-top {
  border-color: #e8501d;
}
body.area-incentivazioni .x-panel {
  border: 1px solid #e8501d;
}
body.area-incentivazioni .x-panel > header {
  background-color: #e8501d;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#e8501d), to(#e8501d));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #e8501d 0%, #e8501d 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #e8501d 0%, #e8501d 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #e8501d 0%, #e8501d 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe8501d', endColorstr='#ffe8501d', GradientType=0);
}
body.area-incentivazioni .x-nav-tabs {
  border-bottom: 1px solid #e8501d;
}
body.area-incentivazioni .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #e8501d;
}
body.area-incentivazioni .x-nav-tabs > li.x-active > a,
body.area-incentivazioni .x-nav-tabs > li.x-active > a:hover,
body.area-incentivazioni .x-nav-tabs > li.x-active > a:focus {
  border-color: #e8501d #e8501d transparent;
}
body.area-incentivazioni .grid-box {
  min-height: 18rem;
  background-color: #fef5f2;
  background-image: url("//static.portaleagendo.it/img/incentivazioni/box-bg.png");
  background: url("//static.portaleagendo.it/img/incentivazioni/box-bg.png"), -webkit-gradient(linear, 50% top, 90% top, from(#ffffff), to(#fce5de));
  background: url("//static.portaleagendo.it/img/incentivazioni/box-bg.png"), -webkit-linear-gradient(left, color-stop(#ffffff 50%), color-stop(#fce5de 90%));
  background: url("//static.portaleagendo.it/img/incentivazioni/box-bg.png"), -moz-linear-gradient(left, #ffffff 50%, #fce5de 90%);
  background: url("//static.portaleagendo.it/img/incentivazioni/box-bg.png"), linear-gradient(to right, #ffffff 50%, #fce5de 90%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fffce5de', GradientType=1);
  border-bottom-color: #e8501d;
}
body.area-incentivazioni .grid-box i {
  background-color: #e8501d;
}
body.area-incentivazioni .grid-box img {
  display: inline-block;
  float: left;
  margin-right: 0.6rem;
  vertical-align: top;
}
body.area-incentivazioni .grid-box strong {
  display: inline;
}
body.area-gare #x-nav-top > ul > li.area-gare > a {
  font-weight: bold;
  color: #f5a019;
}
body.area-gare #x-nav-top {
  border-color: #f5a019;
}
body.area-gare .x-panel {
  border: 1px solid #f5a019;
}
body.area-gare .x-panel > header {
  background-color: #f5a019;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#f5a019), to(#f5a019));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #f5a019 0%, #f5a019 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #f5a019 0%, #f5a019 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #f5a019 0%, #f5a019 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff5a019', endColorstr='#fff5a019', GradientType=0);
}
body.area-gare .x-nav-tabs {
  border-bottom: 1px solid #f5a019;
}
body.area-gare .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #f5a019;
}
body.area-gare .x-nav-tabs > li.x-active > a,
body.area-gare .x-nav-tabs > li.x-active > a:hover,
body.area-gare .x-nav-tabs > li.x-active > a:focus {
  border-color: #f5a019 #f5a019 transparent;
}
body.area-gare .grid-box {
  min-height: 18rem;
  background-color: #fef5f2;
  background-image: url("//static.portaleagendo.it/img/gare/box-bg.png");
  background: url("//static.portaleagendo.it/img/gare/box-bg.png"), -webkit-gradient(linear, 50% top, 90% top, from(#ffffff), to(#fce5de));
  background: url("//static.portaleagendo.it/img/gare/box-bg.png"), -webkit-linear-gradient(left, color-stop(#ffffff 50%), color-stop(#fce5de 90%));
  background: url("//static.portaleagendo.it/img/gare/box-bg.png"), -moz-linear-gradient(left, #ffffff 50%, #fce5de 90%);
  background: url("//static.portaleagendo.it/img/gare/box-bg.png"), linear-gradient(to right, #ffffff 50%, #fce5de 90%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#fffce5de', GradientType=1);
  border-bottom-color: #f5a019;
}
body.area-gare .grid-box i {
  background-color: #f5a019;
}
body.area-gare .grid-box img {
  display: inline-block;
  float: left;
  margin-right: 0.6rem;
  vertical-align: top;
}
body.area-gare .grid-box strong {
  display: inline;
}
body.area-formaz #x-nav-top > ul > li.area-formaz > a {
  font-weight: bold;
  color: #006a53;
}
body.area-formaz #x-nav-top {
  border-color: #006a53;
}
body.area-formaz .x-panel {
  border: 1px solid #006a53;
}
body.area-formaz .x-panel > header {
  background-color: #006a53;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#006a53), to(#006a53));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #006a53 0%, #006a53 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #006a53 0%, #006a53 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #006a53 0%, #006a53 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff006a53', endColorstr='#ff006a53', GradientType=0);
}
body.area-formaz .x-nav-tabs {
  border-bottom: 1px solid #006a53;
}
body.area-formaz .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #006a53;
}
body.area-formaz .x-nav-tabs > li.x-active > a,
body.area-formaz .x-nav-tabs > li.x-active > a:hover,
body.area-formaz .x-nav-tabs > li.x-active > a:focus {
  border-color: #006a53 #006a53 transparent;
}
body.area-gest #x-nav-top > ul > li.area-gest > a {
  font-weight: bold;
  color: #c7d300;
}
body.area-gest #x-nav-top {
  border-color: #c7d300;
}
body.area-gest .x-panel {
  border: 1px solid #c7d300;
}
body.area-gest .x-panel > header {
  background-color: #c7d300;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#c7d300), to(#c7d300));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #c7d300 0%, #c7d300 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #c7d300 0%, #c7d300 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #c7d300 0%, #c7d300 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffc7d300', endColorstr='#ffc7d300', GradientType=0);
}
body.area-gest .x-nav-tabs {
  border-bottom: 1px solid #c7d300;
}
body.area-gest .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #c7d300;
}
body.area-gest .x-nav-tabs > li.x-active > a,
body.area-gest .x-nav-tabs > li.x-active > a:hover,
body.area-gest .x-nav-tabs > li.x-active > a:focus {
  border-color: #c7d300 #c7d300 transparent;
}
body.area-gest .grid-box {
  background-color: #e7e7de;
  border-bottom-color: #c7d300;
}
body.area-gest .grid-box i {
  background-color: #c7d300;
  display: inline-block;
  float: left;
  margin-bottom: 2rem;
  margin-right: 1.2rem;
}
body.area-marketing #x-nav-top > ul > li.area-marketing > a {
  font-weight: bold;
  color: #3399cc;
}
body.area-marketing #x-nav-top {
  border-color: #3399cc;
}
body.area-marketing .x-panel {
  border: 1px solid #3399cc;
}
body.area-marketing .x-panel > header {
  background-color: #3399cc;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#3399cc), to(#3399cc));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #3399cc 0%, #3399cc 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #3399cc 0%, #3399cc 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #3399cc 0%, #3399cc 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff3399cc', endColorstr='#ff3399cc', GradientType=0);
}
body.area-marketing .x-nav-tabs {
  border-bottom: 1px solid #3399cc;
}
body.area-marketing .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #3399cc;
}
body.area-marketing .x-nav-tabs > li.x-active > a,
body.area-marketing .x-nav-tabs > li.x-active > a:hover,
body.area-marketing .x-nav-tabs > li.x-active > a:focus {
  border-color: #3399cc #3399cc transparent;
}
body.area-news #x-nav-top > ul > li.area-news > a {
  font-weight: bold;
  color: #952456;
}
body.area-news #x-nav-top {
  border-color: #952456;
}
body.area-news .x-panel {
  border: 1px solid #952456;
}
body.area-news .x-panel > header {
  background-color: #952456;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#952456), to(#952456));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #952456 0%, #952456 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #952456 0%, #952456 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #952456 0%, #952456 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff952456', endColorstr='#ff952456', GradientType=0);
}
body.area-news .x-nav-tabs {
  border-bottom: 1px solid #952456;
}
body.area-news .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #952456;
}
body.area-news .x-nav-tabs > li.x-active > a,
body.area-news .x-nav-tabs > li.x-active > a:hover,
body.area-news .x-nav-tabs > li.x-active > a:focus {
  border-color: #952456 #952456 transparent;
}
body.area-download #x-nav-top > ul > li.area-download > a {
  font-weight: bold;
  color: #003366;
}
body.area-download #x-nav-top {
  border-color: #003366;
}
body.area-download .x-panel {
  border: 1px solid #003366;
}
body.area-download .x-panel > header {
  background-color: #003366;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#003366), to(#003366));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #003366 0%, #003366 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #003366 0%, #003366 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #003366 0%, #003366 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff003366', endColorstr='#ff003366', GradientType=0);
}
body.area-download .x-nav-tabs {
  border-bottom: 1px solid #003366;
}
body.area-download .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #003366;
}
body.area-download .x-nav-tabs > li.x-active > a,
body.area-download .x-nav-tabs > li.x-active > a:hover,
body.area-download .x-nav-tabs > li.x-active > a:focus {
  border-color: #003366 #003366 transparent;
}
body.area-incentivazioni #x-nav-top > ul > li.area-incentivazioni > a {
  font-weight: bold;
  color: #e8501d;
}
body.area-incentivazioni #x-nav-top {
  border-color: #e8501d;
}
body.area-incentivazioni .x-panel {
  border: 1px solid #e8501d;
}
body.area-incentivazioni .x-panel > header {
  background-color: #e8501d;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#e8501d), to(#e8501d));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #e8501d 0%, #e8501d 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #e8501d 0%, #e8501d 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #e8501d 0%, #e8501d 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe8501d', endColorstr='#ffe8501d', GradientType=0);
}
body.area-incentivazioni .x-nav-tabs {
  border-bottom: 1px solid #e8501d;
}
body.area-incentivazioni .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #e8501d;
}
body.area-incentivazioni .x-nav-tabs > li.x-active > a,
body.area-incentivazioni .x-nav-tabs > li.x-active > a:hover,
body.area-incentivazioni .x-nav-tabs > li.x-active > a:focus {
  border-color: #e8501d #e8501d transparent;
}
body.area-helpdesk #x-nav-top > ul > li.area-helpdesk > a {
  font-weight: bold;
  color: #a69bc8;
}
body.area-helpdesk #x-nav-top {
  border-color: #a69bc8;
}
body.area-helpdesk .x-panel {
  border: 1px solid #a69bc8;
}
body.area-helpdesk .x-panel > header {
  background-color: #a69bc8;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#a69bc8), to(#a69bc8));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #a69bc8 0%, #a69bc8 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #a69bc8 0%, #a69bc8 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #a69bc8 0%, #a69bc8 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffa69bc8', endColorstr='#ffa69bc8', GradientType=0);
}
body.area-helpdesk .x-nav-tabs {
  border-bottom: 1px solid #a69bc8;
}
body.area-helpdesk .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #a69bc8;
}
body.area-helpdesk .x-nav-tabs > li.x-active > a,
body.area-helpdesk .x-nav-tabs > li.x-active > a:hover,
body.area-helpdesk .x-nav-tabs > li.x-active > a:focus {
  border-color: #a69bc8 #a69bc8 transparent;
}
body.area-iniz-comm #x-nav-top > ul > li.area-iniz-comm > a {
  font-weight: bold;
  color: #918AD3;
}
body.area-iniz-comm #x-nav-top {
  border-color: #918AD3;
}
body.area-iniz-comm .x-panel {
  border: 1px solid #918AD3;
}
body.area-iniz-comm .x-panel > header {
  background-color: #918ad3;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#918AD3), to(#918AD3));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #918AD3 0%, #918AD3 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #918AD3 0%, #918AD3 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #918AD3 0%, #918AD3 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff918ad3', endColorstr='#ff918ad3', GradientType=0);
}
body.area-iniz-comm .x-nav-tabs {
  border-bottom: 1px solid #918AD3;
}
body.area-iniz-comm .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #918AD3;
}
body.area-iniz-comm .x-nav-tabs > li.x-active > a,
body.area-iniz-comm .x-nav-tabs > li.x-active > a:hover,
body.area-iniz-comm .x-nav-tabs > li.x-active > a:focus {
  border-color: #918AD3 #918AD3 transparent;
}
body.area-iniz-comm .initiative-title {
  font-size: 32px;
  line-height: normal;
  font-weight: 400;
  color: #918AD3;
  padding: 0 15px 15px;
  margin: 0;
}
body.area-iniz-comm .downloads {
  font-size: 24px;
  line-height: normal;
  font-weight: 400;
  color: #918AD3;
  padding: 0 15px 15px;
  margin: 0;
}
body.area-iniz-comm .description {
  padding: 15px;
  font-size: 18px;
  line-height: 20px;
  margin: 0 0 20px;
}
body.area-iniz-comm img {
  image-rendering: -moz-crisp-edges;
  /* Firefox        */
  image-rendering: -o-crisp-edges;
  /* Opera          */
  image-rendering: -webkit-optimize-contrast;
  /* Safari         */
  image-rendering: optimize-contrast;
  /* CSS3 Proposed  */
  -ms-interpolation-mode: nearest-neighbor;
  /* IE8+           */
}
body.area-iniz-comm .iniz-btn {
  background-color: #918ad3;
  color: #FFFFFF;
  font-size: 16px;
  padding: 8px 15px;
  border-radius: 3px;
  transition: all 0.2s ease;
  border: 0;
  outline: none;
}
body.area-iniz-comm .iniz-btn:hover {
  background-color: #8664D3;
}
i[class*='icoagendo-menu-'] {
  text-align: center;
  padding: 0.6rem;
  border-radius: 100%;
  color: #ffffff !important;
}
body > header {
  font-size: 1.3rem;
}
#x-widget-user {
  cursor: default;
  display: inline-block;
  padding: 2px 10px;
  background-color: #fff;
  color: #006a53;
}
#x-widget-user:hover {
  color: #006a53;
}
.logo-groupama {
  float: left;
  margin: 1.8rem 0rem;
}
#x-nav-btn {
  font-size: 1.8rem;
}
body.dashboard section.news-grid {
  font-size: 120%;
  color: #626262;
}
body.dashboard section.news-grid .docs-date {
  font-weight: bold;
}
body.dashboard section.news-grid .docs-author {
  font-style: italic;
  padding-left: 1rem;
}
body.dashboard section.news-grid .docs-title {
  font-weight: bold;
}
body.dashboard section.news-grid .x-grid-row {
  margin: 0.6rem 0;
  padding: 0.6rem;
  border-left: 0.6rem solid #003366;
  background-color: #e6e6e6;
}
body.area-incentivazioni h1 {
  text-align: center;
  color: #646464;
  margin: 1rem auto !important;
}
body.area-incentivazioni .table-row-green td {
  background-color: #99f299 !important;
}
body.area-incentivazioni .table-row-green-light td {
  background-color: #e1ffcd !important;
}
body.area-incentivazioni .table-row-orange td {
  background-color: #ffcc00 !important;
}
body.area-incentivazioni .table-row-orange-light td {
  background-color: #ffe85b !important;
}
body.area-incentivazioni header.incentivazioni-2-gare {
  position: absolute;
  top: 0;
  right: 0;
  color: #333333 !important;
  font-size: 1.3rem;
  text-align: right;
  line-height: 1.2rem;
}
body.area-incentivazioni header.incentivazioni-2-gare div,
body.area-incentivazioni header.incentivazioni-2-gare img {
  float: left;
}
body.area-incentivazioni header.incentivazioni-2-gare div {
  margin-top: 1rem;
}
body.area-incentivazioni header.incentivazioni-2-gare img {
  margin-left: 1rem;
}
body.area-incentivazioni .incentivazioni-tutela {
  position: relative;
  display: block;
  padding: 1.2rem 0 0 0 !important;
  width: 10.6rem;
  height: 5.3rem;
  border-top-left-radius: 10.6rem;
  border-top-right-radius: 10.6rem;
  background-color: #e8501d;
  color: white !important;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
}
body.area-incentivazioni .incentivazioni-tutela i {
  margin: 0 0.2rem;
  font-size: 1.6rem;
}
body.area-incentivazioni header.incentivazioni-tutela {
  margin: auto;
}
.incentiv-card {
  margin-bottom: 2.5rem;
}
.incentiv-banner {
  background-color: #e6e6e6;
}
.incentiv-info-box {
  display: block;
  min-height: 15rem;
  padding: 1rem;
  border: 1px solid #e6e6e6;
  background-color: #f5f5f5;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, 50% top, 90% top, from(#ffffff), to(#e6e6e6));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(left, color-stop(#ffffff 50%), color-stop(#e6e6e6 90%));
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(left, #ffffff 50%, #e6e6e6 90%);
  background: url('backgrounds/blank.gif'), linear-gradient(to right, #ffffff 50%, #e6e6e6 90%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe6e6e6', GradientType=1);
}
.incentiv-info-box,
.incentiv-info-box:hover,
.incentiv-info-box:focus {
  color: #626262;
  text-decoration: none;
}
.incentiv-info-box small {
  font-size: 1.3rem;
}
.incentiv-logo {
  position: absolute;
  margin-right: 1rem;
}
.incentiv-name {
  display: inline-block;
  padding-left: 10rem;
  min-height: 6rem;
  font-size: 2rem;
  line-height: 2.2rem;
}
.incentiv-tutela {
  display: block;
  padding-left: 10rem;
  height: 2.4rem;
  color: #e8501d;
  font-size: 2rem;
  font-weight: 600;
}
.incentiv-times {
  display: block;
  width: 100%;
  padding-left: 10rem;
}
.incentiv-period,
.incentiv-update {
  display: block;
}
.incentiv-label {
  -webkit-border-radius: 1rem;
  -moz-border-radius: 1rem;
  border-radius: 1rem;
  padding: 0.2rem 1.2rem;
}
.incentiv-progress-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 4rem;
  padding: 0.5rem 1.6rem;
  background-color: #e6e6e6;
}
.incentiv-bar-container {
  height: 6px;
  margin-bottom: 6px;
  background: #aaaaaa;
}
.incentiv-bar-orange,
.incentiv-bar-green,
.incentiv-bar-red {
  height: 6px;
  width: 0;
}
.incentiv-bar-red {
  background-color: #e81b1e;
}
.incentiv-bar-orange {
  background-color: #ff8e00;
}
.incentiv-bar-green {
  background-color: #4fae39;
}
.chrome-flash-alert {
  display: none;
  height: 38px;
  line-height: 38px;
  padding: 0 30px;
  background-color: #b2cc28;
  color: white;
  font-size: 18px;
}
.chrome-flash-alert img {
  vertical-align: top;
  border: 0;
}
html.browser-chrome .chrome-flash-alert {
  display: inline-block;
}
body.area-gare h1 {
  text-align: center;
  color: #646464;
  margin: 1rem auto !important;
}
body.area-gare .table-row-green td {
  background-color: #99f299 !important;
}
body.area-gare .table-row-green-light td {
  background-color: #e1ffcd !important;
}
body.area-gare .table-row-orange td {
  background-color: #ffcc00 !important;
}
body.area-gare .table-row-orange-light td {
  background-color: #ffe85b !important;
}
body.area-gare .gare-tutela {
  position: relative;
  display: block;
  padding: 1.2rem 0 0 0 !important;
  width: 10.6rem;
  height: 5.3rem;
  border-top-left-radius: 10.6rem;
  border-top-right-radius: 10.6rem;
  background-color: #f5a019;
  color: white !important;
  text-align: center;
  font-size: 1rem;
  font-weight: 600;
}
body.area-gare .gare-tutela i {
  margin: 0 0.2rem;
  font-size: 1.6rem;
}
body.area-gare header.gare-tutela {
  margin: auto;
}
body.area-gare small.gare-tutela {
  position: absolute;
  left: 0.5rem;
  bottom: 3.5rem;
}
body.area-gare small.gara-update {
  position: absolute;
  display: block;
  right: 1.5rem;
  bottom: 5.5rem;
}
.area-gare-blue .shadow,
.area-gare-green .shadow,
.area-gare-red .shadow {
  -webkit-box-shadow: 4px 4px 2px -2px rgba(138, 138, 138, 0.5);
  -moz-box-shadow: 4px 4px 2px -2px rgba(138, 138, 138, 0.5);
  box-shadow: 4px 4px 2px -2px rgba(138, 138, 138, 0.5);
}
.area-gare-blue .box,
.area-gare-green .box,
.area-gare-red .box,
.area-gare-blue .box-colored,
.area-gare-green .box-colored,
.area-gare-red .box-colored {
  display: block;
  margin: 2rem;
  padding: 1rem;
  background-color: white;
  font-size: 1.8rem;
}
.area-gare-blue .box strong,
.area-gare-green .box strong,
.area-gare-red .box strong,
.area-gare-blue .box-colored strong,
.area-gare-green .box-colored strong,
.area-gare-red .box-colored strong {
  font-weight: bold;
}
.area-gare-blue .box,
.area-gare-green .box,
.area-gare-red .box {
  color: black;
}
.area-gare-blue .box-colored,
.area-gare-green .box-colored,
.area-gare-red .box-colored {
  color: white;
}
.area-gare-blue thead tr th,
.area-gare-green thead tr th,
.area-gare-red thead tr th,
.area-gare-blue tbody,
.area-gare-green tbody,
.area-gare-red tbody {
  background-color: white;
}
.area-gare-blue .box strong {
  color: #6699da;
}
.area-gare-blue .box-colored {
  background-color: #1566be;
}
.area-gare-blue tbody tr:nth-child(even) td {
  background-color: rgba(102, 153, 218, 0.1);
}
.area-gare-green .box strong {
  color: #009999;
}
.area-gare-green .box-colored {
  background-color: #00635c;
}
.area-gare-green tbody tr:nth-child(even) td {
  background-color: rgba(0, 153, 153, 0.1);
}
.area-gare-red .box strong {
  color: #ff716d;
}
.area-gare-red .box-colored {
  background-color: #ff400e;
}
.area-gare-red tbody tr:nth-child(even) td {
  background-color: rgba(255, 113, 109, 0.1);
}
body.area-gest section.docs-grid {
  font-size: 120%;
  color: #626262;
}
body.area-gest section.docs-grid .docs-date {
  font-weight: bold;
}
body.area-gest section.docs-grid .docs-author {
  font-style: italic;
  padding-left: 1rem;
}
body.area-gest section.docs-grid .docs-title {
  font-weight: bold;
}
body.area-gest section.docs-grid .x-grid-row {
  margin: 0.6rem 0;
  padding: 0.6rem;
  border-left: 0.6rem solid #c7d300;
  background-color: #e6e6e6;
}
body.area-gest .x-btn.x-green-light {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #c7d300;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#c7d300), to(#c7d300));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #c7d300 0%, #c7d300 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #c7d300 0%, #c7d300 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #c7d300 0%, #c7d300 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffc7d300', endColorstr='#ffc7d300', GradientType=0);
  border-color: #c7d300 #c7d300 #7f8600;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: #c7d300;
}
body.area-gest .x-btn.x-green-light:hover,
body.area-gest .x-btn.x-green-light:focus,
body.area-gest .x-btn.x-green-light:active,
body.area-gest .x-btn.x-green-light.x-active,
body.area-gest .x-btn.x-green-light.x-disabled,
body.area-gest .x-btn.x-green-light[disabled] {
  color: #fff;
  background-color: #c7d300;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
body.area-news section.docs-grid {
  font-size: 120%;
  color: #626262;
}
body.area-news section.docs-grid .docs-date {
  font-weight: bold;
}
body.area-news section.docs-grid .docs-author {
  font-style: italic;
  padding-left: 1rem;
}
body.area-news section.docs-grid .docs-title {
  font-weight: bold;
}
body.area-news section.docs-grid .x-grid-row {
  margin: 0.6rem 0;
  padding: 0.6rem;
  border-left: 0.6rem solid #952456;
  background-color: #e6e6e6;
}
body.area-news .btn-download {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #952456;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#952456), to(#952456));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #952456 0%, #952456 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #952456 0%, #952456 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #952456 0%, #952456 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff952456', endColorstr='#ff952456', GradientType=0);
  border-color: #952456 #952456 #571532;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: #952456;
}
body.area-news .btn-download:hover,
body.area-news .btn-download:focus,
body.area-news .btn-download:active,
body.area-news .btn-download.x-active,
body.area-news .btn-download.x-disabled,
body.area-news .btn-download[disabled] {
  color: #fff;
  background-color: #952456;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
body.area-download section.docs-grid {
  font-size: 120%;
  color: #626262;
}
body.area-download section.docs-grid .docs-date {
  font-weight: bold;
}
body.area-download section.docs-grid .docs-author {
  font-style: italic;
  padding-left: 1rem;
}
body.area-download section.docs-grid .docs-title {
  font-weight: bold;
}
body.area-download section.docs-grid .x-grid-row {
  margin: 0.6rem 0;
  padding: 0.6rem;
  border-left: 0.6rem solid #003366;
  background-color: #e6e6e6;
}
body.area-download .btn-download {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #003366;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#003366), to(#003366));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #003366 0%, #003366 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #003366 0%, #003366 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #003366 0%, #003366 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff003366', endColorstr='#ff003366', GradientType=0);
  border-color: #003366 #003366 #000d1a;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: #003366;
}
body.area-download .btn-download:hover,
body.area-download .btn-download:focus,
body.area-download .btn-download:active,
body.area-download .btn-download.x-active,
body.area-download .btn-download.x-disabled,
body.area-download .btn-download[disabled] {
  color: #fff;
  background-color: #003366;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
body.area-iniz-comm section.docs-grid {
  font-size: 120%;
  color: #626262;
}
body.area-iniz-comm section.docs-grid .docs-date {
  font-weight: bold;
}
body.area-iniz-comm section.docs-grid .docs-author {
  font-style: italic;
  padding-left: 1rem;
}
body.area-iniz-comm section.docs-grid .docs-title {
  font-weight: bold;
}
body.area-iniz-comm section.docs-grid .x-grid-row {
  margin: 0.6rem 0;
  padding: 0.6rem;
  border-left: 0.6rem solid #918AD3;
  background-color: #e6e6e6;
}
body.area-iniz-comm .btn-download {
  color: #fff;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  background-color: #918ad3;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#918AD3), to(#918AD3));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #918AD3 0%, #918AD3 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #918AD3 0%, #918AD3 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #918AD3 0%, #918AD3 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff918ad3', endColorstr='#ff918ad3', GradientType=0);
  border-color: #918AD3 #918AD3 #5d52be;
  border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
  border: #918AD3;
}
body.area-iniz-comm .btn-download:hover,
body.area-iniz-comm .btn-download:focus,
body.area-iniz-comm .btn-download:active,
body.area-iniz-comm .btn-download.x-active,
body.area-iniz-comm .btn-download.x-disabled,
body.area-iniz-comm .btn-download[disabled] {
  color: #fff;
  background-color: #918AD3;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}
.app-ivass .x-nav-pills.index {
  position: fixed;
}
.app-ivass .x-nav-pills.index li {
  display: block;
  float: none;
  margin-bottom: 2px;
}
.app-ivass .popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.42857143;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 14px;
  background-color: #ffffff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.app-ivass .popover.top {
  margin-top: -10px;
}
.app-ivass .popover.right {
  margin-left: 10px;
}
.app-ivass .popover.bottom {
  margin-top: 10px;
}
.app-ivass .popover.left {
  margin-left: -10px;
}
.app-ivass .popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 14px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0;
}
.app-ivass .popover-content {
  padding: 9px 14px;
}
.app-ivass .popover > .arrow,
.app-ivass .popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.app-ivass .popover > .arrow {
  border-width: 11px;
}
.app-ivass .popover > .arrow:after {
  border-width: 10px;
  content: "";
}
.app-ivass .popover.top > .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}
.app-ivass .popover.top > .arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #ffffff;
}
.app-ivass .popover.right > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.app-ivass .popover.right > .arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #ffffff;
}
.app-ivass .popover.bottom > .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.app-ivass .popover.bottom > .arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #ffffff;
}
.app-ivass .popover.left > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.app-ivass .popover.left > .arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #ffffff;
  bottom: -10px;
}
.app-ivass .banche-drop-files {
  border: 2px dashed #ccc;
  text-align: center;
  padding: 16px;
}
.app-ivass .drop-files-hover {
  border: 2px dashed #3498db;
  opacity: 0.5;
}
.app-ivass .upload-title {
  display: block;
  margin-bottom: 8px;
  font-size: 120%;
}
.app-ivass .text-center {
  text-align: center;
}
.app-ivass .mv {
  margin: 16px auto;
}
.app-avanzamenti .big-number {
  font-size: 16px;
}
.app-avanzamenti .increase-green {
  color: #006a53;
}
.app-avanzamenti .increase-red {
  color: #6A221E;
}
.app-avanzamenti .avanzamenti-table {
  margin-top: 16px;
}
.app-avanzamenti .avanzamenti-table td.big-number {
  text-align: right;
}
.app-avanzamenti .avanzamenti-table th.title {
  text-align: center;
  font-size: 18px;
  color: #006a53;
}
.avanzamenti-table th {
  font-weight: bold!important;
  color: #4F4F4F !important;
  background: transparent!important;
  text-align: center!important;
  font-size: 1.2em !important;
}
.avanzamenti-table tbody td {
  background: #F2F2F2 !important;
}
.avanzamenti-table tbody tr td:first-of-type {
  font-weight: bold!important;
}
.avanzamenti-table tbody tr td.br-15 {
  border-right: 15px solid #ffffff !important;
}
.avanzamenti-table td {
  padding: 15px 30px!important;
  font-size: 1.2em !important;
}
.app-avanzamenti .objective-table td,
.app-avanzamenti .objective-table th {
  text-align: center;
}
.app-avanzamenti .bigger {
  font-size: 16px;
}
.app-avanzamenti .margin {
  margin: 16px auto;
}
.app-avanzamenti .section-title {
  height: 27px;
  line-height: 27px;
}
.app-avanzamenti .visible-canvas {
  visibility: visible;
}
.app-avanzamenti .invisible-canvas {
  visibility: hidden;
}
.avanzamenti-alert {
  background-color: #D1EAF5;
  display: flex;
  align-items: center;
  padding: 20px;
}
.avanzamenti-alert .alert-iconbox i {
  font-size: 45px;
  color: #008DC6;
}
.avanzamenti-alert .alert-message {
  padding-left: 15px;
}
.avanzamenti-alert .alert-message h3 {
  font-size: 16px;
  color: #008DC6;
}
.avanzamenti-alert .alert-message ul {
  padding-inline-start: 20px;
}
.avanzamenti-toggle {
  background-color: #008DC6;
  color: #ffffff;
  font-size: 25px;
  font-weight: 300;
  display: flex;
  justify-content: space-between;
  padding: 15px 25px;
  cursor: pointer;
}
.app-cruscotti .circle {
  border-radius: 50%;
  width: 20px;
  height: 20px;
  color: #fff;
}
.app-cruscotti .circle-red {
  background-color: #9d261d;
  color: #fff;
}
.app-cruscotti .circle-orange {
  background-color: #f89406;
  color: #fff;
}
.app-cruscotti .circle-green {
  background-color: #46a546;
  color: #fff;
}
.app-cruscotti .mtop {
  margin-top: 30px !important;
}
.app-cruscotti .x-btn.inline {
  line-height: 21px;
}
.app-cruscotti .cruscotti-popup-container {
  position: fixed;
  width: 100%;
  z-index: 100;
}
.app-cruscotti .x-table tbody td.kpi-value:hover {
  background-color: #c7d300;
  color: #333;
  cursor: pointer;
}
.app-cruscotti .cruscotti-popup {
  position: relative;
  top: -100px;
  left: 40%;
  width: 30%;
  background-color: #f7f7f7;
  border: 1px solid #f0f0f0;
  z-index: 100;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.app-cruscotti .cruscotti-popup .content {
  padding: 1.5em;
}
.app-cruscotti .cruscotti-popup .value {
  font-size: 130%;
}
.app-cruscotti .cruscotti-popup fieldset legend {
  margin-bottom: 0;
}
.app-cruscotti .progress-bar-container {
  padding: 2px;
  background-color: #fff;
  border: 1px solid #f3f3f3;
}
.app-cruscotti .progress-bar {
  text-align: center;
}
.app-cruscotti h5 {
  font-weight: normal;
}
.app-cruscotti .formula {
  font-style: italic;
  font-size: 130%;
  font-family: 'Courier New';
}
.app-cruscotti .table-thresholds th,
.app-cruscotti .table-thresholds td {
  text-align: center;
}
.app-accordo2 {
  border: 0 !important;
  background-color: transparent;
}
.app-accordo2 #x-nav-top > ul > li.app-accordo2 > a {
  font-weight: bold;
  color: #c7d300;
}
.app-accordo2 #x-nav-top {
  border-color: #c7d300;
}
.app-accordo2 .x-panel {
  border: 1px solid #c7d300;
}
.app-accordo2 .x-panel > header {
  background-color: #c7d300;
  background-image: url('backgrounds/blank.gif');
  background: url('backgrounds/blank.gif'), -webkit-gradient(linear, left 0%, left 100%, from(#c7d300), to(#c7d300));
  background: url('backgrounds/blank.gif'), -webkit-linear-gradient(top, #c7d300 0%, #c7d300 100%);
  background: url('backgrounds/blank.gif'), -moz-linear-gradient(top, #c7d300 0%, #c7d300 100%);
  background: url('backgrounds/blank.gif'), linear-gradient(to bottom, #c7d300 0%, #c7d300 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffc7d300', endColorstr='#ffc7d300', GradientType=0);
}
.app-accordo2 .x-nav-tabs {
  border-bottom: 1px solid #c7d300;
}
.app-accordo2 .x-nav-tabs > li > a {
  padding: 1rem 2rem !important;
  background-color: #EEF0BC;
  color: #00674F !important;
  font-size: 24px !important;
  font-weight: 400 !important;
}
.app-accordo2 .x-nav-tabs > li > a:hover {
  border-color: #555555 #555555 #c7d300;
}
.app-accordo2 .x-nav-tabs > li.x-active > a {
  background-color: #C8D200;
  font-weight: 700 !important;
  color: #00674F;
}
.app-accordo2 .x-nav-tabs > li.x-active > a,
.app-accordo2 .x-nav-tabs > li.x-active > a:hover,
.app-accordo2 .x-nav-tabs > li.x-active > a:focus {
  background-color: #C8D200;
  color: #00674F;
  font-weight: 700 !important;
  border-color: #c7d300 #c7d300 transparent;
}
.app-accordo2 > header {
  background: transparent !important;
  border-bottom: 0 !important;
}
.app-accordo2 > section {
  border-top: 0 !important;
  border-bottom: 0 !important;
}
.app-accordo2 select {
  font-size: 18px;
  font-weight: 700;
  color: #3A3A3A;
  padding: 8px;
  border-radius: 4px;
  border: 0;
  width: auto;
}
.app-accordo2 select.select-sm {
  font-size: 14px;
  padding: 4px;
}
.app-accordo2 .x-btn {
  font-size: 16px;
  font-weight: 700;
  border-radius: 4px;
  padding: 8px 12px !important;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}
.app-accordo2 .x-btn:focus {
  outline: none!important;
}
.app-accordo2 .x-red {
  background: #E8501D !important;
}
.app-accordo2 .x-secondary {
  background: #eee !important;
  color: darkslategray;
}
.app-accordo2 .x-red:hover {
  background: #802b0e !important;
}
.app-accordo2 .x-green {
  background: #00674F;
}
.app-accordo2 .x-btn.x-green.x-disabled {
  background: #00674F;
  opacity: 0.6;
  cursor: not-allowed;
}
.app-accordo2 .summary-counters {
  color: #00674F;
  font-size: 24px;
  padding: 20px;
  display: flex;
  align-items: center;
}
.app-accordo2 .summary-counters > div {
  margin-right: 20px;
}
.app-accordo2 .x-table-container {
  border: 0;
  max-height: 800px;
  overflow: scroll;
}
.app-accordo2 .x-table thead tr th {
  background-color: #EEF0BC;
  color: #3A3A3A;
  font-size: 16px;
  padding: 20px;
  font-weight: 700;
  position: sticky;
  top: 0;
}
.app-accordo2 .x-table.align-right thead tr th {
  text-align: right;
}
.app-accordo2 .x-table tbody tr td {
  color: #3A3A3A;
  font-size: 16px;
  padding: 10px 15px;
  background-color: #FFFFFF;
}
.app-accordo2 .x-table.align-right tbody tr td {
  text-align: right;
}
.app-accordo2 .x-table tbody tr:nth-of-type(even) td {
  background-color: #FAFAEF;
}
.app-accordo2 .x-table tfoot tr td {
  color: #3A3A3A;
  font-size: 16px;
  padding: 10px 15px;
  font-weight: 700;
  background-color: #EEF0BC;
}
.app-accordo2 .x-table.align-right tfoot tr td {
  text-align: right;
}
.app-accordo2 .section-header {
  color: #00674F;
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  text-transform: uppercase;
  border-top: 4px solid white;
  border-bottom: 4px solid white;
}
.app-accordo2 .grid-personalizzato {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, auto);
  grid-template-rows: repeat(auto-fill, 1fr);
  grid-row-gap: 0;
  grid-column-gap: 0;
}
.app-accordo2 .grid-personalizzato .grid-header {
  justify-content: center;
  font-size: 16px;
}
.app-accordo2 .grid-personalizzato > div {
  border-bottom: 4px solid #FFFFFF;
  display: flex;
  align-items: center;
  padding: 10px 5px;
}
.app-accordo2 .grid-incassi {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(auto-fill, 1fr);
  grid-row-gap: 0;
  grid-column-gap: 0;
}
.app-accordo2 .grid-incassi .grid-header {
  justify-content: center;
  font-size: 16px;
}
.app-accordo2 .grid-incassi > div {
  border-bottom: 4px solid #FFFFFF;
  display: flex;
  align-items: center;
  padding: 10px 5px;
}
.app-accordo2 .grid-incassi .row-header {
  color: #00674F;
  font-size: 24px;
  font-weight: 700;
  text-transform: uppercase;
}
.app-accordo2 .grid-incassi .double-column {
  grid-column: 2 / span 2;
  justify-content: center;
}
.app-accordo2 .grid-incassi .double-column > div {
  font-size: 18px;
  font-weight: 700;
  padding: 10px;
}
.app-accordo2 input {
  font-size: 18px;
  font-weight: 700;
  color: #3A3A3A;
  padding: 8px;
  border-radius: 4px;
  text-align: center;
  border: 0;
  width: 100%;
}
.app-accordo2 input.invalid {
  border: 1px solid #E8501D;
  color: #E8501D;
}
.app-accordo2 input:read-only {
  background-color: #EEF0BC;
}
.app-accordo2 textarea {
  border-radius: 8px;
  padding: 20px;
  font-size: 20px;
  color: #3A3A3A;
  border: 0;
}
.app-accordo2 .agency-info {
  font-size: 16px;
  color: #3A3A3A;
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr 1fr 1fr 1fr;
  gap: 15px 10px;
  grid-template-areas: ". ." ". ." ". ." ". ." ". ." ". .";
}
.app-accordo2 .agency-info > div:nth-child(odd) {
  text-align: right;
}
.app-accordo2 .agency-info > div:nth-child(even) {
  font-weight: 700;
}
.app-accordo2 .section-subtitle {
  color: #00674F;
  font-size: 20px;
  font-weight: 700;
  padding: 20px 0;
}
.app-accordo2 .table-section > div {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.app-accordo2 .table-section .row-label {
  font-size: 16px;
  color: #3A3A3A;
}
.app-accordo2 .table-section .row-value {
  text-align: right;
}
.app-accordo2 .table-section .row-value input {
  width: 95px;
  text-align: right;
}
.app-accordo2 .radio-container {
  display: inline-flex;
  align-items: center;
  margin-bottom: 10px;
  margin-right: 30px;
}
.app-accordo2 .radio-container label {
  margin: 0;
  padding: 0;
  font-size: 20px;
  font-weight: 700;
  text-transform: uppercase;
}
.app-accordo2 .radio-container input[type="radio"] {
  margin: 0 0 0 10px;
  padding: 0;
  -ms-transform: scale(1.5);
  /* IE 9 */
  -webkit-transform: scale(1.5);
  /* Chrome, Safari, Opera */
  transform: scale(1.5);
}
.app-accordo2 .feed-container.AMMINISTRATORE {
  padding-right: 120px;
}
.app-accordo2 .feed-container.MANAGER {
  padding-left: 120px;
}
.app-accordo2 .info-panel {
  background-color: #FFFFFF;
  border-radius: 8px;
  font-size: 14px;
  padding: 20px;
  margin-top: 20px;
  color: #00674F;
  position: relative;
}
.app-accordo2 .info-panel .close {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 16px;
}
.app-accordo2 .info-panel tr.highlight {
  font-weight: 700;
}
.app-accordo2 .info-panel tr.highlight td {
  border: 2px solid #00c89e;
}
.app-accordo2 .info-panel th {
  font-weight: 700;
}
.app-accordo2 .info-panel th,
.app-accordo2 .info-panel td {
  padding: 10px;
}
.app-accordo2 .info-panel td {
  border: 1px solid #00c89e;
}
.app-accordo2 .not-allowed {
  cursor: not-allowed;
}
.inc2-header i {
  background-color: #e8501d !important;
  font-size: 2.2rem;
}
body > header ul li a {
  display: block;
  background: transparent;
  /*border-radius: 0.25em;*/
  color: #969696;
  /*font-weight: 100;*/
}
body > header ul li a:hover {
  color: #f90;
  /* TBD */
  text-decoration: none;
}
.light-grey {
  color: #626262;
}
.hidden {
  display: none !important;
}
body > header ul {
  margin-top: 10px;
}
.x-btn.x-medium {
  padding: 0.2em 0.6em;
}
.x-table tbody > tr.x-row-selected td {
  background-color: #b5d39d !important;
}
.pull-left {
  float: left;
}
.x-groupama {
  background-color: #006a53;
  color: #fff;
}
.x-groupama:hover {
  color: #006a53;
}
