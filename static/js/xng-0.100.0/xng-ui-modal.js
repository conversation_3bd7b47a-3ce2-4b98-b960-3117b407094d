(function(ng, module) {

	module.run(['$document', '$rootScope', 'xng.ui.modal.Stack', function($document, $rootScope, Stack) {
		// bind ESC key
		$document.bind('keydown', function(evt) {
			var modal;
			if(evt.which === 27) {
				modal = windowsStack.top();
				if(modal && modal.value.keyboard) {
					$rootScope.$apply(function () {
						Stack.dismiss(modal.key);
					});
				}
			}
		});
	}]);

	module.provider('xng.ui.$modal', function() {
		var defaultOptions = {
			backdrop: true, //can be also false or 'static'
			keyboard: true,
			delay: 300
		};

		return {
			setOptions: function(options) {
				defaultOptions = options;
			},
			$get: ['$injector', '$rootScope', '$q', '$http', '$templateCache', '$controller', 'xng.ui.modal.Stack', function($injector, $rootScope, $q, $http, $templateCache, $controller, Stack) {
				function getTemplatePromise(options) {
					return options.template ? $q.when(options.template) :
						$http.get(options.templateUrl, {cache: $templateCache}).then(function (result) {
							return result.data;
						});
				}
				function getResolvePromises(resolves) {
					var promisesArr = [];
					ng.forEach(resolves, function(value) {
						if (ng.isFunction(value) || ng.isArray(value)) {
							promisesArr.push($q.when($injector.invoke(value)));
						}
					});
					return promisesArr;
				}
				return {
					open: function(options) {
						var modalResultDeferred = $q.defer();
						var modalOpenedDeferred = $q.defer();
						//prepare an instance of a modal to be injected into controllers and returned to a caller
						var modalInstance = {
							result: modalResultDeferred.promise,
							opened: modalOpenedDeferred.promise,
							close: function(result) {
								Stack.close(modalInstance, result);
							},
							dismiss: function(reason) {
								Stack.dismiss(modalInstance, reason);
							}
						};
						//merge and clean up options
						options = ng.extend({}, defaultOptions, options);
						options.resolve = options.resolve || {};
						//verify options
						if (!options.template && !options.templateUrl) {
							throw new Error('One of template or templateUrl options is required.');
						}
						var templateAndResolvePromise = $q.all([getTemplatePromise(options)].concat(getResolvePromises(options.resolve)));
						templateAndResolvePromise.then(
							function resolveSuccess(tplAndVars) {
								var modalScope = (options.scope || $rootScope).$new();
								modalScope.$close = modalInstance.close;
								modalScope.$dismiss = modalInstance.dismiss;
								var ctrlLocals = {};
								var resolveIter = 1;
								//controllers
								if (options.controller) {
									ctrlLocals.$scope = modalScope;
									ctrlLocals.$modalInstance = modalInstance;
									ng.forEach(options.resolve, function (value, key) {
										ctrlLocals[key] = tplAndVars[resolveIter++];
									});
									$controller(options.controller, ctrlLocals);
								}
								Stack.open(modalInstance, {
									scope: modalScope,
									deferred: modalResultDeferred,
									content: tplAndVars[0],
									backdrop: options.backdrop,
									keyboard: options.keyboard,
									windowClass: options.windowClass
								});
							},
							function resolveError(reason) {
								modalResultDeferred.reject(reason);
							});
						templateAndResolvePromise.then(
							function () {
								modalOpenedDeferred.resolve(true);
							},
							function () {
								modalOpenedDeferred.reject(false);
							});
						return modalInstance;
					}
				}
			}]
		}
	});

	module.factory('xng.ui.modal.Stack', ['$document', '$compile', '$rootScope', function($document, $compile, $rootScope) {
		var backdropjqLiteEl, backdropDomEl;
		var backdropScope = $rootScope.$new(true);
		var body = $document.find('body').eq(0);
		function backdropIndex() {
			var topBackdropIndex = -1;
			var opened = windowsStack.keys();
			for (var i = 0; i < opened.length; i++) {
				if (windowsStack.get(opened[i]).value.backdrop) {
					topBackdropIndex = i;
				}
			}
			return topBackdropIndex;
		}
		$rootScope.$watch(backdropIndex, function(newBackdropIndex){
			backdropScope.index = newBackdropIndex;
		});
		function removeModalWindow(modalInstance) {
			var modalWindow = windowsStack.get(modalInstance).value;
			//clean up the stack
			windowsStack.remove(modalInstance);
			//remove window DOM element
			modalWindow.modalDomEl.remove();
			//remove backdrop if no longer needed
			if (backdropDomEl && backdropIndex() == -1) {
				backdropDomEl.remove();
				backdropDomEl = undefined;
			}
			//destroy scope
			modalWindow.modalScope.$destroy();
		}
		return {
			open: function(modalInstance, modal) {
				windowsStack.add(modalInstance, {
					deferred: modal.deferred,
					modalScope: modal.scope,
					backdrop: modal.backdrop,
					keyboard: modal.keyboard
				});
				var angularDomEl = ng.element('<div xng-modal-window></div>');
				angularDomEl.attr('window-class', modal.windowClass);
				angularDomEl.attr('index', windowsStack.length() - 1);
				angularDomEl.html(modal.content);
				var modalDomEl = $compile(angularDomEl)(modal.scope);
				windowsStack.top().value.modalDomEl = modalDomEl;
				body.append(modalDomEl);
				if (backdropIndex() >= 0 && !backdropDomEl) {
					backdropjqLiteEl = ng.element('<div xng-modal-backdrop></div>');
					backdropDomEl = $compile(backdropjqLiteEl)(backdropScope);
					body.append(backdropDomEl);
				}
			},
			close: function(modalInstance, result) {
				var modal = windowsStack.get(modalInstance);
				if (modal) {
					modal.value.deferred.resolve(result);
					removeModalWindow(modalInstance);
				}
			},
			dismiss: function(modalInstance, reason) {
				var modalWindow = windowsStack.get(modalInstance).value;
				if (modalWindow) {
					modalWindow.deferred.reject(reason);
					removeModalWindow(modalInstance);
				}
			},
			getTop: function() {
				return windowsStack.top();
			}
		}
	}]);

	/**
	 * A helper, internal data structure that acts as a map,
	 * but also allows getting / removing elements in the LIFO order.
	 */
	var windowsStack = new function() {
		var stack = [];
		return {
			add: function(key, value) {
				stack.push({
					key: key,
					value: value
				});
			},
			get: function(key) {
				for (var i = 0; i < stack.length; i++) {
					if (key == stack[i].key) {
						return stack[i];
					}
				}
			},
			keys: function() {
				var keys = [];
				for (var i = 0; i < stack.length; i++) {
					keys.push(stack[i].key);
				}
				return keys;
			},
			top: function() {
				return stack[stack.length - 1];
			},
			remove: function(key) {
				var idx = -1;
				for (var i = 0; i < stack.length; i++) {
					if (key == stack[i].key) {
						idx = i;
						break;
					}
				}
				return stack.splice(idx, 1)[0];
			},
			/*removeTop: function() {
				return stack.splice(stack.length - 1, 1)[0];
			},*/
			length: function() {
				return stack.length;
			}
		};
	};

	module.directive('xngModalBackdrop', ['$timeout', function($timeout) {
		return {
			restrict: 'EA',
			replace: true,
			template: '<div class="x-modal-backdrop fade" ng-class="{in: animate}" ng-style="{\'z-index\': 1050+index}"></div>',
			link: function(scope) {
				//trigger CSS transitions
				$timeout(function () {
					scope.animate = true;
				}, 200);
			}
		};
	}]);

	module.directive('xngModalWindow', ['$timeout', 'xng.ui.modal.Stack', function($timeout, Stack) {
		return {
			restrict: 'EA',
			scope: {
				index: '@'
			},
			replace: true,
			transclude: true,
			template: '<div ng-click="close($event)" class="x-modal {{ windowClass }}" ng-class="{in: animate}"><div class="x-modal-dialog"><div class="x-modal-content" ng-transclude></div></div></div>',
			link: function (scope, element, attributes) {
				scope.windowClass = attributes.windowClass || '';
				scope.close = function(evt) {
					var modal = Stack.getTop();
					if (!evt.target.closest('.x-modal-content') && modal && modal.value.backdrop && modal.value.backdrop != 'static') {
						evt.preventDefault();
						evt.stopPropagation();
						Stack.dismiss(modal.key, 'backdrop click');
					}
				};
				//trigger CSS transitions
				$timeout(function () {
					scope.animate = true;
				}, 200);
			}
		};
	}]);

})(angular, angular.module('xng.ui.modal', []));
