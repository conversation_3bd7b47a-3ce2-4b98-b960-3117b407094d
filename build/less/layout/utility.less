// Floats
// --------------------------------------------------

.x-clearfix {
	.clear-fix();
}
.x-center-block {
	.center-block();
}
.x-pull-right {
	float: right !important;
}
.x-pull-left {
	float: left !important;
}


// Flex layouts
// --------------------------------------------------

.x-flex-hbox {
	.flex-hbox(center, stretch);
}
.x-flex-vbox {
	.flex-vbox(center, stretch);
}
.x-flex-1 { .flex-element-flex-important(1); }
.x-flex-2 { .flex-element-flex-important(2); }
.x-flex-3 { .flex-element-flex-important(3); }
.x-flex-4 { .flex-element-flex-important(4); }
.x-flex-5 { .flex-element-flex-important(5); }
.x-flex-6 { .flex-element-flex-important(6); }
.x-flex-7 { .flex-element-flex-important(7); }
.x-flex-8 { .flex-element-flex-important(8); }


// Show / Hide content
// --------------------------------------------------

.x-hide {
	display: none !important;
}
.x-block {
	display: block !important;
}
.x-inline {
	display: inline-block !important;
}
@media (max-width: @breakpoint-xs) {
	.x-show-xs				{ display: block; }
	.x-show-inline-xs		{ display: inline !important; }
	.x-hide-xs				{ display: none !important; }
}
@media (min-width: (@breakpoint-xs+0.1)) and (max-width: @breakpoint-sm) {
	.x-show-sm				{ display: block; }
	.x-show-inline-sm		{ display: inline; }
	.x-hide-sm				{ display: none !important; }
}
@media (min-width: (@breakpoint-sm+0.1)) and (max-width: @breakpoint-md) {
	.x-show-md				{ display: block; }
	.x-show-inline-md		{ display: inline; }
	.x-hide-md				{ display: none !important; }
}
@media (min-width: (@breakpoint-md+0.1)) and (max-width: @breakpoint-lg) {
	.x-show-lg				{ display: block; }
	.x-show-inline-lg		{ display: inline; }
	.x-hide-lg				{ display: none !important; }
}
@media (min-width: (@breakpoint-lg+0.1)) {
	.x-show-xl				{ display: block; }
	.x-show-inline-xl		{ display: inline; }
	.x-hide-xl				{ display: none !important; }
}


// Backgrounds
// --------------------------------------------------

.x-bg-cover-fixed {
	background-size: cover;
	background-attachment: fixed;
	background-repeat: no-repeat;
	background-position: center center;
}
.x-bg-cover-scrolling {
	background-size: cover;
	background-attachment: scroll;
	background-repeat: no-repeat;
	background-position: center center;
}


// Overflow
// --------------------------------------------------

.x-overflow-y {
	overflow-y: auto;
}
.x-overflow-x {
	overflow-x: auto;
}

// Dimensions
// --------------------------------------------------

.x-width-100 { width: 100%; }
.x-height-100 { height: 100%; }

